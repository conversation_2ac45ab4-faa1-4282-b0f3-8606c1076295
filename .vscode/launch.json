{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {"name":"Python Debugger: Current File with Arguments","type":"debugpy","request":"launch","program":"${file}","console":"integratedTerminal",
        "args": [ "roce", "/home/<USER>/POC/csg-htsim/sim/datacenter/connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm", "128", "128", "700", "allreduce" ] },
        

        {
            "name": "(gdb) Launch",
            "type": "cppdbg",
            "request": "launch",
            // "program": "/home/<USER>/POC/csg-htsim/sim/datacenter/htsim_roce",
            // "args": [
            //     "-topo", "topologies/leaf_spine_lior.topo",
            //     "-strat", "ecmp_host",
            //     "-paths", "1",
            //     "-nodes", "16",
            //     "-conns", "15",
            //     "-mtu", "3000",
            //     "-log", "sink",
            //     "-end", "30000",
            //     "-tm", "connection_matrices/15mix.cm",
            //     "-q", "0",
            //     "-pfc_thresholds", "1", "2"
            //   ],   


            // "program": "/home/<USER>/POC/csg-htsim/sim/datacenter/htsim_roce",
            // "args": [
            //  //   "-topo", "topologies/leaf_spine_lior.topo",
            //     "-topo", "topologies/fat_tree_16.topo",
            //  //   "-strat", "ecmp_host_ar","5",
            //     "-strat", "ecmp_ar",
            //     "-ar_sticky_delta", "5",
            //  //   "-strat", "ecmp_host",
            //     "-paths", "1",
            //     "-nodes", "16",
            //     "-conns", "12",
            //     "-mtu", "3000",
            //     "-log", "traffic",
            //     "-end", "500000",
            // //  "-tm", "connection_matrices/incast16/all2all_16.cm",
            //     "-tm", "connection_matrices/incast16/incast_rand_flowsize_incastratio_3_seed7.cm",
            // //    "-tm", "connection_matrices/6.cm",
            //     "-q", "30",
            //     "-pfc_thresholds", "2", "3",
            //     "-pfc_thresholds_1", "2", "3",
            //     "-spine_pfc_thresholds", "2", "3",
            //  //   "-ecn_thresholds", "30", "31",
            //  //   "-queue_type", "composite_ecn",
            //     "-roce_cfg", "roce_config_file.txt",
            //     "-seed", "0",
            //     "-seed_path", "2"
            // ],   


            "program": "/home/<USER>/POC/csg-htsim/sim/datacenter/htsim_ndp",
            "args": [
             //   "-topo", "topologies/leaf_spine_lior.topo",
                "-topo", "topologies/fat_tree_128.topo",
             //   "-strat", "ecmp_host_ar","5",
                "-strat", "ecmp_ar",
                "-ar_sticky_delta", "5",
             //   "-strat", "ecmp_host",
             //   "-paths", "1",
                "-nodes", "128",
                "-conns", "96",
                "-mtu", "3000",
                "-log", "traffic",
                "-end", "500000",
            //  "-tm", "connection_matrices/incast16/all2all_16.cm",
            //    "-tm", "connection_matrices/incast16/incast_rand_flowsize_incastratio_3_seed7.cm",
                "-tm", "connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed100.cm",
            //    "-tm", "connection_matrices/6.cm",
                "-q", "30",
                "-pfc_thresholds", "1", "2",
                // "-pfc_thresholds_1", "2", "3",
             //   "-spine_pfc_thresholds", "2", "3",
             //   "-ecn_thresholds", "30", "31",
                "-queue_type", "lossless_input",
                "-ar_granularity", "packet",
                "-cwnd", "50",
             // "-roce_cfg", "roce_config_file.txt",
             // "-seed", "0",
             // "-seed_path", "2"
            ],   

            // "program": "/home/<USER>/POC/csg-htsim/sim/datacenter/htsim_roce",
            // "args": [
            //     "-topo", "topologies/fat_tree_64.topo",
            //     "-strat", "ecmp_host",
            //     "-paths", "1",
            //     "-nodes", "64",
            //     "-conns", "57",
            //     "-mtu", "3000",
            //     "-log", "sink",
            //     "-end", "50000",
            //     "-tm", "connection_matrices/incast64/incast_rand_flowsize_incastratio_8_seed51.cm",
            //     "-q", "50",
            //     "-pfc_thresholds", "8", "9",
            //     "-seed", "51"
            //   ],   

            // "program": "/home/<USER>/POC/csg-htsim/sim/datacenter/htsim_ndp",
            // "args": [
            //     "-topo", "topologies/fat_tree_128.topo",
            //     "-strat", "ecmp_host_ecn",
            //     "-ecn_thresh", "0.1",
            //     "-paths", "8",
            //     "-nodes", "128",
            //     "-conns", "127",
            //     "-mtu", "3000",
            //     "-log", "sink",
            //     "-end", "30000",
            //     "-tm", "connection_matrices/incast_128.cm",
            //     "-q", "90",
            //     "-cwnd", "50"
            //   ],   


            "stopAtEntry": false,
            "cwd": "/home/<USER>/POC/csg-htsim/sim/datacenter/",
            "environment": [],
            "externalConsole": false,
            "MIMode": "gdb",
            "setupCommands": [
                {
                    "description": "Enable pretty-printing for gdb",
                    "text": "-enable-pretty-printing",
                    "ignoreFailures": true
                },
                {
                    "description": "Set Disassembly Flavor to Intel",
                    "text": "-gdb-set disassembly-flavor intel",
                    "ignoreFailures": true
                }
            ]
        }
      

    ]
}