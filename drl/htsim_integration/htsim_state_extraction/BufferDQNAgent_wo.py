
import numpy as np
import torch
import torch.optim as optim
from FileModel_wo import FileModel  # Import the custom model for the file-based environment
from ReplayBuffer_wo import *
from logger_wo import Logger
import torch.nn.functional as F 
from RNNReplayBuffer import *
from CompressedReplayBuffer import CompressedRNNReplayBuffer
import torch
import torch.nn.functional as F
import numpy as np
import StateFeatureExtractor as SFE
# or normal ReplayBuffer

import zipfile
import os
import torch
import zipfile
import os
import io
import tempfile
torch.backends.cudnn.benchmark = False
torch.backends.cudnn.enabled = True
torch.autograd.set_detect_anomaly(True)  # Optional: helps with tracing NaNs in backward

import torch.cuda.amp as amp
# actOne = np.array([[5,6,8])
action_space_un700_705=np.array([[7, 7, 7], [7, 5, 5],  [13, 8, 8], [12, 9, 9],
              [5, 6, 6],  [9, 7, 7], [5, 8, 8], [14, 6, 6], 
              [10, 6, 6], [12, 6, 6], [11, 9, 9], [6, 5, 5], [14, 8, 8],
                [6, 7, 7], [10, 8, 8], [9, 9, 9], [7, 9, 9],  
                [12, 8, 8],  [7, 6, 6], [8, 7, 7], [11, 6, 6], 
                [8, 5, 5], [9, 6, 6], [6, 9, 9], [11, 8, 8], [9, 8, 8], 
                [13, 5, 5], [13, 7, 7], [8, 9, 9], [6, 6, 6], [5, 5, 5],
                   [5, 7, 7], [6, 8, 8], [11, 5, 5], [14, 5, 5],
                    [10, 5, 5], [8, 6, 6], [13, 9, 9], [14, 7, 7], [10, 7, 7],
                      [12, 5, 5], [12, 7, 7], 
              [13, 6, 6], [14, 9, 9], [10, 9, 9], [11, 7, 7], [9, 5, 5]])

ACIONS=np.array([[9, 9, 45], [3, 3, 30], [7, 7, 30], [5, 5, 40], [1, 1, 25], [3, 3, 45], [7, 7, 45], [1, 1, 40], 
        [2, 2, 20], [9, 9, 20], [4, 4, 25], [2, 2, 35], [9, 9, 35], [3, 3, 20], [4, 4, 40], [7, 7, 20],
          [5, 5, 30], [3, 3, 35], [7, 7, 35], [5, 5, 45], [1, 1, 30], [1, 1, 45], [2, 2, 25], [9, 9, 25], [4, 4, 30], [2, 2, 40], [5, 5, 20], [9, 9, 40], [3, 3, 25], [2, 2, 45], [4, 4, 45], [7, 7, 25], [5, 5, 35], [1, 1, 20], 
        [3, 3, 40], [7, 7, 40], [1, 1, 35], [4, 4, 20], [2, 2, 30], [9, 9, 30], [4, 4, 35], [5, 5, 25]])
class BufferDQNAgent:  
    def __init__(self, env, config):
        """
        Initialize the DQN agent using Q(state, action) architecture for dynamic action support.
        """
        self.class_name = self.__class__.__name__
        self.config = config
        self.training_step = 0
        self.reward = -1
        self.logger_agent = Logger("BufferDQNAgent", self.config).get_logger()
        self.logger_agent.info("DQN Agent initialized.")

        self.action_type = config["file_agent_incast"]["action_type"]
        self.mode = config["file_env"]["mode"]
        print("agent mode", self.mode)

        # Action space construction
        if self.action_type in ["ar", "pfcs_and_ar", "single", "multiple"]:
            single_cfg = config["file_agent_incast"]["single_action_config"]
            self.q_values = np.array(single_cfg["q_values"], dtype=np.int32)
            self.pfc_thresholds = np.arange(3, 10)
            sticky_delta_tre = np.arange(6,14)
            mesh = np.meshgrid( self.pfc_thresholds, self.pfc_thresholds,sticky_delta_tre)
            self.action_space =  np.vstack([mesh[0].ravel(), mesh[1].ravel(), mesh[2].ravel()]).T
            print("Action space shape:", self.action_space)
        else:
            raise ValueError(f"Unknown action_type: {self.action_type}")

        # Setup device
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(self.device)
        # self.feature_extractor = SFE.StateFeatureExtractor()
        # self.feature_extractor = SFE.StateFeatureExtractor(device=self.device)



        # From config:
        enabled = config["file_agent_incast"]["feature_extractors"]

        # Mapping from config strings to constructor kwargs
        feature_flags = {
            "cnn": "use_cnn",
            "global_basic": "use_global_basic",
            "global_stats": "use_global_stats",
            "fft": "use_fft",
            "edge": "use_edge",
            "entropy": "use_entropy",
            "statistical": "use_statistical",
            "texture": "use_texture"
        }

        # Build kwargs dynamically
        extractor_kwargs = {kwarg: (name in enabled) for name, kwarg in feature_flags.items()}

        # Add the device argument
        extractor_kwargs["device"] = self.device

        # Instantiate
        self.feature_extractor = SFE.StateFeatureExtractor(**extractor_kwargs)

        self.logger_agent.info(f"Feature extractor created with kwargs:{extractor_kwargs}")

        # Model dimensions
        # self.state_dim = self._get_flattened_state_size(env)
        # self.action_dim = self.action_space.shape[0]

        # Dynamic state dimension from extractor
        self.state_dim = self._get_feature_size()
        self.action_dim = self.action_space.shape[0]


        # Initialize model and target model with Q(state, action) architecture
        self.model = FileModel(
            self.config,
            state_dim=self.state_dim,
            action_dim=self.action_dim,
            hidden_sizes=config["file_agent_incast"]["hidden_sizes"],
            dropout_rate=config["file_agent_incast"]["dropout_rate"]
        ).to(self.device)

        self.target_model = FileModel(
            self.config,
            state_dim=self.state_dim,
            action_dim=self.action_dim,
            hidden_sizes=config["file_agent_incast"]["hidden_sizes"],
            dropout_rate=config["file_agent_incast"]["dropout_rate"]
        ).to(self.device)

        self.config = self.config["file_agent_incast"]
        self.optimizer = optim.Adam(self.model.parameters(), lr=self.config['learning_rate'])
        self.training_frequence = self.config['training_frequence']
        self.gamma = self.config['gamma']
        self.epsilon = self.config['epsilon_start']
        self.epsilon_min = self.config['epsilon_min']
        self.epsilon_decay = self.config['epsilon_decay']
        self.batch_size = self.config['batch_size']
        self.action_map = {tuple(a): idx for idx, a in enumerate(self.action_space)}
        self.scaler = torch.cuda.amp.GradScaler()

        # Replay buffer setup
        self.buffer_type = self.config["buffer_type"]
        if self.buffer_type == "uniform":
            self.replay_buffer = ReplayBuffer(self.config["buffer_size"])
            print("Using Uniform Replay Buffer.")

        elif self.buffer_type == "rnn":
            self.replay_buffer = CompressedRNNReplayBuffer(
            buffer_size=self.config["buffer_size"],
            sequence_len=self.config["sequence_len"],
            config=config)
            print("Using RNN-compatible Uniform Replay Buffer.")    
        elif self.buffer_type == "prioritized":
            self.replay_buffer = PrioritizedReplayBuffer(
                size=self.config["buffer_size"],
                alpha=self.config.get("prioritization_alpha", 0.6)
            )
            print("Using Prioritized Replay Buffer.")
        else:
            raise ValueError(f"Invalid buffer type: {self.buffer_type}")

        self.update_target_model()
        self.logger_agent.info("Agent initialized.")


    ## ✅ NEW: feature extraction function
    def extract_features(self, state):
        if isinstance(state, tuple) and len(state) == 3:
            matrix, drop_count, reorder_count = state
            state_np = np.array(matrix, dtype=np.float32)
        elif isinstance(state, (list, np.ndarray)):
            state_np = np.array(state, dtype=np.float32)
        else:
            raise ValueError(f"Unsupported state type: {type(state)}")

        state_tensor = torch.tensor(state_np, dtype=torch.float32, device=self.device)
        with torch.no_grad():
            features = self.feature_extractor(state_tensor).cpu().numpy()
        return features


    ## ✅ Auto detect state size
    def _get_feature_size(self):
        dummy_input = torch.zeros((10, 128, 128), dtype=torch.float32).to(self.device)
        dummy_output = self.feature_extractor(dummy_input)
        return dummy_output.shape[0]


    ## ✅ Fully efficient act() using extracted features
    def act(self, features):
        self.model.eval()
        state_vec = torch.tensor(features, dtype=torch.float32, device=self.device)
        state_seq = state_vec.unsqueeze(0).unsqueeze(0)

        with torch.no_grad():
            q_values = self.model(state_seq)
            best_action_idx = q_values.argmax(dim=1).item()

        selected_action = self.action_space[best_action_idx]

        if self.mode == "train" and np.random.rand() <= self.epsilon:
            idx = np.random.randint(len(self.action_space))
            selected_action = self.action_space[idx]

        if self.mode == "train":
            self.epsilon = max(self.epsilon_min, self.epsilon * self.epsilon_decay)

        return selected_action


    def _get_flattened_state_sizeO(self, state):
        return self._flatten_state(state).size

    def _get_flattened_state_size(self, env):
        dummy_input = torch.zeros((10, 128, 128), dtype=torch.float32)
        dummy_output = self.feature_extractor(dummy_input.to(self.device))
        return dummy_output.shape[0]


    def _flatten_state(self, state):
        """
        Input: state (expected shape: tuple or np.ndarray of (10,128,128))
        Output: extracted feature vector as np array (float32)
        """
        if isinstance(state, tuple) and len(state) == 3:
            matrix, drop_count, reorder_count = state
            state_np = np.array(matrix, dtype=np.float32)
        elif isinstance(state, (list, np.ndarray)):
            state_np = np.array(state, dtype=np.float32)
        else:
            raise ValueError(f"Unsupported state type: {type(state)}")

        state_tensor = torch.tensor(state_np, dtype=torch.float32, device=self.device)
        with torch.no_grad():
            features = self.feature_extractor(state_tensor).cpu().numpy()
        return features



    def _flatten_stateO(self, state):
        self.logger_agent.debug(f"Flatten state - input type: {type(state)}")

        if isinstance(state, tuple):
            if len(state) == 3:
                matrix_list, drop_count, reorder_count = state
                self.logger_agent.debug("🟢 Tuple received — flattening matrix + penalties")
                flat_matrix = np.array(matrix_list).flatten().astype(np.float16)
                extra = np.array([drop_count, reorder_count], dtype=np.float16)
                combined = np.concatenate([flat_matrix, extra])
                self.logger_agent.debug(f"Flattened shape with penalties: {combined.shape}")
                return combined
            else:
                raise ValueError(f"Unsupported tuple length in state: {len(state)}")

        elif isinstance(state, (list, np.ndarray)):
            # Only flatten the matrix
            self.logger_agent.debug("🟡 Raw matrix received — flattening only matrix")
            flat_matrix = np.array(state).flatten().astype(np.float16)
            self.logger_agent.debug(f"Flattened shape: {flat_matrix.shape}")
            return flat_matrix

        else:
            raise ValueError(f"Expected tuple or ndarray, but got: {type(state)}")


    def act_old(self, state):
        self.model.eval()

        # If state is a tuple (matrix, drop_count, reorder_count)
        if isinstance(state, tuple):
            matrix, drop_count, reorder_count = state
            matrix = np.array(matrix, dtype=np.float32)
            flat_state = matrix.flatten()
            state_vec = torch.tensor(flat_state, dtype=torch.float32, device=self.device)
        elif isinstance(state, np.ndarray):
            state_vec = torch.tensor(state.flatten(), dtype=torch.float32, device=self.device)
        else:
            raise ValueError(f"Unexpected state type: {type(state)}")

        # 🔥 Assume state_vec is (state_dim,) — reshape to (1, 1, state_dim) for GRU
        state_seq = state_vec.unsqueeze(0).unsqueeze(0)  # (batch_size=1, sequence_len=1, state_dim)

        with torch.no_grad():
            q_values = self.model(state_seq)  # (1, action_dim)
            best_action_idx = q_values.argmax(dim=1).item()

        selected_action = self.action_space[best_action_idx]

        # 🔥 Epsilon-greedy during training
        if self.mode == "train" and np.random.rand() <= self.epsilon:
            idx = np.random.randint(len(self.action_space))
            selected_action = self.action_space[idx]

        # # 🔥 Epsilon-greedy during training
        # if self.mode == "test" and self.TEST ==True:
        #     idx = np.random.randint(len(self.action_space))
        #     selected_action = self.action_space[idx]

        if self.mode == "train":
            self.epsilon = max(self.epsilon_min, self.epsilon * self.epsilon_decay)

        return selected_action




    # def remember(self, state, action_vector, reward, next_state, next_action_vector, done):
    #     self.replay_buffer.add(
    #         np.array(state, dtype=np.float16).flatten(),
    #         np.array(action_vector, dtype=np.float16),
    #         np.float16(reward),
    #         np.array(next_state, dtype=np.float16).flatten(),
    #         np.array(next_action_vector, dtype=np.float16),
    #         done
    #     )


    ## ✅ Store pre-extracted features directly
    def remember(self, features, action_vector, reward, next_features, next_action_vector, done):
        self.replay_buffer.add(
            np.array(features, dtype=np.float16),
            np.array(action_vector, dtype=np.float16),
            np.float16(reward),
            np.array(next_features, dtype=np.float16),
            np.array(next_action_vector, dtype=np.float16),
            done
        )


    ## ✅ Standard replay stays fully compatible (already works on feature vectors)
    def replay(self):
        if len(self.replay_buffer) < self.batch_size:
            return

        if self.buffer_type == "prioritized":
            batch, weights, indices = self.replay_buffer.sample(self.batch_size, beta=self.config.get("importance_beta", 0.4))
        else:
            batch = self.replay_buffer.sample(self.batch_size)
            weights = np.ones(self.batch_size)
            indices = None

        states = torch.tensor(batch[0], dtype=torch.float32, device=self.device)
        actions = torch.tensor(batch[1], dtype=torch.float32, device=self.device)
        rewards = torch.tensor(batch[2], dtype=torch.float32, device=self.device)
        next_states = torch.tensor(batch[3], dtype=torch.float32, device=self.device)
        dones = torch.tensor(batch[5], dtype=torch.float32, device=self.device)
        weights_tensor = torch.tensor(weights, dtype=torch.float32, device=self.device)

        if states.dim() == 3:
            states = states[:, -1, :]
            next_states = next_states[:, -1, :]

        if actions.dim() == 3:
            last_actions = actions[:, -1, :]
            action_list = [tuple(a.cpu().numpy()) for a in last_actions]
            action_idx = []
            for a in action_list:
                action_idx.append(self.action_map.get(a, 0))
            actions = torch.tensor(action_idx, dtype=torch.long, device=self.device)

        states = states.unsqueeze(1)
        next_states = next_states.unsqueeze(1)

        self.model.train()
        self.target_model.eval()

        q_values = self.model(states)
        current_q = q_values.gather(1, actions.unsqueeze(1)).squeeze(1)

        with torch.no_grad():
            next_q_main = self.model(next_states)
            best_next_actions = next_q_main.argmax(dim=1, keepdim=True)
            next_q_target = self.target_model(next_states)
            selected_next_q = next_q_target.gather(1, best_next_actions).squeeze(1)
            target = rewards + (1 - dones) * self.gamma * selected_next_q

        huber_loss = F.smooth_l1_loss(current_q, target, reduction='none')
        loss = (weights_tensor * huber_loss).mean()

        if torch.isnan(loss) or torch.isinf(loss):
            return

        self.optimizer.zero_grad()
        self.scaler.scale(loss).backward()
        self.scaler.unscale_(self.optimizer)
        torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=10.0)
        self.scaler.step(self.optimizer)
        self.scaler.update()

        if self.buffer_type == "prioritized" and indices is not None:
            td_errors = current_q - target
            new_priorities = td_errors.abs().detach().cpu().numpy() + 1e-6
            self.replay_buffer.update_priorities(indices, new_priorities)

        self.training_step += 1
        if self.training_step % self.config.get("soft_update_frequency", 10) == 0:
            self.update_target_model(tau=self.config.get("tau", 0.005))


    def replay_old(self):
        self.logger_agent.debug(f"🚀 Starting Replay Step {self.training_step}")

        # Check if we have enough samples
        if len(self.replay_buffer) < self.batch_size:
            self.logger_agent.warning("❌ Not enough samples in buffer for training.")
            return

        # Sample from replay buffer
        try:
            if self.buffer_type == "prioritized":
                batch, weights, indices = self.replay_buffer.sample(
                    self.batch_size, beta=self.config.get("importance_beta", 0.4)
                )
            else:
                batch = self.replay_buffer.sample(self.batch_size)
                weights = np.ones(self.batch_size)
                indices = None
        except Exception as e:
            self.logger_agent.error(f"🔥 ERROR sampling from replay buffer: {e}")
            return

        try:
            # Convert batch to tensors with proper error handling
            states = torch.tensor(batch[0], dtype=torch.float32, device=self.device)
            actions = torch.tensor(batch[1], dtype=torch.float32, device=self.device)
            rewards = torch.tensor(batch[2], dtype=torch.float32, device=self.device)
            next_states = torch.tensor(batch[3], dtype=torch.float32, device=self.device)
            dones = torch.tensor(batch[5], dtype=torch.float32, device=self.device)
            weights_tensor = torch.tensor(weights, dtype=torch.float32, device=self.device)

            # 🔥 Pick the LAST step from the sequences
            if states.dim() == 3:
                states = states[:, -1, :]  # (batch_size, state_dim)
            if next_states.dim() == 3:
                next_states = next_states[:, -1, :]  # (batch_size, state_dim)

            # 🛠️ Fix actions properly
            if actions.dim() == 3:
                last_actions = actions[:, -1, :]  # (batch_size, 3)

                action_list = [tuple(a.cpu().numpy()) for a in last_actions]
                action_idx = []

                for a in action_list:
                    if a in self.action_map:
                        action_idx.append(self.action_map[a])
                    else:
                        self.logger_agent.error(f"❌ Action {a} not found in action_map!")
                        action_idx.append(0)

                actions = torch.tensor(action_idx, dtype=torch.long, device=self.device)

            # 🛠️ Important: unsqueeze to restore sequence dimension for GRU (T=1)
            states = states.unsqueeze(1)       # (batch_size, 1, state_dim)
            next_states = next_states.unsqueeze(1)  # (batch_size, 1, state_dim)

        except Exception as e:
            self.logger_agent.error(f"🔥 ERROR converting batch to tensors: {e}")
            return

        self.model.train()
        self.target_model.eval()

        for name, tensor in {
            "states": states, "actions": actions,
            "next_states": next_states
        }.items():
            if torch.isnan(tensor).any() or torch.isinf(tensor).any():
                self.logger_agent.error(f"❌ NaN or Inf detected in {name}")
                return

        try:
            with torch.autograd.set_detect_anomaly(True):
                # 🛡️ DEBUG SAFEGUARD
                # self.logger_agent.info(f"🧪 Before gather: states.shape={states.shape}")
                # self.logger_agent.info(f"🧪 Before gather: actions.shape={actions.shape}")

                # Forward current Q-values
                q_values = self.model(states)  # (batch_size, action_dim)
                # self.logger_agent.info(f"🧪 Before gather: q_values.shape={q_values.shape}")
                current_q = q_values.gather(1, actions.unsqueeze(1)).squeeze(1)  # (batch_size,)

                with torch.no_grad():
                    next_q_main = self.model(next_states)
                    best_next_actions = next_q_main.argmax(dim=1, keepdim=True)
                    next_q_target = self.target_model(next_states)
                    selected_next_q = next_q_target.gather(1, best_next_actions).squeeze(1)

                    target = rewards + (1 - dones) * self.gamma * selected_next_q

                
                huber_loss = F.smooth_l1_loss(current_q, target, reduction='none')
                loss = (weights_tensor * huber_loss).mean()

                if torch.isnan(loss) or torch.isinf(loss):
                    self.logger_agent.error("❌ NaN or Inf in loss — skipping this replay step.")
                    return

                self.optimizer.zero_grad()
                self.scaler.scale(loss).backward()
                self.scaler.unscale_(self.optimizer)
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=10.0)
                self.scaler.step(self.optimizer)
                self.scaler.update()
                
                # Update priorities in prioritized replay buffer if used
                if self.buffer_type == "prioritized" and indices is not None:
                    td_errors = current_q - target
                    new_priorities = td_errors.abs().detach().cpu().numpy() + 1e-6
                    self.replay_buffer.update_priorities(indices, new_priorities)

                self.logger_agent.info(f"✅ Replay step {self.training_step} | Loss: {loss.item():.6f}")
                self.training_step += 1

                if self.training_step % self.config.get("soft_update_frequency", 10) == 0:
                    self.update_target_model(tau=self.config.get("tau", 0.005))
                    self.logger_agent.debug("🔁 Target network updated.")

        except Exception as e:
            self.logger_agent.error(f"🔥 ERROR during training step: {e}")

        finally:
            # Clean up to prevent memory leaks
            if 'states' in locals(): del states
            if 'actions' in locals(): del actions
            if 'rewards' in locals(): del rewards
            if 'next_states' in locals(): del next_states
            if 'dones' in locals(): del dones
            if 'weights_tensor' in locals(): del weights_tensor
            torch.cuda.empty_cache()

    

    def update_target_model(self, tau=0.005):
                for target_param, param in zip(self.target_model.parameters(), self.model.parameters()):
                    target_param.data.copy_(tau * param.data + (1.0 - tau) * target_param.data)


    import tempfile

    def load_model(self, path):
        """
        Load model's state_dict from a compressed .zip file (in-memory).
        """
        if not path.endswith(".zip"):
            raise ValueError("Only .zip files supported in compressed mode.")

        with zipfile.ZipFile(path, 'r') as zipf:
            buffer = zipf.read("model.pth")
            state_dict = torch.load(io.BytesIO(buffer), map_location=self.device)
            self.model.load_state_dict(state_dict)
            self.model.eval()

        print(f"✅ Model loaded from zip: {path}")


 

    def save_model(self, path):
        """
        Save model's state_dict and compress it to a .zip file.
        """
        if not path.endswith(".zip"):
            raise ValueError("Please provide a '.zip' path for compression.")

        buffer = io.BytesIO()
        torch.save(self.model.state_dict(), buffer)
        buffer.seek(0)

        with zipfile.ZipFile(path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            zipf.writestr("model.pth", buffer.read())

        print(f"📦 Model zipped and saved to: {path}")


    def save_model_no_zip(self, path):
        """Save only the model's state_dict to avoid security risks."""
        torch.save(self.model.state_dict(), path)
        print(f"Model weights saved at: {path}")
            # ✅ Log the model to W&B
        # wandb.save(path)
        # wandb.log({"model_artifact": wandb.Artifact(f"model_step_{path}", type="model")})
        # self.logger_agent.info(f"Model logged to Weights & Biases at step {path}.")

    def load_model_no_zip(self, path):
        """Load only model weights to prevent security warnings."""
        self.model.load_state_dict(torch.load(path, map_location=self.device))
        self.model.eval()
        print(f"Model weights loaded from: {path}")

    def reset_epsilon(self):
        self.epsilon = self.config['epsilon_start']
