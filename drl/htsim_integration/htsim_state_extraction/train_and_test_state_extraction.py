


from runner_state_extraction import *
import os
import re
import glob
import json
import functools
print = functools.partial(print, flush=True)
from ReplayBuffer_wo import *


import zipfile
import torch
import tempfile
action_space_un700_705=np.array([[7, 7, 7], [7, 5, 5],  [13, 8, 8], [12, 9, 9],
              [5, 6, 6],  [9, 7, 7], [5, 8, 8], [14, 6, 6], 
              [10, 6, 6], [12, 6, 6], [11, 9, 9], [6, 5, 5], [14, 8, 8],
                [6, 7, 7], [10, 8, 8], [9, 9, 9], [7, 9, 9],  
                [12, 8, 8],  [7, 6, 6], [8, 7, 7], [11, 6, 6], 
                [8, 5, 5], [9, 6, 6], [6, 9, 9], [11, 8, 8], [9, 8, 8], 
                [13, 5, 5], [13, 7, 7], [8, 9, 9], [6, 6, 6], [5, 5, 5],
                   [5, 7, 7], [6, 8, 8], [11, 5, 5], [14, 5, 5],
                    [10, 5, 5], [8, 6, 6], [13, 9, 9], [14, 7, 7], [10, 7, 7],
                      [12, 5, 5], [12, 7, 7], 
              [13, 6, 6], [14, 9, 9], [10, 9, 9], [11, 7, 7], [9, 5, 5]])
            

q = 150

def extract_connections_from_file(folder_path, filename):
    file_path = os.path.join(folder_path, filename)
    if not os.path.isfile(file_path):
        return None, None

    num_connections = None
    destinations = set()

    with open(file_path, 'r') as file:
        for line in file:
            match_conn = re.search(r"Connections (\d+)", line)
            if match_conn:
                num_connections = int(match_conn.group(1))

            match_dest = re.search(r"(\d+)->(\d+)", line)
            if match_dest:
                destinations.add(int(match_dest.group(2)))

    return num_connections, destinations


def get_incast_files(folder_path):
    search_pattern = os.path.join(folder_path, "incast_rand_flowsize_incastratio_3_seed7*")
    files = glob.glob(search_pattern)
    filenames = []

    for file in files:
        basename = os.path.basename(file)
        match = re.search(r"seed(\d+)", basename)
        if match:
            seed = int(match.group(1))
            if  706 <= seed <= 769 and seed != 712:
                filenames.append(basename)
    filenames.sort()  # Sort by seed number
    # random.shuffle(filenames) 
    return filenames
def hyp_parameter_to_monitor(config):
    return {
        "learning_rate": config["file_agent_incast"]["learning_rate"],
        "gamma": config["file_agent_incast"]["gamma"],
        "epsilon_start": config["file_agent_incast"]["epsilon_start"],
        "epsilon_min": config["file_agent_incast"]["epsilon_min"],
        "epsilon_decay": config["file_agent_incast"]["epsilon_decay"],
        "batch_size": config["file_agent_incast"]["batch_size"],
        "buffer_size": config["file_agent_incast"]["buffer_size"],
        "num_episodes": config["file_agent_incast"]["num_episodes"]
    }




def find_last_trained_idx(model_dir):
    if not os.path.isdir(model_dir):
        return 0
    max_idx = 0
    pattern = re.compile(r"agent_checkpoint_(\d+)_cm_")
    for filename in os.listdir(model_dir):
        match = pattern.search(filename)
        if match:
            idx = int(match.group(1))
            if idx > max_idx:
                max_idx = idx
    return max_idx+1









def save_model_with_limit(agent, save_path, max_models=10):
    """
    Save model using agent's method and keep only the latest `max_models` checkpoints.
    If save_path ends with .pth, it's converted to .zip for compression.
    """
    # 🔁 Convert .pth to .zip if needed
    if save_path.endswith(".pth"):
        save_path = save_path.replace(".pth", ".zip")

    # 1. Save model
    agent.save_model(save_path)
    print(f"✅ Model weights saved at: {save_path}")

    # 2. Cleanup older models
    model_dir = os.path.dirname(save_path)
    pattern = os.path.join(model_dir, "agent_checkpoint_*.zip")

    model_files = sorted(glob.glob(pattern), key=os.path.getmtime)
    print(f"model files {model_files}")
    if len(model_files) > max_models:
        files_to_delete = model_files[:-max_models]
        for file_path in files_to_delete:
            try:
                os.remove(file_path)
                print(f"🗑️ Deleted old model: {file_path}")
            except Exception as e:
                print(f"⚠️ Failed to delete {file_path}: {e}")


def main_train( cf):
    config_file = cf
    print(config_file)
    with open(config_file, 'r') as f:
        config = json.load(f)

    BASE_LOG_DIR = config["file_env"]["log_dir"]
    MODEL_SAVE_DIR = config["file_env"]["MODEL_SAVE_DIR"]
    os.makedirs(BASE_LOG_DIR, exist_ok=True)
    os.makedirs(MODEL_SAVE_DIR, exist_ok=True)
    folder_path = config["file_env"]["folder_path"]
    mode = config["file_env"]["mode"]
    roce_file=config["file_env"]["roce_config_file"]
    
    print("roce_file", roce_file)#get_incast_files(folder_path
    files = [f"incast_rand_flowsize_incastratio_3_seed{seed}.cm" for seed in range(707, 721)]
    # +[f"incast_rand_flowsize_incastratio_31_seed{seed}.cm" for seed in range(715, 736)]
    # +[f"incast_rand_flowsize_incastratio_63_seed{seed}.cm" for seed in range(1, 736)]
    if mode == "train":
        num = 1

    else:
        num = 1
        

    environment_counter = 0

    test_files = [f"incast_rand_flowsize_incastratio_3_seed{seed}.cm" for seed in range(701, 707)]
    +[f"incast_rand_flowsize_incastratio_31_seed{seed}.cm" for seed in range(701, 707)]
    # test_files_full = [f"incast_rand_flowsize_incastratio_3_seed{seed}.cm" 
    #            for seed in list(range(701, 710)) ]

    
    last_idx = find_last_trained_idx(MODEL_SAVE_DIR)
    print("last_idx-", last_idx)
    runner = Runner(config, buffer_size=10, num_flows=128,num_runs=num, mode=mode)
    print(f"runner {runner}")
    
    for repetitions in range(1):
        for idx, filename in enumerate(files, start=last_idx):
            idx = idx + repetitions * len(files)
            environment_counter += 1
            print(f"\n🧠 Training on environment {environment_counter}: {filename}")
            log_identifier = f"{idx}_{filename}_{mode}_{mode}"
            Logger.update_global_cm(log_identifier)
            runner.cm_file = filename

            

            val = set(tuple(row) for row in action_space_un700_705)  # <- convert fallback to correct format


            runner.cm_file_valid_action =  val
            
            
    

            runner.logger = Logger("Runner", config).get_logger()
            runner.logger.info("Runner initialized.")
            runner.logger.info(f"cm_file: {runner.cm_file}")
            runner.logger.info(f"🎯 Total valid actions: {runner.cm_file_valid_action} \n")
            num_connections, destinations = extract_connections_from_file(folder_path, filename)
            runner.destinations = destinations
            runner.flow_activity = defaultdict(lambda: deque(maxlen=5))
            runner.flow_ids = [i for i in range(runner.num_flows)]            
            runner.logger.info(f"Num Connections: {num_connections}")
            runner.logger.info(f"Destinations: {destinations}")
            print(f'num of connestions: {num_connections}')
            print(runner.cm_file)

            runner.command = (
                f"echo '-----------------------------------' >> {BASE_LOG_DIR}/FCT_random_{idx}_{runner.mode}_{runner.cm_file}.log && "
                f"./htsim_roce -topo topologies/fat_tree_128.topo -tm connection_matrices/incast128/{runner.cm_file} "
                f"-strat ecmp_ar -ar_sticky_delta 10 -nodes 128 -conns {num_connections} "
                f"-pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 "
                f"-mtu 3000 -seed 0 -seed_path 0 -end 999000000000000 -q {q} "
                f"-roce_cfg {roce_file} "
                f"2> >(while read line; do echo \"$(date '+%Y-%m-%d %H:%M:%S') - ERROR - $line\" >> {BASE_LOG_DIR}/assertion_errors_{runner.cm_file}.log; done) "
                # f"2> >(grep 'Assertion' >> {BASE_LOG_DIR}/assertion_errors_{runner.cm_file}.log) "
                f"| tee >(grep --line-buffered -e 'finished' >> {BASE_LOG_DIR}/FCT_random_{idx}_{runner.mode}_{runner.cm_file}.log) "
                f"| grep -e 'Bytes via Flow' -e 'finished at' -e 'dropped' -e 'Wrong' "
                f"| python3 -u liortests/split2timeframes_dynamic.py"
            )



            start = time.perf_counter()
            runner.logger.info(f"Starting run for cm {filename}")
            # runner.start_htsim_process()
            runner.initialize_agent_and_env()
            # wandb.log({"environment_counter_start": runner.agent.training_step})
            runner.run_multiple_times()
            duration = time.perf_counter() - start
            print("🕒 Duration:", duration)
            error_log_path = f"{BASE_LOG_DIR}/assertion_errors_{runner.cm_file}.log"
            skip_save_due_to_stoi = False

            if os.path.exists(error_log_path):
                with open(error_log_path, 'r') as f:
                    for line in f:
                        if "what():  stoi" in line:
                            runner.logger.error("❌ Detected 'stoi' crash, will skip saving agent.")
                            skip_save_due_to_stoi = True
                            model_directory = runner.config["file_env"]["MODEL_SAVE_DIR"]
                            latest_model = runner.get_latest_model(model_directory)

                            if latest_model:
                                runner.agent.load_model(latest_model)
                                runner.logger.info(f"Loaded latest agent model: {latest_model}")
                            
   
                                break




            for handler in runner.logger.handlers:
                handler.flush()
                handler.close()
            runner.logger.handlers = []

            # wandb.log({"environment_counter_end": runner.agent.training_step})

            if mode == "train" and not skip_save_due_to_stoi:
                save_path = f"{MODEL_SAVE_DIR}/agent_checkpoint_{idx}_cm_{runner.cm_file}_run_.pth"
                save_model_with_limit(runner.agent, save_path, max_models=3)
                runner.logger.info(f"✅ Saved agent state: {save_path}")
            else:
                runner.logger.warning("⚠️ Skipped model save due to 'stoi' runtime crash.")





        for test_file in test_files:
                    runner.logger.info(f"🔍 Sanity test on {test_file}")
                    runner.cm_file = test_file
                            
                    
                    # val=runner.get_valid_actions_for_file(70)
                    

 
                    runner.mode = "test"
                    runner.agent.mode = "test"
                    # Distinct logger for sanity test
                    log_identifier = f"{idx}_{test_file}_sanity_test"
                    Logger.update_global_cm(log_identifier)
                    runner.logger = Logger("Runner", config).get_logger()
                    runner.num_runs = 3



                    runner.command = (
                        f"echo '-----------------------------------' >> {BASE_LOG_DIR}/FCT_random_{idx}_{runner.mode}_{runner.cm_file}.log && "
                        f"./htsim_roce -topo topologies/fat_tree_128.topo -tm connection_matrices/incast128/{runner.cm_file} "
                        f"-strat ecmp_ar -ar_sticky_delta 10 -nodes 128 -conns {num_connections} "
                        f"-pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 "
                        f"-mtu 3000 -seed 0 -seed_path 0 -end 500000000 -q {q} "
                        f"-roce_cfg {roce_file} "
                        f"2> >(while read line; do echo \"$(date '+%Y-%m-%d %H:%M:%S') - ERROR - $line\" >> {BASE_LOG_DIR}/assertion_errors_{runner.cm_file}.log; done) "
                        # f"2> >(grep 'Assertion' >> {BASE_LOG_DIR}/assertion_errors_{runner.cm_file}.log) "
                        f"| tee >(grep --line-buffered -e 'finished' >> {BASE_LOG_DIR}/FCT_random_{idx}_{runner.mode}_{runner.cm_file}.log) "
                        f"| grep -e 'Bytes via Flow' -e 'finished at' -e 'dropped' -e 'Wrong' "
                        f"| python3 -u liortests/split2timeframes_dynamic.py"
                    )



                    # runner.initialize_agent_and_env()
                    act=next(iter(val))
                    reordered_action = [act[2], act[0], act[1]]
                    num_connections, destinations = extract_connections_from_file(folder_path, filename)
                    runner.destinations = destinations                    
                    runner._update_config_file(reordered_action)
                    runner.run_multiple_times()

                    # Optionally log or save a sanity-check step
                    # wandb.log({f"sanity_{test_file}_env_{idx}": runner.agent.training_step})

                    # Return to train mode
                    runner.mode = "train"
                    runner.agent.mode = "train"
                    runner.num_runs = 1

def find_last_idx_and_repetition(log_dir, num_envs_per_repetition=15):
    import re
    pattern = re.compile(r"Runner_(\d+)_incast_rand_flowsize_incastratio_3_seed\d+\.cm_train_train\.log")
    last_idx = -1
    for filename in os.listdir(log_dir):
        match = pattern.match(filename)
        if match:
            idx = int(match.group(1))
            if idx > last_idx:
                last_idx = idx
    if last_idx == -1:
        return 0, 0
    repetition = last_idx // num_envs_per_repetition
    start_idx = last_idx % num_envs_per_repetition + 1  # +1 to start from next env
    if start_idx >= num_envs_per_repetition:
        start_idx = 0
        repetition += 1
    return start_idx, repetition

import os
import multiprocessing

def run_train(i):
    recovery_file = "/home/<USER>/POC/drl/htsim_integration/last_successful_i.txt"
    cf = f"/home/<USER>/POC/drl/htsim_integration/generated_configs/config_run_{i}.json"
    print(f"\n🚀 Starting run index {i}")

    try:
        main_train( cf)
        # Step 3: Only update recovery file if run completed successfully
        with open(recovery_file, "w") as f:
            f.write(str(i))
    except Exception as e:
        print(f"❌ Run {i} failed with error: {e}")

# if __name__ == "__main__":
#     num_parallel_runs = 2 # Adjust this based on your CPU/GPU capacity
#     indices = list(range(6))  # e.g., config_run_0 to config_run_9

#     with multiprocessing.Pool(processes=num_parallel_runs) as pool:
#         pool.map(run_train, indices)


if __name__ == "__main__":
    recovery_file = "/home/<USER>/POC/drl/htsim_integration/last_successful_i.txt"

    # Step 1: Read the last successfully completed i
    # try:
    #     with open(recovery_file, "r") as f:
    #         start_i = int(f.read().strip()) + 1
    # except FileNotFoundError:
    #     start_i = 0


    if os.path.exists("/home/<USER>/POC/drl/htsim_integration/generated_configs2/"): # Example hostname check
        start, end = 13, 24
    else:
        start, end = 8, 24


    # Step 2: Start from the correct index
    for i in range(start, end):
        print(i)
        # /home/<USER>/POC/drl/htsim_integration/generated_configs/config_run_0.json
        if os.path.exists("/home/<USER>/POC/drl/htsim_integration/generated_configs2/"):
            cf = f"/home/<USER>/POC/drl/htsim_integration/generated_configs2/config_run_{i}.json"
        else:
            cf = f"/home/<USER>/POC/drl/htsim_integration/generated_configs2/config_run_{i}.json"
        print(f"\n🚀 Starting run index {i}")

        try:
            main_train( cf)
        except Exception as e:
            print(f"❌ Run {i} failed with error: {e}")
            break

        # Step 3: Only update recovery file if run completed successfully
        with open(recovery_file, "w") as f:
            f.write(str(i))

