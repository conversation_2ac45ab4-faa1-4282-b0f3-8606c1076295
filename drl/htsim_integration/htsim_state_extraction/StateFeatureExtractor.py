# import torch
# import torch.nn as nn
# import numpy as np

# class StateFeatureExtractor(nn.Module):
#     def __init__(self, cnn_output_dim=32):
#         super().__init__()

#         # CNN encoder for temporal-spatial transmission matrix
#         self.cnn = nn.Sequential(
#             nn.Conv3d(1, 8, kernel_size=(3, 3, 3), padding=1),
#             nn.ReLU(),
#             nn.Conv3d(8, 16, kernel_size=(3, 3, 3), padding=1),
#             nn.ReLU(),
#             nn.Flatten(),
#             nn.Linear(16 * 10 * 128 * 128, cnn_output_dim),
#             nn.ReLU()
#         )

#         self.global_feature_size = 9
#         self.total_output_size = self.global_feature_size + cnn_output_dim

#     def extract_global_features(self, state_tensor):
#         """
#         Compute global statistical features from state tensor.
#         state_tensor: torch tensor of shape (10, 128, 128)
#         """
#         state_np = state_tensor.cpu().numpy()

#         total_bytes = np.sum(state_np)
#         mean_throughput = np.mean(state_np)
#         std_throughput = np.std(state_np)
#         max_throughput = np.max(state_np)
#         min_throughput = np.min(state_np)
#         median_throughput = np.median(state_np)
#         cv_throughput = std_throughput / (mean_throughput + 1e-6)
#         throughput_slope = (np.sum(state_np[-1]) - np.sum(state_np[0])) / 10
#         nonzero_flows = np.count_nonzero(state_np)

#         global_features = np.array([
#             total_bytes, mean_throughput, std_throughput, max_throughput,
#             min_throughput, median_throughput, cv_throughput,
#             throughput_slope, nonzero_flows
#         ], dtype=np.float32)

#         return torch.tensor(global_features, dtype=torch.float32)

#     def forward(self, state_tensor):
#         """
#         Full feature extraction pipeline.

#         state_tensor: torch tensor of shape (10, 128, 128)
#         output: tensor of shape (total_output_size,)
#         """
#         # CNN embedding
#         x = state_tensor.unsqueeze(0).unsqueeze(0)  # shape: (1, 1, 10, 128, 128)
#         cnn_embed = self.cnn(x).squeeze(0)

#         # Global statistical features
#         global_features = self.extract_global_features(state_tensor)

#         # Concatenate both
#         full_state = torch.cat([global_features, cnn_embed], dim=0)

#         return full_state
import torch
import torch.nn as nn
import numpy as np

class StateFeatureExtractorO(nn.Module):
    def __init__(self, cnn_output_dim=32, device=None):
        super().__init__()

        self.device = device or torch.device('cpu')  # Pass device to CNN
        self.cnn = nn.Sequential(
            nn.Conv3d(1, 8, kernel_size=(3, 3, 3), padding=1),
            nn.ReLU(),
            nn.Conv3d(8, 16, kernel_size=(3, 3, 3), padding=1),
            nn.ReLU(),
            nn.Flatten(),
            nn.Linear(16 * 10 * 128 * 128, cnn_output_dim),
            nn.ReLU()
        ).to(self.device)

        self.global_feature_size = 9
        self.total_output_size = self.global_feature_size + cnn_output_dim

    def extract_global_features(self, state_tensor):
        """
        Compute global statistical features from state tensor.
        state_tensor: torch tensor of shape (10, 128, 128)
        """
        state_np = state_tensor.cpu().numpy()

        total_bytes = np.sum(state_np)
        mean_throughput = np.mean(state_np)
        std_throughput = np.std(state_np)
        max_throughput = np.max(state_np)
        min_throughput = np.min(state_np)
        median_throughput = np.median(state_np)
        cv_throughput = std_throughput / (mean_throughput + 1e-6)
        throughput_slope = (np.sum(state_np[-1]) - np.sum(state_np[0])) / 10
        nonzero_flows = np.count_nonzero(state_np)

        global_features = np.array([
            total_bytes, mean_throughput, std_throughput, max_throughput,
            min_throughput, median_throughput, cv_throughput,
            throughput_slope, nonzero_flows
        ], dtype=np.float32)

        return torch.tensor(global_features, dtype=torch.float32, device=self.device)

    def forward(self, state_tensor):
        """
        Full feature extraction pipeline.

        state_tensor: torch tensor of shape (10, 128, 128)
        output: tensor of shape (total_output_size,)
        """
        x = state_tensor.unsqueeze(0).unsqueeze(0).to(self.device)  # (1, 1, 10, 128, 128)
        cnn_embed = self.cnn(x).squeeze(0)
        global_features = self.extract_global_features(state_tensor)
        full_state = torch.cat([global_features, cnn_embed], dim=0)
        return full_state



import torch
import numpy as np
from torch import nn
from scipy.ndimage import sobel, laplace
from scipy.stats import entropy, kurtosis, skew
from skimage.feature import graycomatrix as greycomatrix, graycoprops as greycoprops
from skimage.feature import hog
import time

class StateFeatureExtractor_with_zero(nn.Module):
    def __init__(self, device=None, use_slow_features=False):
        super().__init__()
        self.device = device or torch.device("cpu")
        self.use_slow_features = use_slow_features

    def edge_features(self, spatial_slice):
        t_sobel = time.time()
        sobel_x = sobel(spatial_slice, axis=0)
        sobel_y = sobel(spatial_slice, axis=1)
        t_laplacian = time.time()
        laplacian = laplace(spatial_slice)
        t_end = time.time()

        #print("⏱️ sobel: {t_laplacian - t_sobel:.6f}s")
        #print("⏱️ laplacian: {t_end - t_laplacian:.6f}s")

        return [
            np.mean(np.abs(sobel_x)),
            np.mean(np.abs(sobel_y)),
            np.mean(np.abs(laplacian)),
            np.std(laplacian),
        ]

    def texture_features(self, spatial_slice):
        t_start = time.time()
        norm_slice = ((spatial_slice - spatial_slice.min()) / (spatial_slice.ptp() + 1e-6) * 63).astype(np.uint8)
        t_glcm = time.time()
        glcm = greycomatrix(norm_slice, distances=[1], angles=[0], levels=64, symmetric=True, normed=True)
        t_props = time.time()
        props = [
            greycoprops(glcm, p)[0, 0] for p in ['contrast', 'dissimilarity', 'homogeneity', 'energy', 'correlation']
        ]
        t_end = time.time()

        #print("⏱️ texture_norm: {t_glcm - t_start:.6f}s")
        #print("⏱️ texture_glcm: {t_props - t_glcm:.6f}s")
        #print("⏱️ texture_props: {t_end - t_props:.6f}s")

        return props

    def spatial_entropy(self, spatial_slice):
        t_start = time.time()
        hist, _ = np.histogram(spatial_slice, bins=32, density=True)
        result = entropy(hist + 1e-6)
        #print("⏱️ spatial_entropy_fast: {time.time() - t_start:.6f}s")
        return [result, 0.0]

    def statistical_features(self, spatial_slice):
        t_start = time.time()
        flattened = spatial_slice[spatial_slice != 0]
        if len(flattened) == 0:
            result = [0.0, 0.0, 0.0, 0.0]
        else:
            result = [
                np.mean(flattened),
                np.std(flattened),
                kurtosis(flattened),
                skew(flattened),
            ]
        #print("⏱️ statistical_features: {time.time() - t_start:.6f}s")
        return result

    def fft_features(self, spatial_slice):
        t_start = time.time()
        fft = np.fft.fft2(spatial_slice)
        magnitude = np.abs(np.fft.fftshift(fft))
        result = [
            np.mean(magnitude),
            np.std(magnitude),
            np.max(magnitude),
            np.min(magnitude),
            np.median(magnitude),
            np.percentile(magnitude, 95),
        ]
        #print("⏱️ fft_features: {time.time() - t_start:.6f}s")
        return result

    def extract_global_features(self, state_tensor):
        t_start = time.time()
        state_np = state_tensor.cpu().numpy()
        flattened = state_np[state_np != 0]

        if flattened.size == 0:
            result = [0.0] * 14
        else:
            window_means = np.mean(state_np, axis=(1, 2))
            result = [
                np.sum(flattened),
                np.mean(flattened),
                np.std(flattened),
                np.max(flattened),
                np.min(flattened),
                np.median(flattened),
                np.std(flattened) / (np.mean(flattened) + 1e-6),
                kurtosis(flattened),
                skew(flattened),
                (np.sum(state_np[-1]) - np.sum(state_np[0])) / 10,
                np.mean(window_means),
                np.std(window_means),
                np.std(window_means) / (np.mean(window_means) + 1e-6),
                np.count_nonzero(state_np)
            ]
        #print("⏱️ extract_global_features: {time.time() - t_start:.6f}s")
        return result

    def forward(self, state_tensor):
        state_np = state_tensor.cpu().numpy()
        time_start = time.time()
        edge_feats_all = [self.edge_features(s) for s in state_np]
        fft_feats_all = [self.fft_features(s) for s in state_np]
        entropy_feats_all = [self.spatial_entropy(s) for s in state_np]
        statistical_feats_all = [self.statistical_features(s) for s in state_np]

        if self.use_slow_features:
            texture_feats_all = [self.texture_features(s) for s in state_np]
            texture_feats = np.mean(texture_feats_all, axis=0).tolist() + np.std(texture_feats_all, axis=0).tolist()
        else:
            texture_feats = []

        global_feats = self.extract_global_features(state_tensor)
        edge_feats = np.mean(edge_feats_all, axis=0).tolist() + np.std(edge_feats_all, axis=0).tolist()
        fft_feats = np.mean(fft_feats_all, axis=0).tolist() + np.std(fft_feats_all, axis=0).tolist()
        entropy_feats = np.mean(entropy_feats_all, axis=0).tolist()
        statistical_feats = np.mean(statistical_feats_all, axis=0).tolist() + np.std(statistical_feats_all, axis=0).tolist()

        all_feats = global_feats + edge_feats + fft_feats + texture_feats + entropy_feats + statistical_feats
        # print(f"⏱️ Total time: {time.time() - time_start:.6f}s")
        return torch.tensor(all_feats, dtype=torch.float32, device=self.device)






import torch
import numpy as np
from torch import nn
from scipy.ndimage import sobel, laplace
from scipy.stats import entropy, kurtosis, skew
from skimage.feature import graycomatrix, graycoprops
from numpy.fft import fft2, fftshift

class StateFeatureExtractor_pp(nn.Module):
    def __init__(self, device=None, use_slow_features=False):
        super().__init__()
        self.device = device or torch.device("cpu")
        self.use_slow_features = use_slow_features

    def edge_features(self, spatial_slice):
        mask = spatial_slice != 0
        if not np.any(mask):
            return [0.0, 0.0, 0.0, 0.0]

        sobel_x = sobel(spatial_slice, axis=0)
        sobel_y = sobel(spatial_slice, axis=1)
        laplacian = laplace(spatial_slice)

        return [
            np.mean(np.abs(sobel_x[mask])),
            np.mean(np.abs(sobel_y[mask])),
            np.mean(np.abs(laplacian[mask])),
            np.std(laplacian[mask]),
        ]

    def texture_features(self, spatial_slice):
        mask = spatial_slice != 0
        if not np.any(mask):
            return [0.0] * 5

        norm_slice = ((spatial_slice - spatial_slice.min()) / (spatial_slice.ptp() + 1e-6) * 63).astype(np.uint8)
        glcm = graycomatrix(norm_slice, distances=[1], angles=[0], levels=64, symmetric=True, normed=True)
        props = [graycoprops(glcm, p)[0, 0] for p in ['contrast', 'dissimilarity', 'homogeneity', 'energy', 'correlation']]
        return props

    def spatial_entropy(self, spatial_slice):
        mask = spatial_slice != 0
        if not np.any(mask):
            return [0.0]

        hist, _ = np.histogram(spatial_slice[mask], bins=32, density=True)
        return [entropy(hist + 1e-6)]

    def statistical_features(self, spatial_slice):
        flattened = spatial_slice[spatial_slice != 0]
        if len(flattened) == 0:
            return [0.0, 0.0, 0.0, 0.0]

        return [
            np.mean(flattened),
            np.std(flattened),
            kurtosis(flattened),
            skew(flattened),
        ]

    def fft_features(self, spatial_slice):
        mask = spatial_slice != 0
        if not np.any(mask):
            return [0.0] * 6

        fft_mag = np.abs(fftshift(fft2(spatial_slice)))
        return [
            np.mean(fft_mag),
            np.std(fft_mag),
            np.max(fft_mag),
            np.min(fft_mag),
            np.median(fft_mag),
            np.percentile(fft_mag, 95),
        ]

    def extract_global_features(self, state_tensor):
        state_np = state_tensor
        flattened = state_np[state_np != 0]

        if flattened.size == 0:
            return [0.0] * 14

        window_means = np.mean(state_np, axis=(1, 2))
        return [
            np.sum(flattened),
            np.mean(flattened),
            np.std(flattened),
            np.max(flattened),
            np.min(flattened),
            np.median(flattened),
            np.std(flattened) / (np.mean(flattened) + 1e-6),
            kurtosis(flattened),
            skew(flattened),
            (np.sum(state_np[-1]) - np.sum(state_np[0])) / 10,
            np.mean(window_means),
            np.std(window_means),
            np.std(window_means) / (np.mean(window_means) + 1e-6),
            np.count_nonzero(state_np)
        ]

    def forward(self, state_tensor):
        state_np = state_tensor.cpu().numpy()

        edge_feats = np.mean([self.edge_features(s) for s in state_np], axis=0).tolist()
        fft_feats = np.mean([self.fft_features(s) for s in state_np], axis=0).tolist()
        entropy_feats = np.mean([self.spatial_entropy(s) for s in state_np], axis=0).tolist()
        statistical_feats = np.mean([self.statistical_features(s) for s in state_np], axis=0).tolist()

        texture_feats = []
        if self.use_slow_features:
            texture_feats = np.mean([self.texture_features(s) for s in state_np], axis=0).tolist()

        global_feats = self.extract_global_features(state_np)

        all_feats = global_feats + edge_feats + fft_feats + texture_feats + entropy_feats + statistical_feats
        return torch.tensor(all_feats, dtype=torch.float32, device=self.device)





import torch
import torch.nn as nn
import numpy as np
from scipy.ndimage import sobel, laplace
from scipy.stats import entropy, kurtosis, skew
from skimage.feature import graycomatrix, graycoprops
from numpy.fft import fft2, fftshift


class StateFeatureExtractor(nn.Module):
    def __init__(self,
                 device=None,
                 use_cnn=True,
                 use_global_stats=True,
                 use_global_basic=True,  # your 9-feature vector
                 use_fft=True,
                 use_edge=True,
                 use_entropy=True,
                 use_statistical=True,
                 use_texture=False,
                 cnn_output_dim=32):
        super().__init__()
        self.device = device or torch.device("cpu")
        self.use_cnn = use_cnn
        self.use_global_stats = use_global_stats
        self.use_global_basic = use_global_basic
        self.use_fft = use_fft
        self.use_edge = use_edge
        self.use_entropy = use_entropy
        self.use_statistical = use_statistical
        self.use_texture = use_texture

        if self.use_cnn:
            self.cnn = nn.Sequential(
                nn.Conv3d(1, 8, kernel_size=(3, 3, 3), padding=1),
                nn.ReLU(),
                nn.Conv3d(8, 16, kernel_size=(3, 3, 3), padding=1),
                nn.ReLU(),
                nn.Flatten(),
                nn.Linear(16 * 10 * 128 * 128, cnn_output_dim),
                nn.ReLU()
            ).to(self.device)
            self.cnn_output_dim = cnn_output_dim
        else:
            self.cnn_output_dim = 0

    def extract_global_basic_features(self, state_np):
        total_bytes = np.sum(state_np)
        mean_throughput = np.mean(state_np)
        std_throughput = np.std(state_np)
        max_throughput = np.max(state_np)
        min_throughput = np.min(state_np)
        median_throughput = np.median(state_np)
        cv_throughput = std_throughput / (mean_throughput + 1e-6)
        throughput_slope = (np.sum(state_np[-1]) - np.sum(state_np[0])) / 10
        nonzero_flows = np.count_nonzero(state_np)

        return [
            total_bytes, mean_throughput, std_throughput, max_throughput,
            min_throughput, median_throughput, cv_throughput,
            throughput_slope, nonzero_flows
        ]

    def extract_global_extended_features(self, state_np):
        values = state_np[state_np != 0]
        if values.size == 0:
            return [0.0] * 14
        window_means = np.mean(state_np, axis=(1, 2))
        return [
            np.sum(values), np.mean(values), np.std(values),
            np.max(values), np.min(values), np.median(values),
            np.std(values) / (np.mean(values) + 1e-6),
            kurtosis(values), skew(values),
            (np.sum(state_np[-1]) - np.sum(state_np[0])) / 10,
            np.mean(window_means), np.std(window_means),
            np.std(window_means) / (np.mean(window_means) + 1e-6),
            np.count_nonzero(state_np)
        ]

    def edge_features(self, spatial_slice):
        mask = spatial_slice != 0
        if not np.any(mask):
            return [0.0] * 4
        return [
            np.mean(np.abs(sobel(spatial_slice, axis=0)[mask])),
            np.mean(np.abs(sobel(spatial_slice, axis=1)[mask])),
            np.mean(np.abs(laplace(spatial_slice)[mask])),
            np.std(laplace(spatial_slice)[mask]),
        ]

    def texture_features(self, spatial_slice):
        mask = spatial_slice != 0
        if not np.any(mask):
            return [0.0] * 5
        norm_slice = ((spatial_slice - spatial_slice.min()) / (spatial_slice.ptp() + 1e-6) * 63).astype(np.uint8)
        glcm = graycomatrix(norm_slice, distances=[1], angles=[0], levels=64, symmetric=True, normed=True)
        return [graycoprops(glcm, p)[0, 0] for p in ['contrast', 'dissimilarity', 'homogeneity', 'energy', 'correlation']]

    def spatial_entropy(self, spatial_slice):
        mask = spatial_slice != 0
        if not np.any(mask):
            return [0.0]
        hist, _ = np.histogram(spatial_slice[mask], bins=32, density=True)
        return [entropy(hist + 1e-6)]

    def statistical_features(self, spatial_slice):
        values = spatial_slice[spatial_slice != 0]
        if len(values) == 0:
            return [0.0] * 4
        return [np.mean(values), np.std(values), kurtosis(values), skew(values)]

    def fft_features(self, spatial_slice):
        mask = spatial_slice != 0
        if not np.any(mask):
            return [0.0] * 6
        fft_mag = np.abs(fftshift(fft2(spatial_slice)))
        return [
            np.mean(fft_mag), np.std(fft_mag),
            np.max(fft_mag), np.min(fft_mag),
            np.median(fft_mag), np.percentile(fft_mag, 95)
        ]

    def forward(self, state_tensor):
        state_np = state_tensor.cpu().numpy()
        features = []

        # Combine global features
        if self.use_global_basic:
            features += self.extract_global_basic_features(state_np)
        if self.use_global_stats:
            features += self.extract_global_extended_features(state_np)

        if self.use_edge:
            features += np.mean([self.edge_features(s) for s in state_np], axis=0).tolist()

        if self.use_fft:
            features += np.mean([self.fft_features(s) for s in state_np], axis=0).tolist()

        if self.use_entropy:
            features += np.mean([self.spatial_entropy(s) for s in state_np], axis=0).tolist()

        if self.use_statistical:
            features += np.mean([self.statistical_features(s) for s in state_np], axis=0).tolist()

        if self.use_texture:
            features += np.mean([self.texture_features(s) for s in state_np], axis=0).tolist()

        features_tensor = torch.tensor(features, dtype=torch.float32, device=self.device)

        if self.use_cnn:
            x = state_tensor.unsqueeze(0).unsqueeze(0).to(self.device)  # (1, 1, 10, 128, 128)
            cnn_tensor = self.cnn(x).squeeze(0)
            return torch.cat([features_tensor, cnn_tensor], dim=0)

        return features_tensor
# extractor = StateFeatureExtractor(
#     device=torch.device("cuda"),
#     use_cnn=True,
#     use_global_basic=True,
#     use_global_stats=True,
#     use_fft=True,
#     use_edge=True,
#     use_entropy=True,
#     use_statistical=True,
#     use_texture=False  # Set True only if needed
# )

# state_tensor = torch.rand((10, 128, 128))  # Example input
# features = extractor(state_tensor)
# print(f"Feature vector shape: {features.shape}")

# Example usage:
# extractor = SparseStateFeatureExtractor(device=torch.device('cuda'), use_slow_features=False)
# state_tensor = torch.rand((10, 128, 128))
# features = extractor(state_tensor)

# # print(features)


# # Example usage:
extractor = StateFeatureExtractor(device=torch.device('cuda'))
state_tensor = torch.rand((10, 128, 128))
time_start = time.time()

features = extractor(state_tensor)
# #print("⏱️ Total time: {time.time() - time_start:.6f}s")
