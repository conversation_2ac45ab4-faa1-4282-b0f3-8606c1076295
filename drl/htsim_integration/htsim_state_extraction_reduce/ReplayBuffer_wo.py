
import time
# Base class for replay memory
class ReplayBuffer:
    def __init__(self, size):
        self.size = size
        self.buffer = []
        self.position = 0

    def __len__(self):
        return len(self.buffer)

    def add(self, transaction):
        if len(self.buffer) < self.size:
            self.buffer.append(transaction)
        else:
            self.buffer[self.position] = transaction
        self.position = (self.position + 1) % self.size

        # Debugging: Log the shapes of components as they are added
        state, action, reward, next_state, done = transaction
        # logging.debug(f"Added to buffer: State shape: {np.shape(state)}, Action shape: {np.shape(action)}, "
        #               f"Reward: {reward}, Next state shape: {np.shape(next_state)}, Done: {done}")

    def sample(self, batch_size):
        indices = np.random.choice(len(self.buffer), batch_size, replace=False)
        batch = [self.buffer[i] for i in indices]

        try:
            states, actions, rewards, next_states, dones = zip(*batch)

            # Auto-detect if actions are scalar or multi-action (e.g., vector of ECMPs)
            if isinstance(actions[0], (list, np.ndarray)):
                actions = np.stack(actions)  # shape: (batch_size, action_dim)
            else:
                actions = np.array(actions)  # shape: (batch_size,)

            return (
                np.array(states),
                actions,
                np.array(rewards),
                np.array(next_states),
                np.array(dones)
            )

        except Exception as e:
            logging.error(f"❌ Error converting batch to arrays: {e}")
            raise


    def sample_o(self, batch_size):
        indices = np.random.choice(len(self.buffer), batch_size, replace=False)
        batch = [self.buffer[i] for i in indices]

        # Debugging: Log each component's shape in the batch
        for i, item in enumerate(batch):
            state, action, reward, next_state, done = item
            # logging.debug(f"Item {i} in batch: State shape: {np.shape(state)}, Action shape: {np.shape(action)}, "
            #               f"Reward: {reward}, Next state shape: {np.shape(next_state)}, Done: {done}")

        # Attempt to convert batch to numpy arrays and catch any shape errors
        try:
            # logging.debug("Attempting to convert batch to numpy arrays...")
            return batch#tuple(map(np.array, zip(*batch)))  # Convert the map object to a tuple of numpy arrays
        except ValueError as e:
            logging.error(f"Error when converting batch to numpy arrays: {e}")
            # for i, item in enumerate(batch):
                # logging.error(f"Item {i} details: {item}")
            raise

import numpy as np
import random
import operator
import logging

# # Setup logging to file and console
# logging.basicConfig(
#     level=logging.INFO,
#     format='%(asctime)s - %(levelname)s - %(message)s',
#     handlers=[
#         logging.FileHandler("replay_buffer_debug.log")
#         # logging.StreamHandler()
#     ]
# )

class SegmentTree:
    def __init__(self, capacity, operation, neutral_element):
        assert capacity > 0 and capacity & (capacity - 1) == 0, "Capacity must be a power of 2."
        self.capacity = capacity
        self.tree = [neutral_element] * (2 * capacity)
        self.operation = operation
        self.neutral_element = neutral_element

    def update(self, idx, value):
        idx += self.capacity
        self.tree[idx] = value
        while idx > 1:
            idx //= 2
            self.tree[idx] = self.operation(self.tree[2 * idx], self.tree[2 * idx + 1])

    def query(self, start, end):
        result = self.neutral_element
        start += self.capacity
        end += self.capacity

        while start < end:
            if start % 2 == 1:
                result = self.operation(result, self.tree[start])
                start += 1
            if end % 2 == 1:
                end -= 1
                result = self.operation(result, self.tree[end])
            start //= 2
            end //= 2

        return result

    def get(self, idx):
        return self.tree[self.capacity + idx]

    def find_prefixsum_idx(self, prefixsum):
        idx = 1
        while idx < self.capacity:
            if self.tree[2 * idx] > prefixsum:
                idx = 2 * idx
            else:
                prefixsum -= self.tree[2 * idx]
                idx = 2 * idx + 1
        return idx - self.capacity


class SumSegmentTree(SegmentTree):
    def __init__(self, capacity):
        super().__init__(capacity, operator.add, 0.0)

    def sum(self, start=0, end=None):
        if end is None:
            end = self.capacity
        return self.query(start, end)


class MinSegmentTree(SegmentTree):
    def __init__(self, capacity):
        super().__init__(capacity, min, float('inf'))

    def min(self, start=0, end=None):
        if end is None:
            end = self.capacity
        return self.query(start, end)


class PrioritizedReplayBuffer:
    def __init__(self, size, alpha=0.6):
        assert alpha > 0, "Alpha must be positive."
        self.size = size
        self.buffer = []
        self.position = 0
        self.alpha = alpha

        # Initialize Sum and Min Segment Trees
        capacity = 1
        while capacity < size:
            capacity *= 2
        self.sum_tree = SumSegmentTree(capacity)
        self.min_tree = MinSegmentTree(capacity)
        self.max_priority = 1.0

        logging.debug(f"Initialized trees with capacity: {capacity}, Buffer size: {size}")

    def __len__(self):
        return len(self.buffer)

    def add(self, transaction, priority=1.0):
        # start_time = time.time()
        if len(self.buffer) < self.size:
            self.buffer.append(transaction)
        else:
            self.buffer[self.position] = transaction

        idx = self.position
        self.sum_tree.update(idx, priority ** self.alpha)
        self.min_tree.update(idx, priority ** self.alpha)
        self.max_priority = max(self.max_priority, priority)

        self.position = (self.position + 1) % self.size



    # def add(self, transaction, priority=1.0):
    #     if len(self.buffer) < self.size:
            
    #         self.buffer.append(transaction)
    #         idx = self.position
    #         self.position = (self.position + 1) % self.size
    #     else:
    #         # Purge the oldest 20%
    #         # print("else",len(self.buffer))
    #         purge_count = int(0.2 * self.size)
    #         # print("purge_count",purge_count)
    #         self.buffer = self.buffer[purge_count:]
            
    #         # Reset position and append new transaction
    #         self.position = len(self.buffer)
    #         self.buffer.append(transaction)

    #         # Reset and rebuild priority trees
    #         self._rebuild_trees()
    #         idx = self.position - 1
    #         print(len(self.buffer))
    #     # Update segment trees
    #     self.sum_tree.update(idx, priority ** self.alpha)
    #     self.min_tree.update(idx, priority ** self.alpha)
    #     self.max_priority = max(self.max_priority, priority)


    def _rebuild_trees(self):
        """Rebuild sum and min segment trees after purging buffer."""
        capacity = self.sum_tree.capacity
        self.sum_tree = SumSegmentTree(capacity)
        self.min_tree = MinSegmentTree(capacity)
        for i, _ in enumerate(self.buffer):
            self.sum_tree.update(i, self.max_priority ** self.alpha)
            self.min_tree.update(i, self.max_priority ** self.alpha)

    def sample(self, batch_size, beta=0.4):
        # start_time = time.time()
        if len(self.buffer) < batch_size:
            raise ValueError(f"Not enough samples to draw. Buffer size: {len(self.buffer)}, Batch size: {batch_size}")

        # Precompute values that are used multiple times
        total_sum = self.sum_tree.sum()
        min_prob = self.min_tree.min() / total_sum
        max_weight = (min_prob * len(self.buffer)) ** (-beta)

        # Sample indices proportionally
        indices = self._sample_proportional(batch_size)

        # Compute weights efficiently
        sample_probs = np.array([self.sum_tree.get(idx) / total_sum for idx in indices])
        weights = (sample_probs * len(self.buffer)) ** (-beta) / max_weight

        # Encode sampled transitions
        batch = self._encode_sample(indices)
        # end_time = time.time()
        # print(f"sample() took {end_time - start_time:.6f} seconds")
        return batch, weights, indices


    def update_priorities(self, indices, priorities):
        assert len(indices) == len(priorities)
        for idx, priority in zip(indices, priorities):
            assert priority > 0, "Priority must be positive."
            self.sum_tree.update(idx, priority ** self.alpha)
            self.min_tree.update(idx, priority ** self.alpha)
            self.max_priority = max(self.max_priority, priority)



    def _sample_proportional(self, batch_size):
        total = self.sum_tree.sum()  # Calculate total priority sum once
        segment = total / batch_size
        logging.debug(f"Total priority sum: {total}, Segment size: {segment}")

        # Precompute segment boundaries
        boundaries = [random.uniform(i * segment, (i + 1) * segment) for i in range(batch_size)]
        logging.debug(f"Sampling boundaries: {boundaries}")
        indices = [self.sum_tree.find_prefixsum_idx(mass) for mass in boundaries]
        logging.debug(f"Sampled indices: {indices}")
        return indices


    def _encode_sample_o(self, indices):
        batch = [self.buffer[idx] for idx in indices]
        return tuple(map(np.array, zip(*batch)))
    def _encode_sample(self, indices):
        batch = [self.buffer[idx] for idx in indices]
        states, actions, rewards, next_states, next_valids, dones = zip(*batch)
        return (
            np.array(states),
            np.array(actions),
            np.array(rewards),
            np.array(next_states),
            list(next_valids),  # Leave as list-of-arrays
            np.array(dones)
        )


# Usage Example
if __name__ == "__main__":
    buffer = PrioritizedReplayBuffer(size=8, alpha=0.6)
    for i in range(10):
        buffer.add((np.random.rand(3), i, np.random.rand(), np.random.rand(3), False), priority=random.uniform(0.1, 1.0))
    try:
        batch, weights, indices = buffer.sample(5, beta=0.4)
    #     print("Sampled Batch:", batch)
    #     print("Sampled Weights:", weights)
    #     print("Sampled Indices:", indices)
    #     print(buffer)
        print("-----------------------")
    except Exception as e:
        logging.error(f"Error during sampling: {e}")
