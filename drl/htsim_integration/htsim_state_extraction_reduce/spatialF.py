import torch
import numpy as np
from scipy.ndimage import sobel, laplace, generic_filter
from scipy.stats import entropy, kurtosis, skew
from skimage.feature import greycomatrix, greycoprops

class SpatialFeatureExtractor:
    def __init__(self, device=None):
        self.device = device or torch.device('cpu')

    def edge_features(self, spatial_slice):
        sobel_x = sobel(spatial_slice, axis=0)
        sobel_y = sobel(spatial_slice, axis=1)
        laplacian = laplace(spatial_slice)

        features = [
            np.mean(np.abs(sobel_x)),
            np.mean(np.abs(sobel_y)),
            np.mean(np.abs(laplacian)),
            np.std(laplacian),
        ]
        return features

    def texture_features(self, spatial_slice):
        norm_slice = ((spatial_slice - spatial_slice.min()) / (spatial_slice.ptp() + 1e-6) * 255).astype(np.uint8)
        glcm = greycomatrix(norm_slice, distances=[1], angles=[0], levels=256, symmetric=True, normed=True)
        contrast = greycoprops(glcm, 'contrast')[0, 0]
        dissimilarity = greycoprops(glcm, 'dissimilarity')[0, 0]
        homogeneity = greycoprops(glcm, 'homogeneity')[0, 0]
        energy = greycoprops(glcm, 'energy')[0, 0]
        correlation = greycoprops(glcm, 'correlation')[0, 0]

        return [contrast, dissimilarity, homogeneity, energy, correlation]

    def spatial_entropy(self, spatial_slice, neighborhood_size=5):
        def local_entropy(block):
            hist, _ = np.histogram(block, bins=10, range=(block.min(), block.max()))
            hist = hist / (hist.sum() + 1e-6)
            return entropy(hist + 1e-6)

        ent = generic_filter(spatial_slice, local_entropy, size=neighborhood_size)
        return [np.mean(ent), np.std(ent)]

    def extract_global_features(self, state_tensor):
        state_np = state_tensor.cpu().numpy()  # shape: (10, 128, 128)
        flattened = state_np[state_np != 0]  # only non-zero values

        # Overall statistical features on non-zeros
        total_bytes = np.sum(flattened)
        mean_throughput = np.mean(flattened)
        std_throughput = np.std(flattened)
        max_throughput = np.max(flattened)
        min_throughput = np.min(flattened)
        median_throughput = np.median(flattened)
        cv_throughput = std_throughput / (mean_throughput + 1e-6)
        kurt = kurtosis(flattened)
        skewness = skew(flattened)

        # Trend features over time
        throughput_slope = (np.sum(state_np[-1]) - np.sum(state_np[0])) / 10

        # Temporal windowed features (e.g., per-slice means)
        window_means = np.mean(state_np, axis=(1, 2))  # shape (10,)
        window_mean_of_means = np.mean(window_means)
        window_std_of_means = np.std(window_means)
        window_cv_of_means = window_std_of_means / (window_mean_of_means + 1e-6)

        nonzero_flows = np.count_nonzero(state_np)

        return [
            total_bytes, mean_throughput, std_throughput, max_throughput,
            min_throughput, median_throughput, cv_throughput, kurt, skewness,
            throughput_slope, window_mean_of_means, window_std_of_means, window_cv_of_means,
            nonzero_flows
        ]

    def statistical_features(self, spatial_slice):
        flattened = spatial_slice.flatten()
        nonzero_vals = flattened[flattened != 0]
        if len(nonzero_vals) == 0:
            return [0.0, 0.0, 0.0, 0.0]
        kurt_val = kurtosis(nonzero_vals)
        skew_val = skew(nonzero_vals)
        std_val = np.std(nonzero_vals)
        mean_val = np.mean(nonzero_vals)
        return [mean_val, std_val, kurt_val, skew_val]

    def extract_all_features(self, state_tensor):
        state_np = state_tensor.cpu().numpy()  # shape: (10, 128, 128)

        edge_feats_all = []
        texture_feats_all = []
        entropy_feats_all = []
        statistical_feats_all = []

        for interval_slice in state_np:
            edge_feats_all.append(self.edge_features(interval_slice))
            texture_feats_all.append(self.texture_features(interval_slice))
            entropy_feats_all.append(self.spatial_entropy(interval_slice))
            statistical_feats_all.append(self.statistical_features(interval_slice))

        edge_feats = np.mean(edge_feats_all, axis=0).tolist() + np.std(edge_feats_all, axis=0).tolist()
        texture_feats = np.mean(texture_feats_all, axis=0).tolist() + np.std(texture_feats_all, axis=0).tolist()
        entropy_feats = np.mean(entropy_feats_all, axis=0).tolist()
        statistical_feats = np.mean(statistical_feats_all, axis=0).tolist() + np.std(statistical_feats_all, axis=0).tolist()
        global_feats = self.extract_global_features(state_tensor)

        all_feats = global_feats + edge_feats + texture_feats + entropy_feats + statistical_feats
        return torch.tensor(all_feats, dtype=torch.float32, device=self.device)

# Example usage:
# extractor = SpatialFeatureExtractor(device=torch.device('cuda'))
# state_tensor = torch.rand((10, 128, 128))
# combined_features = extractor.extract_all_features(state_tensor)
import torch
import numpy as np
from scipy.ndimage import sobel, laplace, generic_filter
from scipy.stats import entropy, kurtosis, skew
from skimage.feature import greycomatrix, greycoprops

class StateFeatureExtractor:
    def __init__(self, device=None):
        self.device = device or torch.device('cpu')

    def edge_features(self, spatial_slice):
        sobel_x = sobel(spatial_slice, axis=0)
        sobel_y = sobel(spatial_slice, axis=1)
        laplacian = laplace(spatial_slice)

        features = [
            np.mean(np.abs(sobel_x)),
            np.mean(np.abs(sobel_y)),
            np.mean(np.abs(laplacian)),
            np.std(laplacian),
        ]
        return features

    def texture_features(self, spatial_slice):
        norm_slice = ((spatial_slice - spatial_slice.min()) / (spatial_slice.ptp() + 1e-6) * 255).astype(np.uint8)
        glcm = greycomatrix(norm_slice, distances=[1], angles=[0], levels=256, symmetric=True, normed=True)
        contrast = greycoprops(glcm, 'contrast')[0, 0]
        dissimilarity = greycoprops(glcm, 'dissimilarity')[0, 0]
        homogeneity = greycoprops(glcm, 'homogeneity')[0, 0]
        energy = greycoprops(glcm, 'energy')[0, 0]
        correlation = greycoprops(glcm, 'correlation')[0, 0]

        return [contrast, dissimilarity, homogeneity, energy, correlation]

    def spatial_entropy(self, spatial_slice, neighborhood_size=5):
        def local_entropy(block):
            hist, _ = np.histogram(block, bins=10, range=(block.min(), block.max()))
            hist = hist / (hist.sum() + 1e-6)
            return entropy(hist + 1e-6)

        ent = generic_filter(spatial_slice, local_entropy, size=neighborhood_size)
        return [np.mean(ent), np.std(ent)]

    def extract_global_features(self, state_tensor):
        state_np = state_tensor.cpu().numpy()  # shape: (10, 128, 128)
        flattened = state_np[state_np != 0]  # only non-zero values

        # Overall statistical features on non-zeros
        total_bytes = np.sum(flattened)
        mean_throughput = np.mean(flattened)
        std_throughput = np.std(flattened)
        max_throughput = np.max(flattened)
        min_throughput = np.min(flattened)
        median_throughput = np.median(flattened)
        cv_throughput = std_throughput / (mean_throughput + 1e-6)
        kurt = kurtosis(flattened)
        skewness = skew(flattened)

        # Trend features over time
        throughput_slope = (np.sum(state_np[-1]) - np.sum(state_np[0])) / 10

        # Temporal windowed features (e.g., per-slice means)
        window_means = np.mean(state_np, axis=(1, 2))  # shape (10,)
        window_mean_of_means = np.mean(window_means)
        window_std_of_means = np.std(window_means)
        window_cv_of_means = window_std_of_means / (window_mean_of_means + 1e-6)

        nonzero_flows = np.count_nonzero(state_np)

        return [
            total_bytes, mean_throughput, std_throughput, max_throughput,
            min_throughput, median_throughput, cv_throughput, kurt, skewness,
            throughput_slope, window_mean_of_means, window_std_of_means, window_cv_of_means,
            nonzero_flows
        ]

    def statistical_features(self, spatial_slice):
        flattened = spatial_slice.flatten()
        nonzero_vals = flattened[flattened != 0]
        if len(nonzero_vals) == 0:
            return [0.0, 0.0, 0.0, 0.0]
        kurt_val = kurtosis(nonzero_vals)
        skew_val = skew(nonzero_vals)
        std_val = np.std(nonzero_vals)
        mean_val = np.mean(nonzero_vals)
        return [mean_val, std_val, kurt_val, skew_val]

    def extract_all_features(self, state_tensor):
        state_np = state_tensor.cpu().numpy()  # shape: (10, 128, 128)

        edge_feats_all = []
        texture_feats_all = []
        entropy_feats_all = []
        statistical_feats_all = []

        for interval_slice in state_np:
            edge_feats_all.append(self.edge_features(interval_slice))
            texture_feats_all.append(self.texture_features(interval_slice))
            entropy_feats_all.append(self.spatial_entropy(interval_slice))
            statistical_feats_all.append(self.statistical_features(interval_slice))

        edge_feats = np.mean(edge_feats_all, axis=0).tolist() + np.std(edge_feats_all, axis=0).tolist()
        texture_feats = np.mean(texture_feats_all, axis=0).tolist() + np.std(texture_feats_all, axis=0).tolist()
        entropy_feats = np.mean(entropy_feats_all, axis=0).tolist()
        statistical_feats = np.mean(statistical_feats_all, axis=0).tolist() + np.std(statistical_feats_all, axis=0).tolist()
        global_feats = self.extract_global_features(state_tensor)

        all_feats = global_feats + edge_feats + texture_feats + entropy_feats + statistical_feats
        return torch.tensor(all_feats, dtype=torch.float32, device=self.device)


