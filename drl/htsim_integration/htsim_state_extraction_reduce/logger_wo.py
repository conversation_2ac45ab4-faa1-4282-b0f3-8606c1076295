



import os
import logging
from datetime import datetime

import json


class Logger:
    """
    Centralized logger that provides a unique logger instance for each class.
    Supports per-class logging, per-run log separation, and a dynamic `cm` field update.
    """
    LOG_LEVEL = "SILENT"
    LOG_LEVEL = os.getenv("LOG_LEVEL", "DEBUG").upper()  # Global log level management
    GLOBAL_CM = None  # Allows cm to be set from main class and used across all loggers

    @classmethod
    def update_global_cm(cls, cm_value):
        """Updates the global cm field across all logger instances."""
        cls.GLOBAL_CM = cm_value

    def __init__(self, class_name, config):
        """
        "/home/<USER>/POC/drl/htsim_integration/logs"
        Initializes the logger for a given class, ensuring logs are created per execution run.

        Args:
            class_name (str): Name of the class using the logger.
            log_dir (str): Directory where logs will be stored.
        """
        # with open(config_file, 'r') as f:
        #     self.config = json.load(f)
        self.class_name = class_name
        self.log_dir = config["file_env"]["log_dir"]
        os.makedirs(self.log_dir, exist_ok=True)  # Ensure log directory exists

        # Initialize the logger instance
        self.logger = self._setup_logger()

    def _setup_logger(self):
        """Creates and returns a logger instance with a file name format: class_cm.log."""
        cm_suffix = f"_{Logger.GLOBAL_CM}" if Logger.GLOBAL_CM else ""  # Add cm only if set
        log_file_name = f"{self.class_name}{cm_suffix}.log"  # No timestamp
        log_file_path = os.path.join(self.log_dir, log_file_name)

        # Create a logger specific to this class
        logger = logging.getLogger(f"{self.class_name}{cm_suffix}")
        logger.setLevel(getattr(logging, self.LOG_LEVEL, logging.DEBUG))

        # Clear previous handlers
        if logger.hasHandlers():
            logger.handlers.clear()

        # File handler for writing logs
        file_handler = logging.FileHandler(log_file_path, mode='w')
        file_handler.setFormatter(logging.Formatter("%(asctime)s - %(levelname)s - %(message)s"))
        logger.addHandler(file_handler)

        logger.propagate = False  # Ensure logs are not duplicated

        return logger

    def get_logger(self):
        """Returns the logger instance."""
        return self.logger

