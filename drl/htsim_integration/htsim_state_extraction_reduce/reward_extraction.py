# import re
# import matplotlib.pyplot as plt

# # Path to your log file
# log_file_path = "path/to/your/logfile.log"  # Replace with your actual path

# # Regex pattern to extract reward values
# reward_pattern = re.compile(r"🏆 Reward:\s*([0-9.]+)")

# # Read file and extract reward values
# rewards = []
# with open(log_file_path, 'r') as f:
#     for line in f:
#         match = reward_pattern.search(line)
#         if match:
#             rewards.append(float(match.group(1)))

# # Plot the rewards
# plt.figure(figsize=(10, 5))
# plt.plot(rewards, marker='o')
# plt.title("Reward per Iteration")
# plt.xlabel("Iteration")
# plt.ylabel("Reward")
# plt.grid(True)
# plt.tight_layout()
# plt.show()
# import os
# import re
# import matplotlib.pyplot as plt

# def extract_rewards_from_file(file_path):
#     """Extract reward values from a single file."""
#     reward_pattern = re.compile(r"🏆 Reward:\s*([0-9.]+)")
#     rewards = []
#     with open(file_path, 'r') as file:
#         for line in file:
#             match = reward_pattern.search(line)
#             if match:
#                 rewards.append(float(match.group(1)))
#     return rewards

# def plot_and_save_rewards(folder_path):
#     """Plot and save rewards for each Runner_* file and a combined plot."""
#     all_rewards = {}  # filename -> list of rewards

#     # Create 'plots' folder if it doesn't exist
#     plots_folder = os.path.join(folder_path, "plots")
#     os.makedirs(plots_folder, exist_ok=True)

#     # Extract and plot individually
#     for file_name in sorted(os.listdir(folder_path)):
#         if file_name.startswith("Runner_") and file_name.endswith(".log"):
#             full_path = os.path.join(folder_path, file_name)
#             rewards = extract_rewards_from_file(full_path)
#             if rewards:
#                 all_rewards[file_name] = rewards

#                 # Plot and save individual file
#                 plt.figure(figsize=(10, 4))
#                 plt.plot(rewards, marker='o')
#                 plt.title(f"Rewards: {file_name}")
#                 plt.xlabel("Iteration")
#                 plt.ylabel("Reward")
#                 plt.grid(True)
#                 plt.tight_layout()
#                 plt.savefig(os.path.join(plots_folder, file_name.replace(".log", ".png")))
#                 plt.close()

#     # Plot and save combined plot
#     if all_rewards:
#         plt.figure(figsize=(12, 6))
#         for file_name, rewards in all_rewards.items():
#             plt.plot(rewards, label=file_name)
#         plt.title("Reward per Iteration (All Files)")
#         plt.xlabel("Iteration")
#         plt.ylabel("Reward")
#         plt.legend(loc='best', fontsize="small")
#         plt.grid(True)
#         plt.tight_layout()
#         plt.savefig(os.path.join(plots_folder, "all_rewards.png"))
#         plt.close()



# # Example usage: replace with your actual path
# folder_path = "/mnt/disks/disk1/train_128_23_5_re_thr_fairnes_0"  # <- update this line
# plot_and_save_rewards(folder_path)
import os
import re
import matplotlib.pyplot as plt

def extract_rewards_from_file(file_path):
    """Extract reward values from a single file."""
    reward_pattern = re.compile(r"🏆 Reward:\s*(-?[0-9.]+)")

    rewards = []
    with open(file_path, 'r') as file:
        for line in file:
            match = reward_pattern.search(line)
            if match:
                rewards.append(float(match.group(1)))
    return rewards

def plot_and_save_rewards(folder_path):
    """Plot individual rewards and a single concatenated reward curve."""
    all_rewards = {}      # filename -> list of rewards
    concat_rewards = []   # single list of all rewards

    # Create 'plots' subfolder
    plots_folder = os.path.join(folder_path, "plots")
    os.makedirs(plots_folder, exist_ok=True)

    # Process each log file
    for file_name in sorted(os.listdir(folder_path)):
        if file_name.startswith("Runner_") and file_name.endswith(".log"):
            full_path = os.path.join(folder_path, file_name)
            rewards = extract_rewards_from_file(full_path)

            if rewards:
                all_rewards[file_name] = rewards
                concat_rewards.extend(rewards)

                # Plot individual reward curve
                plt.figure(figsize=(10, 5))
                plt.plot(rewards, marker='o')
                plt.title(f"Rewards: {file_name}", fontsize=12)
                plt.xlabel("Iteration")
                plt.ylabel("Reward")
                plt.grid(True)
                plt.subplots_adjust(top=0.85, bottom=0.15)
                plt.savefig(os.path.join(plots_folder, file_name.replace(".log", ".png")))
                plt.close()

    # Plot concatenated reward curve
    if concat_rewards:
        plt.figure(figsize=(12, 6))
        plt.plot(concat_rewards, marker='o')
        plt.title("Concatenated Rewards from All Files", fontsize=14)
        plt.xlabel("Global Iteration")
        plt.ylabel("Reward")
        plt.grid(True)
        plt.subplots_adjust(top=0.85, bottom=0.15)
        plt.savefig(os.path.join(plots_folder, "concatenated_rewards.png"))
        plt.close()

# Example usage
# folder_path = "/mnt/disks/disk1/train_128_26_5_new_Rew__geometric_mean_per_group_0"  # <- update this line
# folder_path = "/mnt/disks/disk1/train_28_5_0_rew_fair_gm_th_by_maxtrh_throu_action_scpase_0"  # <- update this line
# folder_path = "/mnt/disks/disk1/train_gemean_per_grop_collect_full_data0_longer"  # <- update this line
folder_path = "/mnt/disks/disk1/train_16_6_state_zero__0"  # <- update this line

plot_and_save_rewards(folder_path)
