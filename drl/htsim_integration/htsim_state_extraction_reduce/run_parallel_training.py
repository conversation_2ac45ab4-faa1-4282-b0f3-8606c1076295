# run_parallel_training.py

import multiprocessing
import os
from drl.htsim_integration.htsim_state_extraction_reduce.train_and_test_state_extraction_reduce import main_train  # or wherever main_train is defined

def run_job(i):
    config_path = f"/home/<USER>/POC/drl/htsim_integration/generated_configs/config_run_{i}.json"
    print(f"🚀 Launching config {i} from {config_path}")
    try:
        main_train( config_path)
    except Exception as e:
        print(f"❌ Process {i} failed: {e}")

if __name__ == "__main__":
    num_parallel_jobs = 2  # You have config_run_0.json to config_run_5.json
    indices = list(range(num_parallel_jobs))

    with multiprocessing.Pool(processes=num_parallel_jobs) as pool:
        pool.map(run_job, indices)
