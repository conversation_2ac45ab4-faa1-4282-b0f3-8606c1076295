# import torch
# import torch.nn as nn
# import logging
# from logger_wo import Logger

# class FileModel(nn.Module):
#     def __init__(self, config_file, state_dim: int, action_dim: int, hidden_sizes: list, dropout_rate: float = 0.2):
#         super(FileModel, self).__init__()
#         self.class_name = self.__class__.__name__
#         self.logger_agent = Logger(self.class_name, config_file).get_logger()
#         self.logger_agent.info("Q(state, action) model initialized.")

#         # input_dim = state_dim + action_dim  # because we'll concatenate state and action
#         self.input_dim_expected = state_dim + action_dim  # store for later validation
#         input_dim = self.input_dim_expected
#         self.logger_agent.info(f"input_dim {input_dim}.")
#         layers = []
#         for idx, hidden_size in enumerate(hidden_sizes):
#             layers.append(nn.Linear(input_dim, hidden_size))
#             layers.append(nn.ReLU())
#             layers.append(nn.Dropout(p=dropout_rate))
#             input_dim = hidden_size

#         layers.append(nn.Linear(input_dim, 1))  # Output: scalar Q-value

#         self.network = nn.Sequential(*layers)

#     def forward(self, state, action):
#         try:
#             self.logger_agent.debug(f"🚀 FORWARD START")

#             self.logger_agent.debug(f"state type: {type(state)}, action type: {type(action)}")
#             self.logger_agent.debug(f"state shape: {state.shape}, action shape: {action.shape}")

#             batch_size = state.shape[0]
#             assert action.shape[0] == batch_size, f"❌ Mismatched batch sizes: state={state.shape[0]} action={action.shape[0]}"

#             total_input_dim = state.shape[1] + action.shape[1]
#             self.logger_agent.debug(f"Combined input dimension: {total_input_dim}")
#             self.logger_agent.debug(f"Expected input dimension: {self.input_dim_expected}")

#             assert total_input_dim == self.input_dim_expected, (
#                 f"❌ Dimension mismatch: got {total_input_dim}, expected {self.input_dim_expected}"
#             )

#             combined = torch.cat([state, action], dim=-1)
#             self.logger_agent.debug(f"Combined tensor shape: {combined.shape}")

#             q_value = self.network(combined).squeeze(-1)
#             self.logger_agent.debug(f"✅ Q-values shape: {q_value.shape}")

#             return q_value

#         except Exception as e:
#             self.logger_agent.error(f"💥 CRASH in forward pass: {e}")
#             self.logger_agent.error(f"Saving debug tensors for post-mortem...")

#             torch.save(state.cpu(), "crash_state.pt")
#             torch.save(action.cpu(), "crash_action.pt")

#             self.logger_agent.error("Saved: crash_state.pt, crash_action.pt")
#             raise

# File: FileModel_wo.py
import torch
import torch.nn as nn
from logger_wo import Logger

class FileModel(nn.Module):
    def __init__(self, config_file, state_dim: int, action_dim: int, hidden_sizes: list, dropout_rate: float = 0.2):
        super(FileModel, self).__init__()
        self.class_name = self.__class__.__name__
        self.logger_agent = Logger(self.class_name, config_file).get_logger()
        self.logger_agent.info("📦 RLCC-inspired Q(state, action) model initialized.")
        self.logger_agent.info(f"📐 State dim: {state_dim}, Action dim: {action_dim}, Hidden sizes: {hidden_sizes}")

        input_size = state_dim #+ action_dim
        rnn_hidden_size = hidden_sizes[0]  # First element is GRU size
        self.logger_agent.info(f"🔁 Initializing GRU with input size {input_size} and hidden size {rnn_hidden_size}")
        self.gru = nn.GRU(input_size=input_size, hidden_size=rnn_hidden_size, batch_first=True)

        # MLP head (same as RLCC: two layers with ReLU + dropout)
        mlp_layers = []
        current_dim = rnn_hidden_size
        for i, hidden_size in enumerate(hidden_sizes[1:], 1):
            self.logger_agent.info(f"🔧 Adding MLP layer {i}: Linear({current_dim} → {hidden_size}) + ReLU + Dropout({dropout_rate})")
            mlp_layers.append(nn.Linear(current_dim, hidden_size))
            mlp_layers.append(nn.ReLU())
            mlp_layers.append(nn.Dropout(p=dropout_rate))
            current_dim = hidden_size

        # mlp_layers.append(nn.Linear(current_dim, 1))  # Q-value output
        self.fc = nn.Sequential(*mlp_layers)
        self.value_head = nn.Linear(current_dim, 1)
        self.advantage_head = nn.Linear(current_dim, action_dim)

        # After initializing self.fc
        total_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        self.logger_agent.info(f"📊 Total trainable parameters: {total_params}")
        self.logger_agent.info("📚 MLP layer structure:")
        for i, layer in enumerate(self.fc):
            if isinstance(layer, nn.Linear):
                self.logger_agent.info(f"  Layer {i}: Linear({layer.in_features} → {layer.out_features})")

        self.logger_agent.info("✅ Model initialized successfully.")

    def forward(self, state):
        try:
            self.logger_agent.debug("🚀 Forward pass started.")
            self.logger_agent.debug(f"🧮 State shape: {state.shape}")

            if state.dim() != 3:
                raise ValueError(f"Expected 3D input (B, T, D) for GRU, got {state.shape}")

            state = state.float()
            gru_out, _ = self.gru(state)
            self.logger_agent.debug(f"🌀 GRU output shape: {gru_out.shape}")

            last_hidden = gru_out[:, -1, :]
            self.logger_agent.debug(f"📤 Last hidden state shape: {last_hidden.shape}")

            x = self.fc(last_hidden)

            # Dueling streams
            value = self.value_head(x)           # (B, 1)
            advantage = self.advantage_head(x)   # (B, action_dim)

            q_values = value + (advantage - advantage.mean(dim=1, keepdim=True))
            self.logger_agent.debug(f"🎯 Q-values output shape: {q_values.shape}")

            return q_values  # (B, action_dim)

        except Exception as e:
            self.logger_agent.error(f"🔥 Crash in forward: {e}")
            torch.save(state.cpu(), "crash_state.pt")
            raise


    def forward_old(self, state, action):
        try:
            self.logger_agent.debug("🚀 Forward pass started.")
            self.logger_agent.debug(f"🧮 State shape: {state.shape}, Action shape: {action.shape}")

            if state.dim() == 3:
                x = torch.cat([state, action], dim=-1)  # (B, T, D)
                self.logger_agent.debug(f"🔗 Concatenated input (RNN mode) shape: {x.shape}")
                x = x.float()
                gru_out, _ = self.gru(x)
                self.logger_agent.debug(f"🌀 GRU output shape: {gru_out.shape}")
                last_hidden = gru_out[:, -1, :]
                self.logger_agent.debug(f"📤 Last hidden state shape: {last_hidden.shape}")
                q_value = self.fc(last_hidden).squeeze(-1)
            else:
                x = torch.cat([state, action], dim=-1)
                self.logger_agent.debug(f"🔗 Concatenated input (MLP mode) shape: {x.shape}")
                q_value = self.fc(x).squeeze(-1)

            self.logger_agent.debug(f"🎯 Q-value output shape: {q_value.shape}")
            return q_value

        except Exception as e:
            self.logger_agent.error(f"🔥 Crash in forward: {e}")
            self.logger_agent.error("💾 Saving tensors for debugging...")
            torch.save(state.cpu(), "crash_state.pt")
            torch.save(action.cpu(), "crash_action.pt")
            raise
