
import faiss
import numpy as np
import torch
import logging
import gc


class PFCVectorDatabase:
    """
    A FAISS-based vector database for storing states and retrieving similar states
    with their best PFC values and rewards.
    """

    def __init__(self, state_size, similarity_threshold=0.9, k=5, max_states=10000):
        """
        - state_size: The size of the state vector (flattened shape).
        - similarity_threshold: Minimum similarity required for a match.
        - k: Number of nearest neighbors to consider.
        - max_states: Maximum number of stored states to avoid memory overflow.
        """
        self.state_size = state_size
        self.similarity_threshold = similarity_threshold
        self.k = k
        self.max_states = max_states

        # ✅ Use an ID mapping index for better management
        self.index = faiss.IndexIDMap(faiss.IndexFlatL2(int(state_size)))

        # ✅ Dictionary to store PFC values and rewards by ID
        self.pfc_values = {}
        self.reward_values = {}

        self.next_id = 0  # Unique ID counter for FAISS

    def add(self, state, pfc_value, reward):
        """Stores a state vector with its associated best PFC value and reward, avoiding duplicate inserts."""

        # ✅ Convert PyTorch tensor to NumPy if necessary
        if isinstance(state, torch.Tensor):
            state = state.cpu().detach().numpy()

        state_vector = np.array(state, dtype=np.float16).reshape(1, -1)

        # ✅ Enforce max state limit to avoid memory overflow
        if self.index.ntotal >= self.max_states:
            print(f"⚠️ FAISS database full ({self.max_states} states), pruning...")
            self._prune_low_reward_states(keep_fraction=0.5)
            gc.collect()

        # ✅ Check if a similar state already exists
        if self.index.ntotal > 0:
            distances, indices = self.index.search(state_vector, self.k)
            for i, dist in zip(indices[0], distances[0]):
                if i >= 0 and dist < self.similarity_threshold:
                    # ✅ Update existing entry if reward is better
                    if reward > self.reward_values[i]:
                        self.pfc_values[i] = pfc_value
                        self.reward_values[i] = reward
                    return  # Avoid duplicate insertion

        # ✅ Store new state with a unique ID
        self.index.add_with_ids(state_vector, np.array([self.next_id], dtype=np.int64))
        self.pfc_values[self.next_id] = pfc_value
        self.reward_values[self.next_id] = reward
        self.next_id += 1  # Increment unique ID

    def _prune_low_reward_states(self, keep_fraction=0.5):
        if self.index.ntotal == 0:
            return

        all_ids = list(self.reward_values.keys())
        sorted_ids = sorted(all_ids, key=lambda i: self.reward_values[i])
        num_to_remove = int(len(sorted_ids) * (1 - keep_fraction))
        ids_to_remove = sorted_ids[:num_to_remove]

        remaining_ids = sorted_ids[num_to_remove:]
        remaining_vectors = []
        new_id_map = {}
        new_id = 0

        for old_id in remaining_ids:
            try:
                vec = self.index.reconstruct(old_id)
                remaining_vectors.append(vec)
                new_id_map[old_id] = new_id
                new_id += 1
            except RuntimeError:
                continue

        self.index.reset()
        self.pfc_values = {new_id_map[i]: self.pfc_values[i] for i in remaining_ids if i in new_id_map}
        self.reward_values = {new_id_map[i]: self.reward_values[i] for i in remaining_ids if i in new_id_map}
        self.index.add_with_ids(np.array(remaining_vectors, dtype=np.float16), np.array(list(new_id_map.values())))
        self.next_id = max(new_id_map.values(), default=-1) + 1

        print(f"✅ Pruned {num_to_remove} entries. Remaining: {len(self.reward_values)}")

    def get_best_pfc_with_reward(self, state):
        """
        Retrieves the k-nearest states using FAISS and returns:
        - Their best PFC values
        - Their corresponding rewards

        Returns:
        - List of (pfc, reward) tuples (empty list if no match is found)
        """
        # ✅ Convert PyTorch tensor to NumPy if necessary
        if isinstance(state, torch.Tensor):
            state = state.cpu().detach().numpy()

        state_vector = np.array(state, dtype=np.float16).reshape(1, -1)

        if self.index.ntotal == 0:
            return []  # ✅ Return empty list instead of None

        # ✅ Find k closest states
        distances, indices = self.index.search(state_vector, self.k)
        # logging.info(f"get_best_pfc_with_reward distances {distances} .")
        # ✅ Collect (PFC, reward) pairs for similar states
        similar_states = [
            (self.pfc_values[i], self.reward_values[i])
            for i, dist in zip(indices[0], distances[0])
            if i >= 0 and dist < self.similarity_threshold
        ]
        
        return similar_states  # ✅ Always returns a list (even if empty)
