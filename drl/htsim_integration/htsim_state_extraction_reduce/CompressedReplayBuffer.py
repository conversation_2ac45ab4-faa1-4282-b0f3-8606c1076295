import numpy as np
import random
import io
import zipfile
from logger_wo import Logger

class CompressedRNNReplayBuffer:
    def __init__(self, buffer_size, sequence_len, config):
        self.buffer_size = buffer_size
        self.sequence_len = sequence_len
        self.states = []
        self.actions = []
        self.rewards = []
        self.next_states = []
        self.next_actions = []
        self.dones = []
        self.position = 0
        self.config = config
        self.logger = Logger("CompressedRNNReplayBuffer", self.config).get_logger()
        self.logger.info("CompressedRNNReplayBuffer initialized.")

    def __len__(self):
        return len(self.states)

    def _compress(self, array):
        buffer = io.BytesIO()
        np.savez_compressed(buffer, array=array)
        return buffer.getvalue()

    def _decompress(self, compressed_bytes):
        buffer = io.BytesIO(compressed_bytes)
        with np.load(buffer) as data:
            return data['array']

    def add(self, state, action, reward, next_state, next_action, done):
        compressed_state = self._compress(state)
        compressed_next_state = self._compress(next_state)

        if len(self.states) < self.buffer_size:
            self.states.append(compressed_state)
            self.actions.append(np.array(action, dtype=np.float16))
            self.rewards.append(np.float16(reward))
            self.next_states.append(compressed_next_state)
            self.next_actions.append(np.array(next_action, dtype=np.float16))
            self.dones.append(bool(done))
        else:
            self.states[self.position] = compressed_state
            self.actions[self.position] = np.array(action, dtype=np.float16)
            self.rewards[self.position] = np.float16(reward)
            self.next_states[self.position] = compressed_next_state
            self.next_actions[self.position] = np.array(next_action, dtype=np.float16)
            self.dones[self.position] = bool(done)

        self.position = (self.position + 1) % self.buffer_size
        self.logger.info(f"📦 Buffer size: {len(self.states)} / {self.buffer_size}")

    def sample(self, batch_size):
        max_start_index = len(self.states) - self.sequence_len
        valid_indices = list(range(max_start_index))
        self.logger.info(f"🔎 Valid sequences available: {len(valid_indices)} / {len(self.states)}")

        if len(valid_indices) < batch_size:
            raise ValueError("Not enough valid sequences in compressed RNN buffer.")

        indices = random.sample(valid_indices, batch_size)

        # 🏎️ Decompress all needed states at once
        decompressed_states = [self._decompress(s) for s in self.states]
        decompressed_next_states = [self._decompress(s) for s in self.next_states]

        state_seqs = np.array([
            [decompressed_states[i+t] for t in range(self.sequence_len)]
            for i in indices
        ], dtype=np.float16)

        action_seqs = np.array([
            [self.actions[i+t] for t in range(self.sequence_len)]
            for i in indices
        ], dtype=np.float16)

        reward_final = np.array([
            self.rewards[i + self.sequence_len - 1] for i in indices
        ], dtype=np.float16)

        next_state_seqs = np.array([
            [decompressed_next_states[i+t] for t in range(self.sequence_len)]
            for i in indices
        ], dtype=np.float16)

        next_action_seqs = np.array([
            [self.next_actions[i+t] for t in range(self.sequence_len)]
            for i in indices
        ], dtype=np.float16)

        done_final = np.array([
            self.dones[i + self.sequence_len - 1] for i in indices
        ], dtype=bool)

        return state_seqs, action_seqs, reward_final, next_state_seqs, next_action_seqs, done_final


    def sample_o(self, batch_size):
        max_start_index = len(self.states) - self.sequence_len
        valid_indices = list(range(max_start_index))
        self.logger.info(f"🔎 Valid sequences available: {len(valid_indices)} / {len(self.states)}")

        if len(valid_indices) < batch_size:
            raise ValueError("Not enough valid sequences in compressed RNN buffer.")

        indices = random.sample(valid_indices, batch_size)

        # decompress when building batch
        state_seqs = np.array([
            [self._decompress(self.states[i+t]) for t in range(self.sequence_len)]
            for i in indices
        ], dtype=np.float16)

        action_seqs = np.array([
            [self.actions[i+t] for t in range(self.sequence_len)]
            for i in indices
        ], dtype=np.float16)

        reward_final = np.array([
            self.rewards[i + self.sequence_len - 1] for i in indices
        ], dtype=np.float16)

        next_state_seqs = np.array([
            [self._decompress(self.next_states[i+t]) for t in range(self.sequence_len)]
            for i in indices
        ], dtype=np.float16)

        next_action_seqs = np.array([
            [self.next_actions[i+t] for t in range(self.sequence_len)]
            for i in indices
        ], dtype=np.float16)

        done_final = np.array([
            self.dones[i + self.sequence_len - 1] for i in indices
        ], dtype=bool)

        return state_seqs, action_seqs, reward_final, next_state_seqs, next_action_seqs, done_final
