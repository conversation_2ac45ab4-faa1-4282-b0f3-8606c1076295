import torch
import torch.nn as nn
import traceback
from logger_wo import Logger
import torch.nn as nn
import torch

class FileModel(nn.Module):
    def __init__(self, config_file, state_dim, action_dim, hidden_sizes, dropout_rate=0.2):
        super(FileModel, self).__init__()
        self.class_name = self.__class__.__name__
        self.logger_agent = Logger(self.class_name, config_file).get_logger()
        input_size = state_dim + action_dim
        rnn_hidden_size = hidden_sizes[0]

        self.gru = nn.GRU(input_size=input_size, hidden_size=rnn_hidden_size, batch_first=True)

        # Shared MLP trunk
        mlp_layers = []
        current_dim = rnn_hidden_size
        for hidden_size in hidden_sizes[1:]:
            mlp_layers.append(nn.Linear(current_dim, hidden_size))
            mlp_layers.append(nn.ReLU())
            mlp_layers.append(nn.Dropout(dropout_rate))
            current_dim = hidden_size

        self.shared_mlp = nn.Sequential(*mlp_layers)

        # Dueling heads
        self.value_stream = nn.Sequential(
            nn.Linear(current_dim, 1)
        )

        self.advantage_stream = nn.Sequential(
            nn.Linear(current_dim, action_dim)
        )

    def forward(self, state, action):
        if state.dim() == 3:
            x = torch.cat([state, action], dim=-1)
            gru_out, _ = self.gru(x)
            x = gru_out[:, -1, :]
        else:
            x = torch.cat([state, action], dim=-1)

        x = self.shared_mlp(x)

        value = self.value_stream(x)
        advantage = self.advantage_stream(x)

        # Combine value and advantage
        q_value = value + (advantage - advantage.mean(dim=1, keepdim=True))

        return q_value

# class FileModel(nn.Module):
#     def __init__(self, config_file, state_dim: int, action_dim: int, hidden_sizes: list,
#                  dropout_rate: float = 0.2, rnn_hidden_size: int = 64):
#         super(FileModel, self).__init__()
#         self.class_name = self.__class__.__name__
#         self.logger_agent = Logger(self.class_name, config_file).get_logger()

#         self.state_dim = state_dim
#         self.action_dim = action_dim
#         self.input_dim = self.state_dim + self.action_dim
#         self.rnn_hidden_size = rnn_hidden_size

#         self.logger_agent.info("📦 RLCC-inspired Q(state, action) model initialized.")
#         self.logger_agent.info(f"📐 State dim: {state_dim}, Action dim: {action_dim}, Hidden sizes: {hidden_sizes}")
#         self.logger_agent.info(f"🔁 Initializing GRU with input size {self.input_dim} and hidden size {rnn_hidden_size}")

#         self.gru = nn.GRU(input_size=self.input_dim, hidden_size=rnn_hidden_size, batch_first=True)

#         # Build MLP head
#         layers = []
#         current_dim = rnn_hidden_size
#         for idx, hidden_size in enumerate(hidden_sizes):
#             self.logger_agent.info(f"🔧 Adding MLP layer {idx + 1}: Linear({current_dim} → {hidden_size}) + ReLU + Dropout({dropout_rate})")
#             layers.append(nn.Linear(current_dim, hidden_size))
#             layers.append(nn.ReLU())
#             layers.append(nn.Dropout(p=dropout_rate))
#             current_dim = hidden_size

#         self.logger_agent.info(f"🧩 Adding Output Layer: Linear({current_dim} → 1)")
#         layers.append(nn.Linear(current_dim, 1))

#         self.fc = nn.Sequential(*layers)
#         self.logger_agent.info("✅ Model initialized successfully.")

#     def forward(self, state, action):
#         try:
#             self.logger_agent.debug("🚀 Forward pass started.")
#             self.logger_agent.debug(f"🧮 State shape: {state.shape}, Action shape: {action.shape}")

#             if state.dim() != 3 or action.dim() != 3:
#                 msg = f"❌ Invalid input dims. RNN expects 3D tensors. Got state {state.shape}, action {action.shape}"
#                 self.logger_agent.error(msg)
#                 raise ValueError(msg)

#             combined = torch.cat([state, action], dim=-1)
#             self.logger_agent.debug(f"🔗 Concatenated input (RNN mode) shape: {combined.shape}")

#             if combined.shape[-1] != self.input_dim:
#                 msg = f"❌ Mismatch in input dim. Got {combined.shape[-1]}, expected {self.input_dim} (state_dim={self.state_dim} + action_dim={self.action_dim})"
#                 self.logger_agent.error(msg)
#                 raise ValueError(msg)

#             rnn_out, _ = self.gru(combined)
#             self.logger_agent.debug(f"🌀 GRU output shape: {rnn_out.shape}")

#             last_hidden = rnn_out[:, -1, :]
#             self.logger_agent.debug(f"📤 Last hidden state shape: {last_hidden.shape}")

#             q_value = self.fc(last_hidden).squeeze(-1)
#             self.logger_agent.debug(f"🎯 Q-value output shape: {q_value.shape}")

#             return q_value

#         except Exception as e:
#             self.logger_agent.error("🔥 Crash in forward pass:")
#             self.logger_agent.error(f"🧾 Exception: {e}")
#             self.logger_agent.error(traceback.format_exc())

#             try:
#                 torch.save(state.cpu(), "crash_state.pt")
#                 torch.save(action.cpu(), "crash_action.pt")
#                 self.logger_agent.error("💾 Saved tensors for debugging: crash_state.pt, crash_action.pt")
#             except Exception as save_e:
#                 self.logger_agent.error(f"❌ Failed to save tensors: {save_e}")

#             raise e
