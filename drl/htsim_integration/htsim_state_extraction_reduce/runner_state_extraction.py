
import subprocess
import time
from collections import defaultdict, deque
import numpy as np
import scipy.stats
import logging
import math 
import threading
import queue
import threading
import time
import psutil
from pathlib import Path
import os
import numpy as np
from logger_wo import Logger
from BufferDQNAgent_wo import BufferDQNAgent  # Your custom DRL Agent
import re
import glob
import traceback
# from PFCVectorDatabase_wo import *  # Import FAISS-based database
import scipy
import torch
import gc
import traceback
from queue import Empty
# from ECMPPathSelector import ECMPPathSelector

gc.collect()
torch.cuda.empty_cache()
torch.autograd.set_detect_anomaly(True)
import sys
np.set_printoptions(threshold=sys.maxsize, linewidth=200, precision=3)
def convert_cm_to_out(cm_filename):
    seed_part = cm_filename.split("_seed")[-1].replace(".cm", "")
    return f"roce128_incast_rand_flowsize_128_incastratio_3_seed{seed_part}.out"

# Normalize the input state for agent action
def normalize_state_for_agent(input_state):
    if isinstance(input_state, tuple):
        matrix, drop_count, reorder_count = input_state
        matrix = np.clip(matrix / 24000.0, 0.0, 1.0)
        return (matrix, drop_count, reorder_count)
    else:
        return np.clip(input_state / 24000.0, 0.0, 1.0)

def zero_state(state):
        if isinstance(state, tuple):
            matrix, drop_count, reorder_count = state
            return (np.zeros_like(matrix), drop_count, reorder_count)
        else:
            return np.zeros_like(state)

class Runner:
    def __init__(self, config, buffer_size=10, num_flows=16, num_runs=3, mode="train"):
        # Initialize parameters
        # with open(config_file, 'r') as f:
        self.valid=False
        self.config = config
        self.counter_replay=0
        self.finished_flow_tracker = None  # Will be initialized dynamically after destinations are known
        self.command= None
        self.mode = mode
        self.cm_file = None
        self.cm_file_valid_action=None#kiril
        self.buffer_size = buffer_size
        self.num_flows = num_flows
        self.num_runs = num_runs
        self.queue = queue.Queue(maxsize=100)
        self.stop_event = threading.Event()
        self.prev_reward = -1
        # self.pre_state = np.zeros((self.buffer_size, self.num_flows, self.num_flows), dtype=np.float64)
        self.pre_action = np.array([5, 5, 5], dtype=np.float64)
        self.process = None
        self.agent = None  # DRL Agent
        self.selector = None#ECMPPathSelector(num_flowss=12, hash_salt=209652396)
        self.roce_config_file = self.config['file_env']['roce_config_file']
        print(f"roce_config_file-{self.roce_config_file}")
        self.agent_config = self.config['file_agent_incast']
        self.action_type = config["file_agent_incast"]["action_type"]
        print(f"initilize the action tupe of runner-{self.action_type}")

        single_config = self.config["file_agent_incast"]["single_action_config"]
        self.state_extended = False
        if self.state_extended:
            empty_matrix = np.zeros((self.buffer_size, self.num_flows, self.num_flows), dtype=np.float64)
            self.pre_state = (empty_matrix, 0, 0)
        else:
            self.pre_state = np.zeros((self.buffer_size, self.num_flows, self.num_flows), dtype=np.float64)

        # Load reward function hyperparameters
        reward_config = self.config["file_agent_incast"]
        self.fairness_alpha = reward_config["fairness_alpha"]
        self.fluctuation_penalty_weight = reward_config["fluctuation_penalty_weight"]
        self.pfc_penalty_weight = reward_config["pfc_penalty_weight"]
        self.reward_discount = reward_config["reward_discount"]
        #self.mode =

        # Suppose q_values is [130]; just take the first element for queue size
        self.queue_size = single_config["q_values"][0]  # 130
        self.sleeping_time = self.agent_config["sleeping_time"]
        self.seed_config =  self.agent_config["seed_config"]
        self.alpha_rew =  self.agent_config["alpha_rew"]
        self.destinations = []
        self.flow_ids = [i for i in range(self.num_flows)]
        self.flow_activity = defaultdict(lambda: deque(maxlen=5))

        self.logger = None

        # # FAISS Vector Database for state-action storage
        # self.pfc_db = PFCVectorDatabase(state_size=int(self.buffer_size * self.num_flows * self.num_flows))

        self.moving_avg_fairness = 1.0  # Start from 1 for fairness calculation
        # self.prev_throughput = None  # Initialize for fluctuation penalty
        self.moving_avg_reward = 0.0  # Initialize reward smoothing


        print("Runner setup finished.")
    def is_action_valid(self, action):
        return action in self.cm_file_valid_action



    def get_valid_actions_for_file(self,  target_q):
        base_dir = "/home/<USER>/POC/csg-htsim/sim/datacenter/liortests/roce128/incast"
        
        file_name =convert_cm_to_out(self.cm_file)  #f"{base_prefix}_seed{seed}.out"
        
        file_path = os.path.join(base_dir, file_name)
        print(f"\n📄 Checking: {file_path}")
        print(f"🔎 Looking for q = {target_q}")

        pattern = re.compile(
            r"For q\s*=\s*(\d+).*?"
            r"pfc_thresholds\s+(\d+ \d+).*?"
            r"spine_pfc_thresholds\s+(\d+ \d+).*?"
            r"pfc_thresholds_1\s+(\d+ \d+).*?"
            r"ar_sticky_delta\s*=\s*(\d+)"
        )

        valid_actions = set()

        if not os.path.exists(file_path):
            print(f"⚠️ File not found: {file_path}")
            return []

        with open(file_path, 'r') as f:
            for line in f:
                match = pattern.search(line)
                if match:
                    q_val = int(match.group(1))
                    pfc_str = match.group(2)
                    spine_str = match.group(3)
                    pfc1_str = match.group(4)
                    ar_sticky = int(match.group(5))
                    # print(f"→ Found q={q_val}, pfc={pfc_str}, spine={spine_str}, pfc1={pfc1_str}, sticky={ar_sticky}")

                    if q_val == target_q:
                        pfc_val = int(pfc_str.split()[0])
                        pfc_spine = int(spine_str.split()[0])
                        valid_actions.add((pfc_val,pfc_spine, ar_sticky))
                        # print(f"✅ Valid action: ({pfc_val}, {ar_sticky})")

        
        
        return valid_actions


    def get_latest_model(self,directory):
        """Find the latest saved model in the directory based on modification time."""
        model_files = glob.glob(os.path.join(directory, "agent_checkpoint_*.zip"))
        if not model_files:
            return None  # No model found

        latest_model = max(model_files, key=os.path.getmtime)  # Get most recently modified file
        return latest_model

    def initialize_agent_and_env(self):
        """Initialize the agent and load the latest saved model if available."""
        if self.agent is None:
            if self.state_extended == True:

                matrix_list = np.zeros((self.buffer_size, self.num_flows, self.num_flows), dtype=np.float64)
                drop_count = 0
                reorder_count = 0
                state = (matrix_list, drop_count, reorder_count)
                
                self.agent = BufferDQNAgent(state, self.config)

            else:    
                state = np.zeros((self.buffer_size, self.num_flows, self.num_flows), dtype=np.float64)
            self.agent = BufferDQNAgent(state, self.config)
            if True:#self.mode == "train"
                # Find the latest model
                model_directory = self.config["file_env"]["MODEL_SAVE_DIR"]#"/home/<USER>/POC/drl/htsim_integration/agent_ch"#to do
                print(model_directory)
                latest_model = self.get_latest_model(model_directory)

                if latest_model:
                    self.agent.load_model(latest_model)
                    self.logger.info(f"Loaded latest agent model: {latest_model}")
                else:
                    self.logger.info("No saved model found, starting with a new agent.")





    def _update_config_file(self, action, config_file=None):
        """
        Update the RoCE text-based config file by replacing lines using regex.
        Action format assumed: [ PFC1, PFC2,AR]
        """
        self.logger.info(f"Update the file and the action is valid -{self.valid}")
        try:
            with open(self.roce_config_file, 'r') as file:
                lines = file.readlines()

            updated_lines = []
            ar_value = action[2]
            pfc1 = action[0]
            pfc2 = action[1]

            for line in lines:
                if re.match(r'^\s*"ar_sticky_delta"\s*:', line):
                    updated_lines.append(f'  "ar_sticky_delta": {ar_value},\n')
                elif re.match(r'^\s*"pfc_low_threshold"\s*:', line):
                    updated_lines.append(f'  "pfc_low_threshold": {pfc1},\n')
                elif re.match(r'^\s*"pfc_high_threshold"\s*:', line):
                    updated_lines.append(f'  "pfc_high_threshold": {pfc1 + 1},\n')

                elif re.match(r'^\s*"pfc_low_threshold_1"\s*:', line):
                        updated_lines.append(f'  "pfc_low_threshold_1": {pfc1},\n')
                elif re.match(r'^\s*"pfc_high_threshold_1"\s*:', line):
                        updated_lines.append(f'  "pfc_high_threshold_1": {pfc1 + 1},\n')

                elif re.match(r'^\s*"spine_pfc_low_threshold"\s*:', line):
                    updated_lines.append(f'  "spine_pfc_low_threshold": {pfc2},\n')
                elif re.match(r'^\s*"spine_pfc_high_threshold"\s*:', line):
                    updated_lines.append(f'  "spine_pfc_high_threshold": {pfc2 + 1},\n')
                else:
                    updated_lines.append(line)

            # Write back the modified config
            with open(self.roce_config_file, 'w') as file:
                file.writelines(updated_lines)

            self.logger.info(f"✅ Updated config: AR={ar_value}, PFC1={pfc1}, PFC2={pfc2}")

        except Exception as e:
            self.logger.error(f"❌ Error updating text-based config file: {e}")







    def prepare_state_for_agent_n(self, flow_dict_sequence, drop_count):
        """
        Prepare the state (flow-wise) and take action.
        """
        self.logger.info("Prepare the state and take action.")
        if not flow_dict_sequence:
            self.logger.error("❌ State is empty or None in prepare_state_for_agent!")
            return
        self.logger.info(f"flow_dict_sequence: {flow_dict_sequence}")
        try:
            features = self.agent.extract_features(flow_dict_sequence)
        except Exception as e:
            self.logger.error(f"❌ Feature extraction failed: {e}")
            return

        try:
            action = self.agent.act(features)
        except Exception as e:
            self.logger.error(f"❌ Action selection failed: {e}")
            return

        try:
            reward = self.calculate_smoothness_vdb(flow_dict_sequence, action)
            if isinstance(reward, float) and (np.isnan(reward) or np.isinf(reward)):
                self.logger.error(f"❌ Invalid reward value: {reward}")
                return
        except Exception as e:
            self.logger.error(f"❌ Reward calculation failed: {e}")
            return

        self.valid = True
        self.logger.info(f"🏆 Reward: {reward:.6f}, Selected Action: {action}, Valid: {self.valid}")

        if self.valid:
            self._update_config_file(action)

        # Store transition (if training)
        if self.mode == "train":
            try:
                if self.pre_state is not None:
                    prev_features = self.agent.extract_features(self.pre_state)
                    self.agent.remember(prev_features, self.pre_action, reward, features, action, True)
            except Exception as e:
                self.logger.error(f"❌ Error during remember(): {e}")

            if self.counter_replay == 5:
                try:
                    self.agent.replay()
                except Exception as e:
                    self.logger.error(f"❌ Replay failed: {e}")
                self.counter_replay = 0
            self.counter_replay += 1

        self.pre_state = flow_dict_sequence
        self.pre_action = action
        self.prev_reward = reward



    def prepare_state_for_agent(self, state, drop_count):
        self.logger.info("Prepare the state and take action.")
        """Prepare the state, extract features, and take action."""
        if state is None:
            self.logger.error("State is None in prepare_state_for_agent!")
            return

        # ⚠️ Trim long histories to 20
        if isinstance(state, tuple) and len(state) == 3:
            matrix, drop_count, reorder_count = state
            if isinstance(matrix, (list, np.ndarray)) and len(matrix) > 20:
                self.logger.warning(f"⚠️ Received {len(matrix)} matrices. Trimming to 20.")
                matrix = matrix[:20]
            state = (np.array(matrix, dtype=np.float64), drop_count, reorder_count)

        elif isinstance(state, (list, np.ndarray)) and len(state) > 20:
            self.logger.warning(f"⚠️ Received list/array of length {len(state)}. Trimming to 20.")
            state = np.array(state[:20], dtype=np.float64)
        # self.logger.debug(f"state: {state}")
        # ✅ Feature extraction
        try:
            features = self.agent.extract_features(state)

            if isinstance(features, np.ndarray):
                if np.isnan(features).any() or np.isinf(features).any():
                    self.logger.error("❌ NaN or Inf detected in extracted features (NumPy).")
                    return
            elif isinstance(features, torch.Tensor):
                if torch.isnan(features).any() or torch.isinf(features).any():
                    self.logger.error("❌ NaN or Inf detected in extracted features (Tensor).")
                    return
            else:
                self.logger.error(f"❌ Unexpected feature type: {type(features)}")
                return

        except Exception as e:
            self.logger.error(f"❌ Feature extraction failed: {e}")
            return

        # ✅ Action selection
        try:
            action = self.agent.act(features)
        except Exception as e:
            self.logger.error(f"❌ Action selection failed: {e}")
            return

        # 🎯 Reward computation
        try:
            reward = self.calculate_smoothness_vdb(state, drop_count)
            if isinstance(reward, float) and (np.isnan(reward) or np.isinf(reward)):
                self.logger.error(f"❌ Invalid reward value: {reward}")
                return
        except Exception as e:
            self.logger.error(f"❌ Reward calculation failed: {e}")
            return

        self.valid = True  # or use self.is_action_valid(action)

        self.logger.info(f"🏆 Reward: {reward:.6f}, Selected Action: {action}, Valid: {self.valid}")
        if self.valid:
            self._update_config_file(action)

        # 🧠 Training logic
        if self.mode == "train":
            if self.action_type == "routing_multi":
                pass  # custom logic for routing_multi if needed
            elif self.valid and self.pre_state is not None:
                try:
                    features_prev = self.agent.extract_features(self.pre_state)
                    self.agent.remember(features_prev, self.pre_action, reward, features, action, True)
                except Exception as e:
                    self.logger.error(f"❌ Error during remember(): {e}")
            elif self.pre_state is None:
                self.logger.error("❌ Skipping remember() because pre_state is None")

            if self.counter_replay == 5:
                try:
                    self.agent.replay()
                except Exception as e:
                    self.logger.error(f"❌ Replay failed: {e}")
                self.counter_replay = 0
            self.counter_replay += 1

        self.prev_reward = reward
        self.pre_state = state
        self.pre_action = action
    
    
    def prepare_state_for_agent_OLD(self, state,drop_count):
        """Prepare the state and take actions."""
        if state is None:
            self.logger.error("State is None in prepare_state_for_agent!")
            return


        # 🛡️ Handle only trimming matrices — do NOT flatten here
        if isinstance(state, tuple) and len(state) == 3:
            matrix, drop_count, reorder_count = state

            if isinstance(matrix, list) and len(matrix) > 20:
                self.logger.warning(f"⚠️ Received list of {len(matrix)} matrices. Trimming to first 20.")
                matrix = matrix[:20]

            elif isinstance(matrix, np.ndarray) and len(matrix.shape) == 3 and matrix.shape[0] > 20:
                self.logger.warning(f"⚠️ Received ndarray with shape {matrix.shape}. Trimming to first 20 matrices.")
                matrix = matrix[:20]

            matrix = np.array(matrix, dtype=np.float64)
            state = (matrix, drop_count, reorder_count)

        elif isinstance(state, list) and len(state) > 20:
            self.logger.warning(f"⚠️ Received list of {len(state)} matrices. Trimming to first 20.")
            state = np.array(state[:20], dtype=np.float64)

        elif isinstance(state, np.ndarray) and len(state.shape) == 3 and state.shape[0] > 20:
            self.logger.warning(f"⚠️ Received ndarray with shape {state.shape}. Trimming to first 20 matrices.")
            state = state[:20]

        # 🧪 Final shape check before acting
        if isinstance(state, tuple):
            matrix, drop_count, reorder_count = state
            flat_state_for_check = np.concatenate([
                matrix.flatten(),
                np.array([drop_count, reorder_count], dtype=np.float64)
            ])
            self.logger.debug(f"📐 Checking (tuple) flattened shape: {flat_state_for_check.shape}, expected: {self.agent.state_dim}")

            # Validate NaN/Inf
            if np.isnan(flat_state_for_check).any() or np.isinf(flat_state_for_check).any():
                self.logger.error("❌ Invalid (tuple) state detected. Skipping.")
                # np.save(f"invalid_state_step_{self.agent.training_step}.npy", flat_state_for_check)
                return

            if flat_state_for_check.shape[0] != self.agent.state_dim:
                self.logger.error(f"❌ Final state shape mismatch: got {flat_state_for_check.shape[0]}, expected {self.agent.state_dim}")
                return

        else:
            # normal array
            self.logger.debug(f"📐 Checking array state shape: {state.shape}")
            if np.isnan(state).any() or np.isinf(state).any():
                self.logger.error("❌ Invalid array state detected. Skipping.")
                # np.save(f"invalid_state_step_{self.agent.training_step}.npy", state)
                return
            flattened_state = state.flatten()

            # Now check
            if flattened_state.shape[0] != self.agent.state_dim:
                self.logger.error(f"❌ Final array shape mismatch: got {flattened_state.shape[0]}, expected {self.agent.state_dim}")
                return



        valid_actions = list(self.cm_file_valid_action)
        if not valid_actions:
            self.logger.error("❌ No valid actions found for current CM file.")
            return

            # Now you can safely call act
        features = self.agent.extract_features(state)
        action = self.agent.act(features)


        reward = self.calculate_smoothness_vdb(state, drop_count)

        self.valid = True#self.is_action_valid(action)
        self.logger.info(f"🏆 Reward: {reward:.6f}, Selected Action: {action}, Valid action - {self.valid}")

        if self.valid:
            self._update_config_file(action)

        if self.mode == "train":
            if self.action_type == "routing_multi":
                action_to_store = np.array(action, dtype=np.int32)
                # self.agent.remember(self.pre_state, action_to_store, reward, state, True)
            else:
                self.logger.debug(f"🔗 Storing transition: pre_state shape: {self.pre_state.shape if self.pre_state is not None else None}, state shape: {state.shape if not isinstance(state, tuple) else 'tuple'}")
                if self.valid:
                    if self.pre_state is not None:
                        # self.agent.remember(self.pre_state, self.pre_action, reward, state, action, True)
                        # self.agent.remember(self.pre_state , self.pre_action, reward, state , action, True)
                        # self.agent.remember(zero_state(self.pre_state), self.pre_action, reward, zero_state(state), action, True)
                        features_prev = self.agent.extract_features(self.pre_state)
                        features_next = self.agent.extract_features(state)
                        self.agent.remember(features_prev, self.pre_action, reward, features_next, action, True)


                    else:
                        self.logger.error("❌ Skipping remember() because pre_state was None")

            if self.counter_replay == 5:
                self.agent.replay()
                self.counter_replay = 0
            self.counter_replay += 1

        self.prev_reward = reward
        self.pre_state = state
        self.pre_action = action


    def calculate_smoothness_vdb_active(self, state, action):
        """
        Reward based on Jain's fairness index, with flow activity filtering.
        Flows that are inactive in the last 5 intervals are excluded.
        """
        drop_penalty = 0
        reorder_penalty = 0

        if not hasattr(self, 'flow_activity'):
            self.flow_activity = defaultdict(lambda: deque(maxlen=5))

        if isinstance(state, tuple) and len(state) == 3:
            flow_values, drop_count, reorder_count = state
            state_vector = np.vstack(flow_values).flatten()
            drop_penalty = 0.02 if drop_count > 0 else 0.0
            reorder_penalty = 0.05 if reorder_count > 0 else 0.0
        else:
            flow_values = state
            state_vector = flow_values.flatten()

        try:
            stacked_values = np.vstack(flow_values)

            # ---------------------------
            # ✅ Track flow activity
            # ---------------------------
            for i, fid in enumerate(self.flow_ids):
                sent = np.sum(stacked_values[:, i])
                self.flow_activity[fid].append(sent)

            # ---------------------------
            # ✅ Filter active flows
            # ---------------------------
            active_indices = [
                i for i, fid in enumerate(self.flow_ids)
                if any(v > 0 for v in self.flow_activity[fid])
            ]

            if not active_indices:
                self.logger.warning("⚠️ No active flows — returning zero reward.")
                return 0.0

            stacked_active = stacked_values[:, active_indices]

            # ---------------------------
            # ✅ Compute Jain’s Fairness Index
            # ---------------------------
            packets = np.sum(stacked_active, axis=0) / 3000.0
            self.logger.info(f"packets: {packets}")

            safe_normalized_values = np.where(packets > 1e-6, 1 - (1 / (packets + 1e-6)), 1e-6)

            try:
                fairness = scipy.stats.gmean(safe_normalized_values)
            except Exception as e:
                self.logger.error(f"⚠️ Error in geometric mean: {e}")
                fairness = np.mean(safe_normalized_values)

            # ---------------------------
            # ✅ Moving average reward
            # ---------------------------
            if not hasattr(self, 'moving_avg_fairness'):
                self.moving_avg_fairness = fairness
            else:
                self.moving_avg_fairness = 0.7 * self.moving_avg_fairness + 0.3 * fairness

            reward = self.moving_avg_fairness

            self.logger.info(
                f"Fairness: {fairness:.4f}, Drop penalty: {drop_penalty:.4f}, "
                f"Reorder penalty: {reorder_penalty:.4f}, Reward: {reward:.4f}"
            )

            return reward

        except Exception as e:
            self.logger.error(f"Error in reward computation: {e}")
            import traceback
            traceback.print_exc()
            return 0.0

   

    def run_multiple_times(self):
        """Run the HTSIM environment multiple times, reusing the agent."""
        for run_id in range(self.num_runs):
            self.logger.info(f"Starting run {run_id + 1} of {self.num_runs}.")
            self.queue = queue.Queue(maxsize=100)
            self.stop_event.clear()
            self.run_single()

 

    def calculate_smoothness_vdb(self, state, action, epsilon=1e-6, mtu=3000.0, max_per_dst=160.0/60):
        """
        Reward = mean(headroom) / mean(stability) across intervals
        Fully matches reward_bank_headroom_stability logic.
        """
        try:
            if isinstance(state, tuple) and len(state) == 3:
                flow_values, _, _ = state
            else:
                flow_values = state

            headroom_scores = []
            stability_scores = []

            for interval in flow_values:  # interval shape: (num_flows, num_flows)
                interval = interval.astype(np.float64) / mtu
                row_sums = np.sum(interval, axis=1)
                non_zero = row_sums[row_sums > epsilon]

                if len(non_zero) > 1:
                    total = np.sum(non_zero)
                    mean_val = np.mean(non_zero)
                    std_val = np.std(non_zero)
                    n = len(non_zero)

                    # Headroom calculation per interval (based on row sums)
                    headroom = 1.0 - (total / (n*8/3.0  + epsilon))
                    headroom_scores.append(headroom)

                    # Stability calculation per interval
                    coeff_var = std_val / (mean_val + epsilon)
                    stability = 1.0 / (coeff_var + epsilon)
                    stability_scores.append(stability)
                    self.logger.info(f"Headroom: {headroom:.4f}, Stability: {stability:.4f},total: {total:.4f}, mean_val: {mean_val:.4f}, std_val: {std_val:.4f}, n: {n:.4f}")
                else:
                    headroom_scores.append(0.0)
                    stability_scores.append(1.0)  # neutral stability for empty case

            headroom_reward = np.mean(headroom_scores)
            stability_reward = np.mean(stability_scores)

            final_reward =- headroom_reward /stability_reward
            self.logger.info(f"✅ Meta reward (headroom/stability): {final_reward:.6f}")
            return final_reward

        except Exception as e:
            self.logger.error(f"❌ Error computing reward: {e}")
            self.logger.error(traceback.format_exc())
            return 0.0
    





   

    def run_single_watch(self):
        """Run the HTSIM subprocess and threads for one run, with robust stuck detection."""

        output_timestamps = queue.Queue()

        def read_output():
            """Read HTSIM stdout and record last activity timestamp."""
            for line in iter(self.process.stdout.readline, ''):
                line = line.strip()
                if line:
                    output_timestamps.put(time.time())
                    self.logger.debug(f"[HTSIM] {line}")

        def monitor_process(proc_pid, check_interval=2, quiet_timeout=5, cpu_threshold=1.0, max_stuck_cycles=3):
            """Monitor subprocess. Kill if it's quiet AND low CPU usage too long."""
            stuck_count = 0
            last_output_time = time.time()

            try:
                p = psutil.Process(proc_pid)

                while True:
                    time.sleep(check_interval)

                    # 💡 Exit if HTSIM finished normally
                    if self.process.poll() is not None:
                        self.logger.info("🟢 HTSIM finished normally — monitor exiting.")
                        break

                    # Update last output time from output queue
                    try:
                        while True:
                            last_output_time = output_timestamps.get_nowait()
                    except queue.Empty:
                        pass

                    # Check CPU usage
                    cpu_usage = p.cpu_percent(interval=None)
                    if cpu_usage < cpu_threshold:
                        stuck_count += 1
                    else:
                        stuck_count = 0

                    # If too quiet and CPU is low, assume stuck
                    if time.time() - last_output_time > quiet_timeout and stuck_count >= max_stuck_cycles:
                        self.logger.error("🧱 HTSIM is quiet and low-CPU for too long. Killing it.")
                        self.terminate_process()
                        break

            except (psutil.NoSuchProcess, psutil.ZombieProcess):
                self.logger.info("🪦 HTSIM already terminated.")

        self.start_htsim_process()
        self.process.stdout.flush()

        # Start threads
        producer_thread = threading.Thread(target=self.producer, name="ProducerThread", daemon=True)
        consumer_thread = threading.Thread(target=self.consumer, name="ConsumerThread", daemon=True)
        output_thread = threading.Thread(target=read_output, name="OutputMonitor", daemon=True)
        monitor_thread = threading.Thread(target=monitor_process, args=(self.process.pid,), name="MonitorThread", daemon=True)

        start = time.perf_counter()

        producer_thread.start()
        time.sleep(2)
        consumer_thread.start()
        output_thread.start()
        monitor_thread.start()

        try:
            self.process.wait()
            self.logger.info("✅ HTSIM subprocess finished.")
        finally:
            # Check if still alive before trying to kill
            if self.process.poll() is None:
                self.logger.warning("⚠️ HTSIM did not exit cleanly. Terminating now.")
                self.terminate_process()
            else:
                self.logger.info("🧼 HTSIM exited cleanly.")

            # Join threads
            producer_thread.join(timeout=5)
            consumer_thread.join(timeout=5)
            output_thread.join(timeout=2)
            monitor_thread.join(timeout=2)

            if producer_thread.is_alive():
                self.logger.warning("⚠️ Producer thread still alive.")
            if consumer_thread.is_alive():
                self.logger.warning("⚠️ Consumer thread still alive.")

            duration = time.perf_counter() - start
            print("htsim with drl", duration)
        

  
    def run_single(self):
        """Run the HTSIM subprocess and threads for one run."""

        self.start_htsim_process()
        producer_thread = threading.Thread(target=self.producer, name="ProducerThread", daemon=True)
        consumer_thread = threading.Thread(target=self.consumer, name="ConsumerThread", daemon=True)
        start = time.perf_counter()
        producer_thread.start()
        consumer_thread.start()

        try:
            self.process.wait()
            self.logger.info("HTSIM subprocess finished.")
        finally:
            # self.terminate_process()
            producer_thread.join()
            consumer_thread.join()
            duration = time.perf_counter() - start
            print("htsim with drl",duration)

    def terminate_process(self):
        """Terminate HTSIM subprocess and signal threads to stop."""
        self.stop_event.set()
        if self.process and self.process.poll() is None:
            self.process.terminate()
            self.logger.info("HTSIM subprocess terminated.")

    def start_htsim_process(self):
        """Start the HTSIM subprocess."""
        self.logger.info("Starting HTSIM subprocess...")
        try:
            htsim_dir = Path("/home/<USER>/POC/csg-htsim/sim/datacenter").expanduser()
            os.chdir(htsim_dir)
            # print(f"runner comand is: {self.command}")
            self.process = subprocess.Popen(
                self.command,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
                executable="/bin/bash",
            )
            self.logger.info("HTSIM subprocess started successfully.")
        except Exception as e:
            self.logger.error(f"Error starting HTSIM process: {e}")




    def producer(self):
        """Producer thread: Reads from HTSIM's stdout and ensures queue freshness."""
        self.logger.info("Producer thread started.")
        try:
            while not self.stop_event.is_set():
                if self.process and self.process.stdout:
                    try:
                        line = self.process.stdout.readline()
                        # self.logger.debug(f"Producer: {line} ")
                        # self.logger.debug("Attempted to remove item from an empty queue.")
                        if line:
                            # Safely remove the oldest item only if the queue is full and not empty
                            if self.queue.full():
                                try:
                                    self.queue.get_nowait()
                                except Empty:
                                    self.logger.warning("Attempted to remove item from an empty queue.")
                            
                            self.queue.put(line.strip(), timeout=0.01)
                            
                        elif self.process.poll() is not None:
                            self.logger.info("HTSIM subprocess ended.")
                            break

                    except Exception as inner_e:
                        # Log detailed traceback for inner loop errors
                        self.logger.error(f"Inner Error in Producer thread: {inner_e}")
                        self.logger.error(traceback.format_exc())
                        continue  # Continue processing after the error
                else:
                    time.sleep(0.1)

        except Exception as e:
            self.logger.error(f"Critical Error in Producer thread: {e}")
            self.logger.error(traceback.format_exc())

        finally:
            self.logger.info("Producer thread stopped.")
            self.stop_event.set()
        

    def consumer_o(self):
        """
        Consumer thread: Processes data from the queue.
        Tracks per-flow incremental bytes per (src, dst) for each interval.
        """
        self.logger.info("Consumer thread started.")
        interval_count = 0
        flow_dict_sequence = []  # List of interval-wise flow dicts
        current_interval = defaultdict(list)
        drop_count = 0
        reorder_count = 0

        while not self.stop_event.is_set():
            try:
                line = self.queue.get(timeout=0.01)
                # self.logger.debug(f"[Consumer] line: {line.strip()}")
                if "DROPPES" in line:
                    drop_count += 1
                    self.logger.debug(f"DROPPES: {line.strip()}")

                if "Wrong" in line:
                    reorder_count += 1

                if line.startswith("Time Interval:"):
                    # self.logger.debug(f"[Consumer] after if: {line.strip()}")
                    match = re.search(r'Time Interval:\s*([\d\.]+)', line)
                    # self.logger.debug(f"[Consumer] after match: {match}")
                    if match:
                        interval_val = float(match.group(1))
                        # self.logger.debug(f"[Consumer] after interval_val: {interval_val}")
                        if interval_val > 8:
                            self.last_interval = line
                            flow_dict_sequence.append(current_interval)
                            interval_count += 1
                            current_interval = defaultdict(list)
                            self.logger.debug(f"[Consumer] Collected new interval: {line.strip()}")

                elif "Flow:" in line:
                    # self.logger.debug(f"[Consumer] Collected flow line: {line.strip()}")
                    match = re.match(
                        r"Flow:\s+Roce_(\d+)_(\d+)_Src(\d+),?\s+Incremental Bytes Sent:\s+(-?\d+)", line
                    )
                    if match:
                        src = int(match.group(1))
                        dst = int(match.group(2))
                        value = int(match.group(4))
                        num = int(match.group(3))   
                        if value < 0:
                            self.logger.warning(f"Negative incremental bytes skipped: {value} in line: {line.strip()}")
                            continue

                        current_interval[(src, dst, num)].append(value)

                if interval_count >= 10:
                    self.prepare_state_for_agent(flow_dict_sequence, drop_count)

                    interval_count = 0
                    flow_dict_sequence = []
                    current_interval = defaultdict(list)
                    self.logger.debug(f"Drop count this batch: {drop_count}")
                    self.logger.debug(f"Reorders count this batch: {reorder_count}")
                    drop_count = 0
                    reorder_count = 0
                    self.logger.info(f"Last interval: {self.last_interval}")
                    self.logger.info(f"Consumer: Sleeping for {self.sleeping_time} seconds...")
                    self.logger.info("-----------------------------------------")
                    time.sleep(self.sleeping_time)

            except queue.Empty:
                if self.stop_event.is_set():
                    break
                continue
            except Exception as e:
                self.logger.error(f"Error in Consumer thread: {e}")
                if 'line' in locals():
                    self.logger.error(f"Problematic line: {line.strip()}")
                self.logger.error(traceback.format_exc())

    def consumer(self):
        """
        Consumer thread: Processes data from the queue.
        Aggregates per-interval flow values into a (src, dst) matrix.
        """
        self.logger.info("Consumer thread started.")
        interval_count = 0
        new_data = []
        current_matrix = None
        drop_count = 0
        reorder_count = 0

        while not self.stop_event.is_set():
            try:
                line = self.queue.get(timeout=0.01)

                if "DROPPES" in line:
                    drop_count += 1
                    self.logger.debug(f"DROPPES: {line.strip()}")

                if "Wrong" in line:
                    reorder_count += 1

                if line.startswith("Time Interval:"):
                    match = re.search(r'Time Interval:\s*([\d\.]+)', line)
                    if match:
                        interval_val = float(match.group(1))
                        if interval_val > 8:  # Only after warm-up
                            self.last_interval = line
                            if current_matrix is not None:
                                new_data.append(current_matrix)
                                interval_count += 1
                            current_matrix = np.zeros((self.num_flows, self.num_flows), dtype=np.float64)
                            self.logger.debug(f"[Consumer] Collected new interval: {line.strip()}")

                elif "Flow:" in line and current_matrix is not None:
                    match = re.match(
                        r"Flow:\s+Roce_(\d+)_(\d+)_Src(\d+),?\s+Incremental Bytes Sent:\s+(-?\d+)", line
                    )
                    if match:
                        src = int(match.group(1))
                        dst = int(match.group(2))
                        value = int(match.group(4))

                        if value < 0:
                            self.logger.warning(f"Negative incremental bytes skipped: {value} in line: {line.strip()}")
                            continue

                        if 0 <= src < self.num_flows and 0 <= dst < self.num_flows:
                            current_matrix[src][dst] += value
                        else:
                            self.logger.warning(
                                f"[Consumer] Flow index out of bounds (src={src}, dst={dst}) "
                                f"for matrix size {self.num_flows}. Line: {line.strip()}"
                            )

                if interval_count >= 10:
                    if self.state_extended:
                        self.prepare_state_for_agent((new_data, drop_count, reorder_count))
                    else:
                        new_data_np = np.array(new_data, dtype=np.float64)
                        self.prepare_state_for_agent(new_data_np, drop_count)

                    interval_count = 0
                    new_data = []
                    current_matrix = None
                    self.logger.debug(f"Drop count this batch: {drop_count}")
                    self.logger.debug(f"Reorders count this batch: {reorder_count}")
                    drop_count = 0
                    reorder_count = 0
                    self.logger.info(f"Last interval: {self.last_interval}")
                    self.logger.info(f"Consumer: Sleeping for {self.sleeping_time} seconds...")
                    self.logger.info("-----------------------------------------")
                    time.sleep(self.sleeping_time)

            except queue.Empty:
                if self.stop_event.is_set():
                    break
                continue
            except Exception as e:
                self.logger.error(f"Error in Consumer thread: {e}")
                if 'line' in locals():
                    self.logger.error(f"Problematic line: {line.strip()}")
                self.logger.error(traceback.format_exc())



 


    def consumer_old(self):
        """
        Consumer thread: Processes data from the queue.
        """
        self.logger.info("Consumer thread started.")
        interval_count = 0
        new_data = []
        current_matrix = None
        drop_count = 0
        reorder_count = 0

        while not self.stop_event.is_set():
            try:
                line = self.queue.get(timeout=0.01)

                if "DROPPES" in line:
                    drop_count += 1
                    self.logger.debug(f"DROPPES: {line.strip()}")

                if "Wrong" in line:
                    reorder_count += 1

                if line.startswith("Time Interval:") and int(re.search(r'Time Interval:\s*(\d+)', line).group(1)) > 8000:
                    self.last_interval = line
                    if current_matrix is not None:
                        new_data.append(current_matrix)
                        interval_count += 1
                    current_matrix = np.zeros((self.num_flows, self.num_flows), dtype=np.float64)
                    self.logger.debug(f"Collected new interval: {line.strip()}")

                elif "Flow:" in line and current_matrix is not None:
                    self.logger.debug(f"Collected flow line: {line.strip()}")
                    match = re.match(
                        r"Flow:\s+Roce_(\d+)_(\d+)_Src(\d+),?\s+Incremental Bytes Sent:\s+(-?\d+)", line
                    )
                    if match:
                        src = int(match.group(1))
                        dst = int(match.group(2))
                        value = int(match.group(4))

                        if value < 0:
                            self.logger.warning(f"Negative incremental bytes skipped: {value} in line: {line.strip()}")
                            continue

                        if 0 <= src < self.num_flows and 0 <= dst < self.num_flows:
                            current_matrix[src][dst] = value
                        else:
                            self.logger.warning(
                                f"[Consumer] Flow index out of bounds (src={src}, dst={dst}) "
                                f"for matrix size {self.num_flows}. Line: {line.strip()}"
                            )

                if interval_count >= 10:
                    if self.state_extended:
                        self.prepare_state_for_agent((new_data, drop_count, reorder_count))
                    else:
                        new_data_np = np.array(new_data, dtype=np.float64)
                        self.prepare_state_for_agent(new_data_np, drop_count)

                    interval_count = 0
                    new_data = []
                    current_matrix = None
                    self.logger.debug(f"Drop count this batch: {drop_count}")
                    self.logger.debug(f"Reorders count this batch: {reorder_count}")
                    drop_count = 0
                    reorder_count = 0
                    self.logger.info(f"Last interval: {self.last_interval}")
                    self.logger.info(f"Consumer: Sleeping for {self.sleeping_time} seconds...")
                    self.logger.info("-----------------------------------------")
                    time.sleep(self.sleeping_time)

            except queue.Empty:
                if self.stop_event.is_set():
                    break
                continue
            except Exception as e:
                self.logger.error(f"Error in Consumer thread: {e}")
                if 'line' in locals():
                    self.logger.error(f"Problematic line: {line.strip()}")
                self.logger.error(traceback.format_exc())


