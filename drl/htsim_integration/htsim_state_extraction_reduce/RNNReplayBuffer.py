# from collections import deque
# import numpy as np
# import random
# from logger_wo import Logger
# class RNNReplayBuffer:
#     def __init__(self, buffer_size, sequence_len,config):
#         self.buffer_size = buffer_size
#         self.sequence_len = sequence_len
#         self.states = []
#         self.actions = []
#         self.rewards = []
#         self.next_states = []
#         self.next_actions = []
#         self.dones = []
#         self.position = 0
#         self.config = config
#         self.logger = Logger("RNNReplayBuffer", self.config).get_logger()
#         self.logger.info("RNNReplayBuffer initialized.")

#     def __len__(self):
#         return len(self.states)

#     def add(self, state, action, reward, next_state, next_action, done):
#         # Ensure correct dtype and shape
#         state = np.asarray(state, dtype=np.float16).flatten()
#         next_state = np.asarray(next_state, dtype=np.float16).flatten()
#         action = np.asarray(action, dtype=np.float16).reshape(-1)
#         next_action = np.asarray(next_action, dtype=np.float16).reshape(-1)
#         reward = np.float16(reward)
#         done = bool(done)

#         if len(self.states) > 0:
#             assert state.shape == self.states[0].shape, f"❌ State shape mismatch: {state.shape} vs {self.states[0].shape}"
#             assert action.shape == self.actions[0].shape, f"❌ Action shape mismatch: {action.shape} vs {self.actions[0].shape}"

#         self.logger.debug(f"📥 Adding sample:")
#         self.logger.debug(f"  - state: shape={state.shape}, dtype={state.dtype}")
#         self.logger.debug(f"  - action: shape={action.shape}, dtype={action.dtype}")
#         self.logger.debug(f"  - reward: {reward}, dtype={type(reward)}")
#         self.logger.debug(f"  - done: {done}")

#         if len(self.states) < self.buffer_size:
#             self.states.append(state)
#             self.actions.append(action)
#             self.rewards.append(reward)
#             self.next_states.append(next_state)
#             self.next_actions.append(next_action)
#             self.dones.append(done)
#         else:
#             self.states[self.position] = state
#             self.actions[self.position] = action
#             self.rewards[self.position] = reward
#             self.next_states[self.position] = next_state
#             self.next_actions[self.position] = next_action
#             self.dones[self.position] = done

#         self.position = (self.position + 1) % self.buffer_size
#         self.logger.info(f"📦 Buffer size: {len(self.states)} / {self.buffer_size}")

#     def sample(self, batch_size):
#         max_start_index = len(self.states) - self.sequence_len
#         valid_indices = list(range(max_start_index))
#         self.logger.info(f"🔎 Valid sequences available: {len(valid_indices)} / {len(self.states)}")

#         if len(valid_indices) < batch_size:
#             raise ValueError("Not enough valid sequences in RNN buffer.")

#         indices = random.sample(valid_indices, batch_size)

#         # ✅ Debug consistency of shapes before creating arrays
#         first_state_shape = self.states[0].shape
#         first_action_shape = self.actions[0].shape
#         for i in indices:
#             for j in range(self.sequence_len):
#                 s_idx = i + j
#                 if self.states[s_idx].shape != first_state_shape:
#                     self.logger.error(f"❌ Inconsistent state shape at index {s_idx}: {self.states[s_idx].shape} vs {first_state_shape}")
#                 if self.actions[s_idx].shape != first_action_shape:
#                     self.logger.error(f"❌ Inconsistent action shape at index {s_idx}: {self.actions[s_idx].shape} vs {first_action_shape}")

#         # 👇 Force dtype and shape early
#         state_seqs = np.array([self.states[i:i + self.sequence_len] for i in indices], dtype=np.float16)
#         action_seqs = np.array([self.actions[i:i + self.sequence_len] for i in indices], dtype=np.float16)
#         reward_final = np.array([self.rewards[i + self.sequence_len - 1] for i in indices], dtype=np.float16)
#         next_state_seqs = np.array([self.next_states[i:i + self.sequence_len] for i in indices], dtype=np.float16)
#         next_action_seqs = np.array([self.next_actions[i:i + self.sequence_len] for i in indices], dtype=np.float16)
#         done_final = np.array([self.dones[i + self.sequence_len - 1] for i in indices], dtype=bool)

#         # 🔍 Logging shapes and dtypes
#         self.logger.info(f"✅ Shapes: states={state_seqs.shape}, actions={action_seqs.shape}, rewards={reward_final.shape}")
#         self.logger.info(f"✅ Dtypes: states={state_seqs.dtype}, actions={action_seqs.dtype}, rewards={reward_final.dtype}, dones={done_final.dtype}")

#         if (state_seqs.dtype == object or
#             action_seqs.dtype == object or
#             reward_final.dtype == object or
#             next_state_seqs.dtype == object or
#             next_action_seqs.dtype == object):
#             self.logger.error("🚨 Found dtype=object in sampled data! This will break torch.tensor conversion.")
#             self.logger.debug(f"Sample state example: {self.states[indices[0]]}")
#             self.logger.debug(f"Sample action example: {self.actions[indices[0]]}")
#             self.logger.debug(f"Sample reward example: {self.rewards[indices[0] + self.sequence_len - 1]}")

#         return state_seqs, action_seqs, reward_final, next_state_seqs, next_action_seqs, done_final

#     def sample_o(self, batch_size):
#         max_start_index = len(self.states) - self.sequence_len
#         valid_indices = list(range(max_start_index))

#         self.logger.info(f"🔎 Valid sequences available: {len(valid_indices)} / {len(self.states)}")
#         if len(valid_indices) < batch_size:
#             raise ValueError("Not enough valid sequences in RNN buffer.")

#         indices = random.sample(valid_indices, batch_size)

#         try:
#             state_seqs = np.array([self.states[i:i + self.sequence_len] for i in indices])
#             action_seqs = np.array([self.actions[i:i + self.sequence_len] for i in indices])
#             reward_final = np.array([self.rewards[i + self.sequence_len - 1] for i in indices])
#             next_state_seqs = np.array([self.next_states[i:i + self.sequence_len] for i in indices])
#             next_action_seqs = np.array([self.next_actions[i:i + self.sequence_len] for i in indices])
#             done_final = np.array([self.dones[i + self.sequence_len - 1] for i in indices])
#         except Exception as e:
#             self.logger.error(f"❌ Error creating arrays in sample(): {e}")
#             raise

#         # 🔍 Logging shapes and dtypes
#         self.logger.info(f"✅ Shapes: states={state_seqs.shape}, actions={action_seqs.shape}, rewards={reward_final.shape}")
#         self.logger.info(f"✅ Dtypes: states={state_seqs.dtype}, actions={action_seqs.dtype}, rewards={reward_final.dtype}, dones={done_final.dtype}")

#         # 🧪 Check for object dtype contamination
#         if state_seqs.dtype == object or action_seqs.dtype == object or reward_final.dtype == object:
#             self.logger.error("🚨 Found dtype=object in sampled data! This will break torch.tensor conversion.")

#             # Log sample values for inspection
#             self.logger.debug(f"Sample state example: {self.states[indices[0]]}")
#             self.logger.debug(f"Sample action example: {self.actions[indices[0]]}")
#             self.logger.debug(f"Sample reward example: {self.rewards[indices[0] + self.sequence_len - 1]}")

#         return state_seqs, action_seqs, reward_final, next_state_seqs, next_action_seqs, done_final
from collections import deque
import numpy as np
import random
from logger_wo import Logger
class RNNReplayBuffer:
    def __init__(self, buffer_size, sequence_len,config):
        self.buffer_size = buffer_size
        self.sequence_len = sequence_len
        self.states = []
        self.actions = []
        self.rewards = []
        self.next_states = []
        self.next_actions = []
        self.dones = []
        self.position = 0
        self.config = config
        self.logger = Logger("RNNReplayBuffer", self.config).get_logger()
        self.logger.info("RNNReplayBuffer initialized.")

    def __len__(self):
        return len(self.states)

    def add(self, state, action, reward, next_state, next_action, done):
        if len(self.states) < self.buffer_size:
            self.states.append(state)
            self.actions.append(action)
            self.rewards.append(reward)
            self.next_states.append(next_state)
            self.next_actions.append(next_action)
            self.dones.append(done)
        else:
            self.states[self.position] = state
            self.actions[self.position] = action
            self.rewards[self.position] = reward
            self.next_states[self.position] = next_state
            self.next_actions[self.position] = next_action
            self.dones[self.position] = done

        self.position = (self.position + 1) % self.buffer_size
           # 🔍 Log buffer size after each insertion
        self.logger.info(f"📦 Buffer size: {len(self.states)} / {self.buffer_size}")
    def sample(self, batch_size):
        max_start_index = len(self.states) - self.sequence_len
        valid_indices = list(range(max_start_index))
        self.logger.info(f"🔎 Valid sequences available: {len(valid_indices)} / {len(self.states)}")

        if len(valid_indices) < batch_size:
            raise ValueError("Not enough valid sequences in RNN buffer.")

        indices = random.sample(valid_indices, batch_size)

        # Inspect one example's shape
        expected_state_shape = self.states[0].shape
        expected_action_shape = self.actions[0].shape

        self.logger.info(f"🧪 Expected state shape: {expected_state_shape}, action shape: {expected_action_shape}")

        for batch_i, start_idx in enumerate(indices):
            for t in range(self.sequence_len):
                i = start_idx + t
                try:
                    s = self.states[i]
                    a = self.actions[i]

                    if s.shape != expected_state_shape:
                        self.logger.error(f"❌ State shape mismatch at buffer[{i}] in Batch[{batch_i}][{t}]")
                        self.logger.error(f"    Got: {s.shape}, Expected: {expected_state_shape}")
                        self.logger.error(f"    State sample (first 20 elements): {s[:20]}")

                    if a.shape != expected_action_shape:
                        self.logger.error(f"❌ Action shape mismatch at buffer[{i}] in Batch[{batch_i}][{t}]")
                        self.logger.error(f"    Got: {a.shape}, Expected: {expected_action_shape}")
                        self.logger.error(f"    Action sample: {a}")

                except Exception as e:
                    self.logger.error(f"🔥 Exception accessing buffer[{i}] in Batch[{batch_i}][{t}]: {e}")

        try:
            state_seqs = np.array([self.states[i:i + self.sequence_len] for i in indices], dtype=np.float16)
            action_seqs = np.array([self.actions[i:i + self.sequence_len] for i in indices], dtype=np.float16)
            reward_final = np.array([self.rewards[i + self.sequence_len - 1] for i in indices], dtype=np.float16)
            next_state_seqs = np.array([self.next_states[i:i + self.sequence_len] for i in indices], dtype=np.float16)
            next_action_seqs = np.array([self.next_actions[i:i + self.sequence_len] for i in indices], dtype=np.float16)
            done_final = np.array([self.dones[i + self.sequence_len - 1] for i in indices], dtype=bool)

        except Exception as e:
            self.logger.error("🔥 ERROR converting batch to tensors:")
            self.logger.error(f"🧾 Exception: {e}")
            self.logger.error(f"📐 Sample shapes: states[0]={self.states[0].shape}, actions[0]={self.actions[0].shape}")
            self.logger.error(f"📦 Buffer size: states={len(self.states)}, actions={len(self.actions)}")
            self.logger.error(f"📊 Example indices: {indices}")
            raise e

        return state_seqs, action_seqs, reward_final, next_state_seqs, next_action_seqs, done_final

    # def sample(self, batch_size):
    #     max_start_index = len(self.states) - self.sequence_len
    #     valid_indices = list(range(max_start_index))
    #     self.logger.info(f"🔎 Valid sequences available: {len(valid_indices)} / {len(self.states)}")

    #     if len(valid_indices) < batch_size:
    #         raise ValueError("Not enough valid sequences in RNN buffer.")

    #     indices = random.sample(valid_indices, batch_size)

    #     # Inspect one example's shape
    #     expected_state_shape = self.states[0].shape
    #     expected_action_shape = self.actions[0].shape

    #     for idx in indices:
    #         for offset in range(self.sequence_len):
    #             i = idx + offset
    #             try:
    #                 s = self.states[i]
    #                 a = self.actions[i]
    #                 if s.shape != expected_state_shape:
    #                     self.logger.error(f"❌ State shape mismatch at {i}: got {s.shape}, expected {expected_state_shape}")
    #                 if a.shape != expected_action_shape:
    #                     self.logger.error(f"❌ Action shape mismatch at {i}: got {a.shape}, expected {expected_action_shape}")
    #             except Exception as e:
    #                 self.logger.error(f"🔥 Exception accessing buffer[{i}]: {e}")

    #     try:
    #         state_seqs = np.array([self.states[i:i + self.sequence_len] for i in indices], dtype=np.float16)
    #         action_seqs = np.array([self.actions[i:i + self.sequence_len] for i in indices], dtype=np.float16)
    #         reward_final = np.array([self.rewards[i + self.sequence_len - 1] for i in indices], dtype=np.float16)
    #         next_state_seqs = np.array([self.next_states[i:i + self.sequence_len] for i in indices], dtype=np.float16)
    #         next_action_seqs = np.array([self.next_actions[i:i + self.sequence_len] for i in indices], dtype=np.float16)
    #         done_final = np.array([self.dones[i + self.sequence_len - 1] for i in indices], dtype=bool)

    #     except Exception as e:
    #         self.logger.error("🔥 ERROR converting batch to tensors:")
    #         self.logger.error(f"🧾 Exception: {e}")
    #         self.logger.error(f"📐 Sample shapes: states[0]={self.states[0].shape}, actions[0]={self.actions[0].shape}")
    #         self.logger.error(f"📦 Batch sizes: states={len(self.states)}, actions={len(self.actions)}")
    #         self.logger.error(f"📊 Example indices: {indices}")
    #         raise e

    #     return state_seqs, action_seqs, reward_final, next_state_seqs, next_action_seqs, done_final

    # def sample(self, batch_size):
    #     max_start_index = len(self.states) - self.sequence_len

    #     # ✅ Allow all sequences of full length, regardless of 'done' flags
    #     valid_indices = list(range(max_start_index))

    #     # 🔍 Log how many valid sequences we found
    #     self.logger.info(f"🔎 Valid sequences available: {len(valid_indices)} / {len(self.states)}")

    #     if len(valid_indices) < batch_size:
    #         raise ValueError("Not enough valid sequences in RNN buffer.")

    #     # Randomly select starting points for batch
    #     indices = random.sample(valid_indices, batch_size)

    #     # Assemble batches
    #     state_seqs = np.array([self.states[i:i + self.sequence_len] for i in indices])             # (B, T, state_dim)
    #     action_seqs = np.array([self.actions[i:i + self.sequence_len] for i in indices])           # (B, T, action_dim)
    #     reward_final = np.array([self.rewards[i + self.sequence_len - 1] for i in indices])         # (B,)
    #     next_state_seqs = np.array([self.next_states[i:i + self.sequence_len] for i in indices])   # (B, T, state_dim)
    #     next_action_seqs = np.array([self.next_actions[i:i + self.sequence_len] for i in indices]) # (B, T, action_dim)
    #     done_final = np.array([self.dones[i + self.sequence_len - 1] for i in indices])             # (B,)

    #     return state_seqs, action_seqs, reward_final, next_state_seqs, next_action_seqs, done_final
