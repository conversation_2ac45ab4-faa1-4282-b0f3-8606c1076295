{"file_env": {"mode": "train", "log_dir": "/mnt/disks/disk1/train_wo_lstm", "MODEL_SAVE_DIR": "/mnt/disks/disk1/train_wo_agent_lstm", "roce_config_file": "/home/<USER>/POC/csg-htsim/sim/datacenter/roce_config_file.txt", "folder_path": "/home/<USER>/POC/csg-htsim/sim/datacenter/connection_matrices/incast128", "incast_folder": "incast_cm//", "incast_output": "incast_out//", "new_env_folder": "incasst32_random_flow_size_per_incastratio_cm//", "new_out_folder": "incasst32_random_flow_size_per_incastratio_out//"}, "file_agent_incast": {"agent": "ddqn", "training_frequence": 10, "fairness_alpha": 0.7, "fluctuation_penalty_weight": 0.2, "pfc_penalty_weight": 0.1, "reward_discount": 0.99, "action_size": 80, "learning_rate": 0.0003, "gamma": 0.99, "sleeping_time": 0.15, "seed_config": "True", "action_type": "pfcs_and_ar", "single_action_config": {"q_values": [30], "pfc_range_single": 11}, "multiple_action_config": {"q_values": [30], "pfc_count": 3, "pfc_range_multi": 11}, "buffer_type": "rnn", "sequence_len": 8, "prioritization_alpha": 0.6, "importance_beta": 0.4, "epsilon_start": 1, "epsilon_min": 0.05, "epsilon_decay": 0.9995, "batch_size": 32, "buffer_size": 50000, "num_episodes": 500, "hidden_sizes": [512, 512, 256, 256, 128], "dropout_rate": 0.1, "soft_update_frequency": 100, "tau": 0.001, "n_step": 3, "noisy_nets": false, "weight_decay": 1e-05}}