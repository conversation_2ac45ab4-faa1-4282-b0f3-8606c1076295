import torch
import torch.nn.functional as F
from agents.base_agent import *
from FileModel_wo import FileModel
import torch.optim as optim

class RainbowDQNAgent(MainAgent):
    def __init__(self, state, config):
        super().__init__(state, config)
        print("RainbowDQNAgent")
        print("RainbowDQNAgent config:", config)

        self.full_config = config

        self.model = FileModel(
            self.full_config,
            state_dim=self.state_dim,
            action_dim=self.action_dim,
            hidden_sizes=config["file_agent_incast"]["hidden_sizes"],
            dropout_rate=config["file_agent_incast"]["dropout_rate"]
        ).to(self.device)
        print("self.model", self.model)

        self.target_model = FileModel(
            self.full_config,
            state_dim=self.state_dim,
            action_dim=self.action_dim,
            hidden_sizes=config["file_agent_incast"]["hidden_sizes"],
            dropout_rate=config["file_agent_incast"]["dropout_rate"]
        ).to(self.device)
        print("self.target_model", self.target_model)

        # Improved optimizer with better hyperparameters
        self.optimizer = optim.AdamW(
            self.model.parameters(),
            lr=self.config['learning_rate'],
            weight_decay=self.config.get("weight_decay", 1e-4),
            betas=(0.9, 0.999),
            eps=1e-8
        )
        print("self.optimizer", self.optimizer)

        # Improved learning rate scheduler with cosine annealing
        self.scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
            self.optimizer,
            T_0=1000,  # Initial restart period
            T_mult=2,  # Factor to increase restart period
            eta_min=1e-6  # Minimum learning rate
        )
        print("self.scheduler", self.scheduler)

        self.update_target_model(tau=1.0)

        # Rainbow DQN specific parameters
        self.n_step = config["file_agent_incast"].get("n_step", 3)  # Multi-step learning
        self.noisy_nets = config["file_agent_incast"].get("noisy_nets", False)

        # Experience replay improvements
        self.min_priority = 1e-6
        self.max_priority = 1.0

    def replay(self):
        if len(self.replay_buffer) < self.batch_size:
            return

        if self.buffer_type == "prioritized":
            batch, weights, indices = self.replay_buffer.sample(
                self.batch_size, beta=self.config.get("importance_beta", 0.4)
            )
        else:
            batch = self.replay_buffer.sample(self.batch_size)
            weights = np.ones(self.batch_size)
            indices = None

        states = torch.tensor(batch[0], dtype=torch.float32, device=self.device)
        actions = torch.tensor(batch[1], dtype=torch.float32, device=self.device)
        rewards = torch.tensor(batch[2], dtype=torch.float32, device=self.device)
        next_states = torch.tensor(batch[3], dtype=torch.float32, device=self.device)
        dones = torch.tensor(batch[5], dtype=torch.float32, device=self.device)
        weights_tensor = torch.tensor(weights, dtype=torch.float32, device=self.device)

        # Handle multi-discrete actions robustly
        if actions.dim() == 2 and actions.size(1) > 1:
            action_list = [tuple(a.cpu().numpy()) for a in actions]
            action_idx = [self.action_map.get(a, 0) for a in action_list]
            actions = torch.tensor(action_idx, dtype=torch.long, device=self.device)
        else:
            actions = actions.long()

        self.model.train()
        q_values = self.model(states)
        current_q = q_values.gather(1, actions.unsqueeze(1)).squeeze(1)

        with torch.no_grad():
            # Double DQN: use main network to select actions, target network to evaluate
            next_actions = self.model(next_states).argmax(1, keepdim=True)
            next_q_values = self.target_model(next_states).gather(1, next_actions).squeeze(1)

            # Multi-step returns for better learning
            gamma_n = self.gamma ** self.n_step
            target_q_values = rewards + (1 - dones) * gamma_n * next_q_values

        # Use Huber loss for better stability
        loss = F.smooth_l1_loss(current_q, target_q_values, reduction='none')
        loss = (loss * weights_tensor).mean()

        if torch.isnan(loss) or torch.isinf(loss):
            return

        # Update scheduler (CosineAnnealingWarmRestarts doesn't need loss)
        self.scheduler.step()

        self.optimizer.zero_grad()
        self.scaler.scale(loss).backward()
        self.scaler.unscale_(self.optimizer)

        # Improved gradient clipping
        grad_norm = torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)

        self.scaler.step(self.optimizer)
        self.scaler.update()

        # Log gradient norm for monitoring
        if self.training_step % 100 == 0:
            self.logger_agent.info(f"Gradient norm: {grad_norm:.4f}")

        if self.buffer_type == "prioritized" and indices is not None:
            td_errors = current_q - target_q_values
            # Improved priority calculation with clipping
            new_priorities = torch.clamp(
                td_errors.abs().detach().cpu(),
                min=self.min_priority,
                max=self.max_priority
            ).numpy()
            self.replay_buffer.update_priorities(indices, new_priorities)

        if self.training_step % 100 == 0:
            with torch.no_grad():
                self.logger_agent.info(
                    f"[Step {self.training_step}] Rainbow Q Stats → "
                    f"Mean: {current_q.mean():.4f}, "
                    f"Std: {current_q.std():.4f}, "
                    f"Min: {current_q.min():.4f}, "
                    f"Max: {current_q.max():.4f}, "
                    f"Loss: {loss.item():.4f}"
                )

        self.training_step += 1
        if self.training_step % self.config.get("soft_update_frequency", 10) == 0:
            self.update_target_model(tau=self.config.get("tau", 0.005))

    def save_best_model(self, path, performance_metric):
        """Save model if it achieves best performance"""
        if not hasattr(self, 'best_performance') or performance_metric > self.best_performance:
            self.best_performance = performance_metric
            torch.save({
                'model_state_dict': self.model.state_dict(),
                'target_model_state_dict': self.target_model.state_dict(),
                'optimizer_state_dict': self.optimizer.state_dict(),
                'scheduler_state_dict': self.scheduler.state_dict(),
                'training_step': self.training_step,
                'best_performance': self.best_performance,
                'config': self.config
            }, path)
            self.logger_agent.info(f"💾 Saved best model with performance: {performance_metric:.4f}")

    def load_best_model(self, path):
        """Load the best saved model"""
        checkpoint = torch.load(path, map_location=self.device)
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.target_model.load_state_dict(checkpoint['target_model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
        self.training_step = checkpoint['training_step']
        self.best_performance = checkpoint['best_performance']
        self.logger_agent.info(f"📂 Loaded best model with performance: {self.best_performance:.4f}")
