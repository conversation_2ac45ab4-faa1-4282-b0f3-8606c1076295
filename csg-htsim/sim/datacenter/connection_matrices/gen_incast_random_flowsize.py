#!/usr/bin/env python

# Generate an incast traffic matrix.
# python gen_incast.py <nodes> <conns> <flowsize> <extrastarttime> <randseed>
# Parameters:
# <nodes>   number of nodes in the topology
# <conns>    number of active connections
# <incastdest>  incast dest node
# <incastratio> incast ratio
# <flowsize_min>  minimum size of the flows in bytes
# <flowsize_max>  maximum size of the flows in bytes
# <extrastarttime>   How long in microseconds to space the start times over (start time will be random in between 0 and this time).  Can be a float.
# <randseed>   Seed for random number generator, or set to 0 for random seed

import os
import sys
import random
from random import seed, shuffle, randint
import math

def generate_random_value_in_steps(min_value, max_value, step,randseed):
  """Generates a random value between min_value and max_value in steps of step.

  Args:
    min_value: The minimum value.
    max_value: The maximum value.
    step: The step size.

  Returns:
    A random value between min_value and max_value in steps of step.
  """

  # Calculate the number of possible values
  num_values = (max_value - min_value) // step + 1

  # Generate a random index between 0 and num_values - 1
  random.seed(randseed)
  random_index = random.randint(0, num_values - 1)

  # Calculate the random value based on the index
  random_value = min_value + random_index * step

  return random_value

def generate_random_flowsize():
    #predefined_values = [128, 3000, 1000, 15000]
    #predefined_values = [128, 1000, 8000000]
    #predefined_values = [128, 1000, 3000, 15000 ,150000, 2000000, 8000000]
    predefined_values = [200000000]

    #Choose a random value from the list
    random_value = random.choice(predefined_values)
    return random_value


def unique_random_numbers(x, y, count,randseed):
  """Generates a list of `count` unique random integers between `x` and `y`.
  Args:
    x: The lower bound of the range.
    y: The upper bound of the range.
    count: The number of unique random integers to generate.
  Returns:
    A list of `count` unique random integers.

  Raises:
    ValueError: If `count` is greater than the range size (`y - x + 1`).
  """

  if count > y - x + 1:
    raise ValueError("Count cannot be greater than the range size.")
  random.seed(randseed)
  random_set = set()
  while len(random_set) < count:
    random_set.add(random.randint(x, y))
  return list(random_set)

def generate_random_incastdest(nodes,number_of_incast_groups,randseed):
    #return list of "number_of_incast_groups" that set the incast destination nodes
    incast_destinations = unique_random_numbers(1,nodes-1 ,number_of_incast_groups,randseed)
    print (f"incast_destinations are: {incast_destinations}")
    return incast_destinations



#print(sys.argv)
if len(sys.argv) != 10:
    print("Usage: python gen_incast.py <filename> <nodes> <conns> <incastdest> <incastratio> <flowsize_min> <flowsize_max> <extrastarttime> <randseed>")
    sys.exit()
filename = sys.argv[1]
nodes = int(sys.argv[2])
conns = int(sys.argv[3])
incastdest = int(sys.argv[4])
incastratio = int(sys.argv[5])
flowsize_min = int(sys.argv[6])
flowsize_max = int(sys.argv[7])
extrastarttime = float(sys.argv[8])
randseed = int(sys.argv[9])

print("Nodes: ", nodes)
print("Connections: ", conns)
print("Dest node: ", incastdest)
print("Incast ratio: ", incastratio)
print("Flowsize min: ", flowsize_min, "bytes")
print("Flowsize max: ", flowsize_max, "bytes")                   
print("ExtraStartTime: ", extrastarttime, "us")
print("Random Seed ", randseed)

srcs = []
incastgroups = []

flowsize_step = 1000

number_of_incast_groups = math.floor(nodes/(incastratio+1))

# if randseed != 0:
#     random_flowsize = generate_random_value_in_steps(flowsize_min, flowsize_max,flowsize_step)
# else:
#     random_flowsize = 2000000


# for n in range(conns):
#     extra = randint(0,int(extrastarttime * 1000000))
#     out = str(srcs[n]) + "->" + str(dst) + " id " + str(n+1) + " start " + str(extra) + " size " + str(generate_random_value_in_steps(flowsize_min, flowsize_max,flowsize_step))
#     print(out, file=f)


# Backup 12.11.24
# if randseed != 0:
#     random_flowsize = generate_random_flowsize()
# else:
#     random_flowsize = 2000000


# for n in range(conns):
#     extra = randint(0,int(extrastarttime * 1000000))
#     out = str(srcs[n]) + "->" + str(dst) + " id " + str(n+1) + " start " + str(extra) + " size " + str(generate_random_flowsize())
#     print(out, file=f)
################################



if randseed != 0:
    random_flowsize = generate_random_flowsize()
else:
    random_flowsize = 2000000000

incastdst= generate_random_incastdest(nodes,number_of_incast_groups,randseed)
conns = nodes - number_of_incast_groups

for n in range(nodes):
    if n in incastdst:
      continue
    srcs.append(n)
if randseed != 0:
    seed(randseed)
#shuffle(srcs)
print (f"srcs : {srcs}")

f = open(filename, "w")
print("Nodes", nodes, file=f)
print("Connections", conns, file=f)

for n in range(len(srcs)):
  i = n % number_of_incast_groups
  dst = incastdst[i]
  extra = randint(0,int(extrastarttime * 1000000))
  out = str(srcs[n]) + "->" + str(dst) + " id " + str(n+1) + " start " + str(extra) + " size " + str(generate_random_flowsize())
  #          15         ->        0        id        1         start         0          size            1036000
  print(out, file=f)
f.close()

