#!/usr/bin/env python

# Generate a ring allreduce traffic matrix.
# python gen_allreduce.py <filename> <nodes> <conns> <groupsize> <flowsize> <locality> <randseed>
# Parameters:
#   <filename>    output CM filename
#   <nodes>       number of nodes in the topology
#   <conns>       number of active GPUs in the reduction operation
#   <groupsize>   number of connections in each allreduce ring
#   <flowsize>    size of each flow in bytes
#   <locality>    if 1, sort each ring’s node list; if 0, leave in ascending order
#   <randseed>    (unused now, kept for compatibility)

import sys

if len(sys.argv) != 8:
    print("Usage: python gen_allreduce.py <filename> <nodes> <conns> <groupsize> <flowsize> <locality> <randseed>")
    sys.exit(1)

filename    = sys.argv[1]
nodes       = int(sys.argv[2])
conns       = int(sys.argv[3])
groupsize   = int(sys.argv[4])
flowsize    = int(sys.argv[5])
locality    = int(sys.argv[6])
randseed    = int(sys.argv[7])    # retained for interface compatibility

print("Connections: ", conns)
print("All-reduce group size: ", groupsize)
print("Flowsize: ", flowsize, "bytes")
print("Random Seed", randseed)

f = open(filename, "w")
print("Nodes", nodes, file=f)
print("Connections", conns * (2*groupsize - 1), file=f)
print("Triggers",    conns * (2*groupsize - 2), file=f)

groups = conns // groupsize
print("Groups", groups)

id     = 0
trig_id = 1

for group in range(groups):
    print("group:", group)
    # Define the ring as the consecutive block [group*groupsize .. group*groupsize + groupsize - 1]
    start   = group * groupsize
    groupsrcs = list(range(start, start + groupsize))
    if locality == 1:
        groupsrcs.sort()
    print(groupsrcs)

    for s in range(groupsize):
        print()
        for d in range(1, 2*groupsize):
            id += 1
            src = (s + d - 1) % groupsize
            dst = (s + d)     % groupsize

            out = f"{groupsrcs[src]}->{groupsrcs[dst]} id {id}"
            if d == 1:
                out += " start 0"
            else:
                out += f" trigger {trig_id}"
                trig_id += 1

            out += f" size {flowsize}"
            if d != 2*groupsize - 1:
                out += f" send_done_trigger {trig_id}"

            print(out, file=f)
            print(groupsrcs[src], "->", groupsrcs[dst])

# finally, emit all triggers as one-shot
for t in range(1, trig_id):
    print(f"trigger id {t} oneshot", file=f)

f.close()
