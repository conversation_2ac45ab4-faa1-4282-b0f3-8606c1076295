python3 liortests/poctests.py roce connection_matrices/incast128/incast_rand_flowsize_incastratio_63_seed700.cm 128 128 700 incast > liortests/roce128/incast/roce128_incast_rand_flowsize_128_incastratio_63_seed700.out
python3 liortests/poctests.py roce connection_matrices/incast128/incast_rand_flowsize_incastratio_63_seed701.cm 128 128 701 incast > liortests/roce128/incast/roce128_incast_rand_flowsize_128_incastratio_63_seed701.out
python3 liortests/poctests.py roce connection_matrices/incast128/incast_rand_flowsize_incastratio_63_seed702.cm 128 128 702 incast > liortests/roce128/incast/roce128_incast_rand_flowsize_128_incastratio_63_seed702.out
python3 liortests/poctests.py roce connection_matrices/incast128/incast_rand_flowsize_incastratio_63_seed703.cm 128 128 703 incast > liortests/roce128/incast/roce128_incast_rand_flowsize_128_incastratio_63_seed703.out
python3 liortests/poctests.py roce connection_matrices/incast128/incast_rand_flowsize_incastratio_63_seed704.cm 128 128 704 incast > liortests/roce128/incast/roce128_incast_rand_flowsize_128_incastratio_63_seed704.out
python3 liortests/poctests.py roce connection_matrices/incast128/incast_rand_flowsize_incastratio_63_seed705.cm 128 128 705 incast > liortests/roce128/incast/roce128_incast_rand_flowsize_128_incastratio_63_seed705.out
python3 liortests/poctests.py roce connection_matrices/incast128/incast_rand_flowsize_incastratio_63_seed706.cm 128 128 706 incast > liortests/roce128/incast/roce128_incast_rand_flowsize_128_incastratio_63_seed706.out
python3 liortests/poctests.py roce connection_matrices/incast128/incast_rand_flowsize_incastratio_63_seed715.cm 128 128 715 incast > liortests/roce128/incast/roce128_incast_rand_flowsize_128_incastratio_63_seed715.out
python3 liortests/poctests.py roce connection_matrices/incast128/incast_rand_flowsize_incastratio_63_seed720.cm 128 128 720 incast > liortests/roce128/incast/roce128_incast_rand_flowsize_128_incastratio_63_seed720.out
