#!/usr/bin/env python3
import subprocess 
import re
import sys
import time
import os
from datetime import datetime

# Define the protocol , base command , topology and ingress traffic pattern 

def extract_connections_from_cm_file(filename):
    with open(filename, 'r') as f:
        for line in f:
            if "Connections" in line:
             connection_value = line.strip().split(" ")[1]
    return connection_value

protocol = "ndp" # (roce | ndp | eqds)
collelctive = "allreduce" # (allreduce | all2all | incast | permutation)"
connection_matric = "connection_matrices/roce16/incast_rand_flowsize_16_seed16.cm"
print(f"number of input parameters is {len(sys.argv)}")
if len(sys.argv) < 6:
    print("Usage: python script.py <protocol> <connection_matric> <nodes> <conns> <seed> <collelctive>..")
    sys.exit(1)
protocol = sys.argv[1]
connection_matric = sys.argv[2]
nodes = sys.argv[3]
conns = sys.argv[4]
conns = extract_connections_from_cm_file(connection_matric)
seed = sys.argv[5]
collelctive = sys.argv[6]

print(f"protocol is {protocol}, collelctive is {collelctive}, connection matric is {connection_matric} , number of nodes is {nodes} , numbe of connections is {conns}")
 
    # ... process the arguments
#RoCE

#For oversubscribed topology
# 18.02.25 roce_base_cmd = f"./htsim_roce -topo topologies/leaf_spine_{nodes}.topo -nodes {nodes} -conns {conns} -strat ecmp_host -paths 1 -log sink -end 50000000 -mtu 3000 -seed {seed}"
# roce_base_cmd = f"./htsim_roce -topo topologies/fat_tree_{nodes}.topo -nodes {nodes} -conns {conns} -strat ecmp_host -paths 1 -log sink -end 50000000 -mtu 3000 -seed {seed}"
# - for ecmp_host : 
#       roce_base_cmd = f"./htsim_roce -topo topologies/fat_tree_{nodes}.topo -nodes {nodes} -conns {conns} -strat ecmp_host -paths 1 -log sink -end 50000000 -mtu 3000"
roce_base_cmd = f"./htsim_roce -topo topologies/fat_tree_{nodes}.topo -nodes {nodes} -conns {conns} -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt"
## print(f"roce_base_cmd is {roce_base_cmd}")
## roce all2all
#conns = 128
#roce_base_cmd = "./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 128 -strat ecmp_host -paths 1 -log sink -end 30000 -mtu 3000 -seed 13"
#conns = 64
#roce_base_cmd = "./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 8127 -strat ecmp_host -paths 1 -log sink -end 60000 -mtu 3000 -seed 13"
#conns = 240
# Oversubscribed8 : roce_base_cmd = "./htsim_roce -topo topologies/leaf_spine_lior.topo -nodes 16 -conns 16 -strat ecmp_host -paths 1 -log sink -end 300000 -mtu 3000"
#roce_base_cmd = "./htsim_roce -topo topologies/fat_tree_16.topo -nodes 16 -conns 16 -strat ecmp_host -paths 1 -log sink -end 300000 -mtu 3000"

#roce allreduced
#roce_base_cmd = "./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 128 -strat ecmp_host -paths 256 -log sink -end 300000 -mtu 3000 -seed 13"

##roce in-cast 256:1
#conns = 255
#roce_base_cmd = "./htsim_roce -topo topologies/fat_tree_256.topo -nodes 256 -conns 255 -tm connection_matrices/incast_256.cm -strat ecmp_host -paths 30 -log sink -end 100000 -mtu 3000 -start_delta 1 -ar_sticky_delta 1 -seed 13"

##roce in-cast 1023:1
#conns = 1023
#roce_base_cmd = "./htsim_roce -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 1023 -tm connection_matrices/incast_1024.cm -strat ecmp_host -paths 300 -log sink -end 300000 -mtu 3000 -start_delta 1 -ar_sticky_delta 1 -seed 13"
#roce_base_cmd = "./htsim_roce -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 1023 -strat ecmp_host -paths 256 -log sink -end 300000 -mtu 3000 -seed 13"

#conns = 256
#roce_base_cmd = "./htsim_roce -topo topologies/fat_tree_256.topo -nodes 256 -conns 256 -tm connection_matrices/all2all_256.cm -strat ecmp_host -paths 30 -log sink -end 30000 -mtu 3000 -switch_latency 1 -hop_latency 1 -start_delta 1 -ar_sticky_delta 1 -seed 13"

## RoCE permmutatons
#conns = 16
#roce_base_cmd = "./htsim_roce -topo topologies/leaf_spine_lior.topo -nodes 16 -conns 16 -strat ecmp_host -paths 1 -log sink -end 30000 -mtu 3000"

#conns = 1024
#roce_base_cmd = "./htsim_roce -nodes 1024 -conns 1024 -tm connection_matrices/perm_1024n_1024c_0u_2000000b.cm -strat ecmp_host -paths 256 -log sink -end 30000 -mtu 4000 -seed 13"

# RoCE Allreduce 
#conns = 1024
#roce_base_cmd = "./htsim_roce -tm connection_matrices/allreduced_1024n_1024c_64_2000000b.cm -strat perm -paths 1 -nodes 1024 -conns 130048 -mtu 4000 -log sink -end 50000"

#conns = 8192
#roce_base_cmd = "./htsim_roce -nodes 8192 -conns 8192 -tm connection_matrices/perm_8192n_8192c_0u_2000000b.cm -strat ecmp_host -paths 256 -log sink -end 30000 -mtu 4000 -seed 13"

# NDP
##ndp in-cast 15:1
#conns = 240
#ndp_base_cmd = "./htsim_ndp -topo topologies/leaf_spine_lior.topo -tm connection_matrices/15.cm -strat perm -nodes 16 -conns 15 -mtu 3000 -log sink -end 30000"
#conns = 128
#ndp_base_cmd = "./htsim_ndp -topo topologies/leaf_spine_lior.topo -strat ecmp -paths 1 -nodes 16 -conns 128 -mtu 3000 -log sink -end 30000"
ndp_base_cmd = f"./htsim_ndp -topo topologies/fat_tree_{nodes}.topo -nodes {nodes} -conns {conns} -strat ecmp_ar  -end 50000000 -mtu 3000"

#ndp_base_cmd = "./htsim_ndp -topo topologies/leaf_spine_lior.topo -strat ecmp -paths 1 -nodes 16 -conns 16 -mtu 3000 -log sink -end 10000"

##ndp in-cast 128:1
#conns = 127
#ndp_base_cmd = "./htsim_ndp -topo topologies/fat_tree_128.topo -tm connection_matrices/incast_128.cm -strat perm -nodes 128 -conns 127 -mtu 3000 -log sink -end 30000"
#ndp_base_cmd = "./htsim_ndp -topo topologies/fat_tree_128.topo -strat ecmp -paths 32 -nodes 128 -conns 127 -mtu 3000 -log sink -end 30000"

#ndp all2all
#conns = 16
#ndp_base_cmd = "./htsim_ndp -topo topologies/leaf_spine_lior.topo -strat ecmp -paths 1 -nodes 16 -conns 16 -mtu 4000 -log sink -end 50000"
#conns = 128
#ndp_base_cmd = "./htsim_ndp -topo topologies/fat_tree_128.topo -strat ecmp -paths 32 -nodes 128 -conns 128 -mtu 4000 -log sink -end 50000"
#ndp allreduced
#conns = 128
#ndp_base_cmd = "./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 128 -strat ecmp -paths 16 -log sink -end 300000 -mtu 3000 -seed 13"

#ndp all2all
#conns = 256
#ndp_base_cmd = "./htsim_ndp -topo topologies/fat_tree_256.topo -tm connection_matrices/all2all_256n_256c_0u_150000b.cm -strat ecmp -paths 32 -nodes 256 -conns 256 -mtu 4000 -log sink -end 50000"


##ndp in-cast 1024:1
#conns = 1023
#ndp_base_cmd = "./htsim_ndp -topo topologies/fat_tree_1024.topo -tm connection_matrices/incast_1024.cm -strat perm -nodes 1024 -conns 1023 -mtu 3000 -log sink -end 300000"
#ndp_base_cmd = "./htsim_ndp -topo topologies/fat_tree_1024.topo -strat ecmp_host -paths 256 -nodes 1024 -conns 1023 -mtu 3000 -log sink -end 300000"

##permutation 1024
#conns = 128
#ndp_base_cmd = "./htsim_ndp -topo topologies/fat_tree_1024.topo -strat perm -nodes 128 -conns 128 -mtu 3000 -log sink -end 30000"


#conns = 1024
#ndp_base_cmd = "./htsim_ndp -topo topologies/fat_tree_1024.topo -tm connection_matrices/perm_1024n_1024c_0u_2000000b.cm -strat perm -nodes 1024 -conns 1024 -mtu 3000 -log sink -end 30000"
#ndp_base_cmd = "./htsim_ndp -topo topologies/fat_tree_1024.topo -strat perm -nodes 1024 -conns 1024 -mtu 3000 -log sink -end 30000"

#conns = 8192
#ndp_base_cmd = "./htsim_ndp -tm connection_matrices/perm_8192n_8192c_0u_2000000b.cm -log sink -linkspeed 100000 -strat ecmp_host_ecn -paths 256 -nodes 8192 -conns 8192 -mtu 4000 -end 3000 -logtime 0.01 -ecn_thresh 0.018"

##AllReduce1024 
#conns = 1024
#ndp_base_cmd = "./htsim_ndp -tm connection_matrices/allreduced_1024n_1024c_64_2000000b.cm -strat ecmp_host -paths 1 -nodes 1024 -conns 130048 -mtu 3000 -log sink -end 30000"

# EQDS
##eqds in-cast 15:1
#conns = 15
#eqds_base_cmd = "./htsim_eqds -topo topologies/leaf_spine_lior.topo -tm connection_matrices/15.cm -strat perm -nodes 16 -conns 15 -mtu 3000 -log sink -end 5000"
#eqds_base_cmd = "./htsim_eqds -topo topologies/leaf_spine_lior.topo -strat ecmp -paths 8 -nodes 16 -conns 15 -mtu 3000 -log sink -end 6000"

##eqds in-cast 128:1
#conns = 127
#eqds_base_cmd = "./htsim_eqds -topo topologies/fat_tree_128.topo -tm connection_matrices/incast_128.cm -strat perm -nodes 128 -conns 127 -mtu 3000 -log sink -end 30000"
#eqds_base_cmd = "./htsim_eqds -topo topologies/fat_tree_128.topo -strat ecmp_host -paths 16 -nodes 128 -conns 127 -mtu 3000 -log sink -end 30000"

#conns = 128
#eqds_base_cmd = "./htsim_eqds -topo topologies/fat_tree_128.topo -strat perm -nodes 128 -conns 128 -mtu 3000 -log sink -end 30000"
eqds_base_cmd = f"./htsim_eqds -topo topologies/fat_tree_{nodes}.topo -nodes {nodes} -conns {conns} -strat ecmp -paths 32 -log sink -mtu 3000  -end 60000"

#conns = 1024
#eqds_base_cmd = "./htsim_eqds -topo topologies/fat_tree_1024.topo -strat perm -nodes 1024 -conns 1023 -mtu 3000 -log sink -end 300000"
#eqds_base_cmd = "./htsim_eqds -topo topologies/fat_tree_1024.topo -strat ecmp -nodes 1024 -conns 1023 -mtu 3000 -log sink -end 300000"

#permutation 1024
#conns = 1024
#eqds_base_cmd = "./htsim_eqds -topo topologies/fat_tree_1024.topo -tm connection_matrices/perm_1024n_1024c_0u_2000000b.cm -strat perm -nodes 1024 -conns 1024 -mtu 3000 -log sink -end 30000"
#eqds_base_cmd = "./htsim_eqds -topo topologies/fat_tree_1024.topo -tm connection_matrices/perm_1024n_1024c_0u_2000000b.cm -strat perm -nodes 1024 -conns 1024 -mtu 3000 -log sink -end 300000"

#conns = 8192
#eqds_base_cmd = "./htsim_eqds -topo topologies/fat_tree_8192.topo -tm connection_matrices/perm_8192n_8192c_0u_2000000b.cm -strat perm -nodes 8192 -conns 8192 -mtu 3000 -log sink -end 30000"


# Define starting and ending values for -q
start_q = 100
end_q = 110
conns  = int(conns)

if protocol == "roce":
    base_cmd = roce_base_cmd
    param1_start_range = 5
    param1_end_range = 10
    param1_step = 1
    congestion_param1 = "pfc_threshold"
    param2_start_range = 5
    param2_end_range = 10
    param2_step = 1
    congestion_param2 = "spine_pfc_threshold"
    param3_start_range = 5
    param3_end_range = 10
    param3_step = 1
    congestion_param3 = "pfc_threshold_1"
    step = 1
    start_seed = 0
    end_seed = 1
    start_seed_path = 0
    end_seed_path = 1
    start_ar_sticky_delta = 7
    end_ar_sticky_delta = 13
elif protocol == "ndp":
    base_cmd = ndp_base_cmd
    param1_start_range = 3
    param1_end_range = 10
    param1_step = 1
    congestion_param1 = "pfc_threshold"
    param2_start_range = 9
    param2_end_range = 10
    param2_step = 1
    congestion_param2 = "spine_pfc_threshold"
    param3_start_range = 30
    param3_end_range = 70
    param3_step = 10
    congestion_param3 = "cwnd"
    start_seed = 0
    end_seed = 1
    step = 1
    start_seed_path = 0
    end_seed_path = 1
    start_ar_sticky_delta = 13
    end_ar_sticky_delta = 14
elif protocol == "eqds":
    base_cmd = eqds_base_cmd
    congestion_param1 = "cwnd"
    start_range = 15
    end_range = 105
    step = 10
else:
    print ("must select protocol")

fastestjct = 99999999
fastest_q = 0
fastest_congestion_param1 = 0
fastest_congestion_param2 = 0
fastest_congestion_param3 = 0
fastest_seed_hash = 0
fastest_seed_path = 0
fastest_ar_sticky_delta = 0
slowestjct = 0
slowest_q = 0
slowest_congestion_param1 = 0
slowest_congestion_param2 = 0
slowest_congestion_param3 = 0
slowest_seed_hash = 0
slowest_seed_path = 0
slowest_ar_sticky_delta = 0
finished_count = 0
dropped_count = 0
# Initialize an empty list to store results
results = []


def extract_finished_at_value(input_string):
  # Use regular expressions to find lines containing "finished at"
  finished_at_lines = re.findall(r"finished at (\d+)", input_string)
  # Extract the "finished at" values and convert them to floats
  finished_at_values = [float(value) for value in finished_at_lines]
  
  totalbytes_at_lines = re.findall(r"total bytes (\d+)", input_string)
  total_bytes =  [float(value) for value in totalbytes_at_lines]
  
  # Find  min and max values
  if finished_at_values:
    return min(finished_at_values) , max(finished_at_values), sum(total_bytes)
  else:
    return None

def extract_rtx_value(line):
  """Extracts the value of Rtx from a given line with the format : New: 45692 Rtx: 143 RTS: 0 Bounced: 0.
  Args:
    line: The input line.
  Returns:
    The value of Rtx, or None if not found.
  """
  match = re.search(r"Rtx:\s+(\d+)", line)
  if match:
    return int(match.group(1))
  else:
    return None
  
def extract_new_packets(line):
  """Extracts the value of New from a given line with the format : New: 45692 Rtx: 143 RTS: 0 Bounced: 0.
  Args:
    line: The input line.
  Returns:
    The value of New, or None if not found.
  """
  match = re.search(r"New:\s+(\d+)", line)
  if match:
    return int(match.group(1))
  else:
    return None

def extract_max_HostQsize_value(input_string):
  # Use regular expressions to find lines containing "Host Queue size"
  HostQsize_at_lines = re.findall(r"Host Queue size (\d+)", input_string)
  # Extract the "finished at" values and convert them to floats
  HostQsize_at_values = [float(value) for value in HostQsize_at_lines]

  # Find  min and max values
  if HostQsize_at_values:
    return max(HostQsize_at_values)
  else:
    return None

# Get current timestamp in a filename-friendly format
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")  # e.g., 20250409_172312


#  logfilename = f"liortests/{protocol}{nodes}/incast/{protocol}{nodes}_incast_rand_flowsize_{nodes}_{timestamp}.out"

logfilename = f"liortests/{protocol}{nodes}/{collelctive}/{protocol}{nodes}_{collelctive}_rand_flowsize_{nodes}_{timestamp}_seed{seed}.out"
logdir = os.path.dirname(logfilename)
os.makedirs(logdir, exist_ok=True)    # ← make sure the folder exists

# Loop through each value of -pfc_thresholds for RoCE
for seed_path in range (start_seed_path, end_seed_path, +step):
  for ar_sticky_delta in range (start_ar_sticky_delta, end_ar_sticky_delta, +step):
    for seed_hash in range (start_seed, end_seed, +step):
      for congestion_param1 in range (param1_start_range, param1_end_range, +param1_step):
    # Loop through each value of -pfc_thresholds for RoCE
        if protocol == "roce" : 
          congestion_param3 = congestion_param1 # FOR Roce  to run faster use same pfc for leaf and spines , switch 0 and switch 1
          congestion_param2 = congestion_param1 # FOR roce to reduce number of iterations use same pfc for leaf and spines
        else : 
          congestion_param2 = congestion_param1 # FOR NDP to run fasteruse same pfc for leaf and spines , switch 0 and switch 1
          # Loop through each value of -spine_pfc_thresholds for RoCE
        # for congestion_param2 in range (param2_start_range, param2_end_range, +param2_step):
          # Loop through each value of CWND for NDP
          #for congestion_param3 in range (param3_start_range, param3_end_range, +param3_step):
            # Loop through each value of -pfc_thresholds_1 for RoCE
        for q in range(start_q, end_q, +10):
               #Loop through each value of -q
                # Build the complete command with the current -q value
                if protocol == "roce":
                    full_cmd = f"{base_cmd} -tm {connection_matric} -q {q} -seed_path {seed_path} -seed {seed_hash} -ar_sticky_delta {ar_sticky_delta} -pfc_thresholds {congestion_param1} {congestion_param1+1} -spine_pfc_thresholds {congestion_param2} {congestion_param2+1} -pfc_thresholds_1 {congestion_param3} {congestion_param3+1} "
                    # full_cmd = f"{base_cmd} -q {q} -pfc_thresholds {congestion_param1} {congestion_param1+1}"
                    print (f"roce full command is : {full_cmd}")
                elif protocol in ("ndp" , "eqds"):
                    full_cmd = f"{base_cmd} -tm {connection_matric} -q {q} -cwnd {congestion_param3} -ar_sticky_delta {ar_sticky_delta} -pfc_thresholds {congestion_param1} {congestion_param1+1} -spine_pfc_thresholds {congestion_param2} {congestion_param2+1}"
                #Run the command with pipe to capture only the output for grep
                    print (f"ndp full command is : {full_cmd}")
                with subprocess.Popen(full_cmd.split(),stdout=subprocess.PIPE) as process:
                    #time.sleep(10)
                    filtered_output = subprocess.run(["grep", "-e", "dropped" , "-e" , "finished", "-e" , "Done", "-e" , "Rtx", "-e" , "Host Queue size"], stdin=process.stdout, capture_output=True, text=True).stdout
                    HostQsize_count = filtered_output.count("Host Queue size")
                    if  HostQsize_count > 0 :
                      max_HostQsize = extract_max_HostQsize_value(filtered_output)
                      #print(f"When q is : {q} and congestion_param1 is {congestion_param1} , there are {HostQsize_count} times that qsize exceeds host max qsize, the max Host Queue size found was: {max_HostQsize}")
                    dropped_count = filtered_output.count("dropped")
                    finished_count = filtered_output.count("finished")
                    if filtered_output.count("Done") > 0 and finished_count == conns: # ONLY if the simulation completed succesfuly and ALL flows completed - the results will be proccesed
                        # print(f"dropped_count: {dropped_count}, finished_count:{finished_count}")
                        # Extract the maximum and minimum "finished at" values
                        min_finished_at, max_finished_at, totalbytes = extract_finished_at_value(filtered_output)
                        # print(f"totalbytes: of simulation with q: {q} and congestion_param1: {congestion_param1} is: {totalbytes}")
                        # Append a dictionary containing q, finished_at, and numberofdrops
                        if protocol == "roce":
                            #dropped_count = filtered_output.count("dropped")
                            # print(f"For q: {q} , congestion_param1 : {congestion_param1} ar_sticky_delta : {ar_sticky_delta} seed_path : {seed_path} seed : {seed_hash}  : finished_count: {finished_count}, conns:{conns} max_finished_at: {max_finished_at}, min_finished_at : {min_finished_at} dropped_count : {dropped_count}")                                                       
                            # results.append({"q": q, "max_finished_at": max_finished_at, "min_finished_at": min_finished_at, "dropped_count": dropped_count, "finished_count": finished_count,"ar_sticky_delta": ar_sticky_delta, "pfc_threshold": congestion_param1 ,"spine_pfc_threshold": congestion_param2 ,"pfc_threshold_1": congestion_param3, "totalbytes" : totalbytes , "seed_hash" : seed_hash , "seed_path" : seed_path} )
                            # print(f"For q: {q} , max_finished_at : {max_finished_at}, min_finished_at: {min_finished_at}, dropped_count : {dropped_count}, finished_count: {finished_count}, ar_sticky_delta : {ar_sticky_delta}, pfc_threshold: {congestion_param1} ,spine_pfc_threshold : {congestion_param2} , pfc_threshold_1 : {congestion_param3}, totalbytes : {totalbytes} , seed_hash : {seed_hash} , seed_path : {seed_path}")
                            with open(logfilename, "a") as f:
                                f.write(f"Running command: {full_cmd}\n")
                                f.write(f"For q: {q}, max_finished_at: {max_finished_at}, min_finished_at: {min_finished_at}, "
                                        f"dropped_count: {dropped_count}, finished_count: {finished_count}, "
                                        f"ar_sticky_delta: {ar_sticky_delta}, pfc_threshold: {congestion_param1}, "
                                        f"spine_pfc_threshold: {congestion_param2}, pfc_threshold_1: {congestion_param3}, "
                                        f"totalbytes: {totalbytes}, seed_hash: {seed_hash}, seed_path: {seed_path}\n")


                            results.append({"q": q, "max_finished_at": max_finished_at, "min_finished_at": min_finished_at, "dropped_count": dropped_count, "finished_count": finished_count,"ar_sticky_delta": ar_sticky_delta, "pfc_threshold": congestion_param1 ,"spine_pfc_threshold": congestion_param2 ,"pfc_threshold_1": congestion_param3, "totalbytes" : totalbytes , "seed_hash" : seed_hash , "seed_path" : seed_path} )
                        elif protocol in("ndp","eqds"):
                            with open(logfilename, "a") as f:
                                f.write(f"Running command: {full_cmd}\n")
                                f.write(f"For q: {q}, max_finished_at: {max_finished_at}, min_finished_at: {min_finished_at}, "
                                        f"dropped_count: {dropped_count}, finished_count: {finished_count}, "
                                        f"ar_sticky_delta: {ar_sticky_delta}, pfc_threshold: {congestion_param1}, " 
                                        f"spine_pfc_threshold: {congestion_param2}, cwnd: {congestion_param3}, "
                                        f"totalbytes: {totalbytes}\n")
                            rtx_value = extract_rtx_value(filtered_output)
                            new_packets = extract_new_packets(filtered_output)
                            results.append({"q": q, "max_finished_at": max_finished_at, "min_finished_at": min_finished_at, "finished_count": finished_count , "pfc_threshold": congestion_param1, "spine_pfc_threshold": congestion_param2, "cwnd": congestion_param3, "ar_sticky_delta": ar_sticky_delta, "rtx_value" : rtx_value, "new_packets" : new_packets})
                        if  max_finished_at < fastestjct and dropped_count == 0 and finished_count == conns:
                            fastestjct = max_finished_at
                            fastest_q = q
                            fastest_congestion_param1 = congestion_param1
                            fastest_congestion_param2 = congestion_param2
                            fastest_congestion_param3 = congestion_param3
                            fastest_seed_hash = seed_hash
                            fastest_seed_path = seed_path
                            fastest_ar_sticky_delta = ar_sticky_delta
                        if max_finished_at > slowestjct :
                            slowestjct = max_finished_at
                            slowest_q = q
                            slowest_congestion_param1 = congestion_param1
                            slowest_congestion_param2 = congestion_param2
                            slowest_congestion_param3 = congestion_param3
                            slowest_seed_hash = seed_hash
                            slowest_seed_path = seed_path
                            slowest_ar_sticky_delta = ar_sticky_delta
# Print the results as a table using f-strings
for result in results:
    if protocol == "roce":
        print(f"For q = {result['q']} and pfc_thresholds {result['pfc_threshold']} {result['pfc_threshold']+1} spine_pfc_thresholds {result['spine_pfc_threshold']} {result['spine_pfc_threshold']+1} pfc_thresholds_1 {result['pfc_threshold_1']} {result['pfc_threshold_1']+1} ar_sticky_delta = {result['ar_sticky_delta']}  seed_hash = {result['seed_hash']}  seed_path = {result['seed_path']} there are {result['dropped_count']} drops, Total simualtion bytes {result['totalbytes']} and {result['finished_count']} flows completed . jct is: {result['max_finished_at']} us, fastest flow: {result['min_finished_at']}us")
    elif protocol in ("ndp", "eqds"):
        print(f"For q = {result['q']} ar_sticky_delta = {result['ar_sticky_delta']} pfc_thresholds {result['pfc_threshold']} {result['pfc_threshold']+1} spine_pfc_thresholds {result['spine_pfc_threshold']} and cwnd {result['cwnd']}. {result['finished_count']} flows completed. Number of packets {result['new_packets']} ,Number of RTX {result['rtx_value']} , JCT is: {result['max_finished_at']}us, fastest flow: {result['min_finished_at']}us ")
print ("")
if protocol == "roce":
    print (f"The shortest jct with 0 drops:  {fastestjct}us, with qsize = {fastest_q}, pcf_thresholds = {fastest_congestion_param1} {fastest_congestion_param1+1},pcf_thresholds_1 = {fastest_congestion_param2} {fastest_congestion_param2+1}, spine_pfc_threshold = {fastest_congestion_param3} {fastest_congestion_param3+1}, fastest_ar_sticky_delta = {fastest_ar_sticky_delta}, seed_hash = {fastest_seed_hash}, seed_path = {fastest_seed_path} ")
    print (f"The slowest jct :  {slowestjct}us, with {dropped_count} drops at qsize = {slowest_q}, pcf_thresholds = {slowest_congestion_param1} {slowest_congestion_param1+1},pcf_thresholds_1 = {slowest_congestion_param2} {slowest_congestion_param2+1}, spine_pfc_threshold = {slowest_congestion_param3} {slowest_congestion_param3+1}, slowest_ar_sticky_delta = {slowest_ar_sticky_delta} ,seed_hash = {slowest_seed_hash}, seed_path = {slowest_seed_path}")
elif protocol in ("ndp" , "eqds"):
    print (f"The shortest jct : {fastestjct}us, with qsize = {fastest_q}, cwnd = {fastest_congestion_param3}, ar_sticky_delta = {fastest_ar_sticky_delta} ,pcf_thresholds = {fastest_congestion_param1} {fastest_congestion_param1+1} ,spine_pfc_threshold = {fastest_congestion_param2} {fastest_congestion_param2+1}")
    print (f"The slowest jct : {slowestjct}us, with qsize = {slowest_q}, cwnd = {slowest_congestion_param3} , ar_sticky_delta = {slowest_ar_sticky_delta},pcf_thresholds = {slowest_congestion_param1} {slowest_congestion_param1+1} ,spine_pfc_threshold = {slowest_congestion_param2} {slowest_congestion_param2+1}")

print ("")
print ("------------------------------------ Thw above results are for the following base command ----------------------")
print (base_cmd)
print ("----------------------------------------------------------------------------------------------------------------")
