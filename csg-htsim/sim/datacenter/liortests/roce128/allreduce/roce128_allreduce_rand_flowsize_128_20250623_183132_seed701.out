Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 22051.0, min_finished_at: 239.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 7, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 20546.0, min_finished_at: 229.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 7, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 5, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 25168.0, min_finished_at: 228.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 7, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 5, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 27603.0, min_finished_at: 229.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 7, pfc_threshold: 5, spine_pfc_threshold: 8, pfc_threshold_1: 5, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 34070.0, min_finished_at: 226.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 7, pfc_threshold: 5, spine_pfc_threshold: 9, pfc_threshold_1: 5, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 16218.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 7, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 15156.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 7, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 16384.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 7, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 6, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 15019.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 7, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 6, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 15051.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 7, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 6, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 17283.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 7, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 7, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 15125.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 7, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 7, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 13748.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 7, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 7, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 15696.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 7, pfc_threshold: 7, spine_pfc_threshold: 8, pfc_threshold_1: 7, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 15484.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 7, pfc_threshold: 7, spine_pfc_threshold: 9, pfc_threshold_1: 7, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 8 9 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 19480.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 7, pfc_threshold: 8, spine_pfc_threshold: 5, pfc_threshold_1: 8, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 8 9 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 14533.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 7, pfc_threshold: 8, spine_pfc_threshold: 6, pfc_threshold_1: 8, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 8 9 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 15005.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 7, pfc_threshold: 8, spine_pfc_threshold: 7, pfc_threshold_1: 8, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 14068.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 7, pfc_threshold: 8, spine_pfc_threshold: 8, pfc_threshold_1: 8, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 8 9 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 14594.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 7, pfc_threshold: 8, spine_pfc_threshold: 9, pfc_threshold_1: 8, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 9 10 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 16151.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 7, pfc_threshold: 9, spine_pfc_threshold: 5, pfc_threshold_1: 9, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 9 10 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 15202.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 7, pfc_threshold: 9, spine_pfc_threshold: 6, pfc_threshold_1: 9, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 9 10 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 13771.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 7, pfc_threshold: 9, spine_pfc_threshold: 7, pfc_threshold_1: 9, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 9 10 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 13365.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 7, pfc_threshold: 9, spine_pfc_threshold: 8, pfc_threshold_1: 9, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 15551.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 7, pfc_threshold: 9, spine_pfc_threshold: 9, pfc_threshold_1: 9, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 21330.0, min_finished_at: 240.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 8, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 21050.0, min_finished_at: 228.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 8, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 5, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 37706.0, min_finished_at: 228.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 8, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 5, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 21553.0, min_finished_at: 229.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 8, pfc_threshold: 5, spine_pfc_threshold: 8, pfc_threshold_1: 5, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 23618.0, min_finished_at: 228.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 8, pfc_threshold: 5, spine_pfc_threshold: 9, pfc_threshold_1: 5, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 16709.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 8, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 15165.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 8, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 14932.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 8, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 6, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 15297.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 8, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 6, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 15985.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 8, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 6, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 16580.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 8, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 7, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 15444.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 8, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 7, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 14624.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 8, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 7, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 15043.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 8, pfc_threshold: 7, spine_pfc_threshold: 8, pfc_threshold_1: 7, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 15047.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 8, pfc_threshold: 7, spine_pfc_threshold: 9, pfc_threshold_1: 7, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 8 9 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 15727.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 8, pfc_threshold: 8, spine_pfc_threshold: 5, pfc_threshold_1: 8, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 8 9 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 14324.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 8, pfc_threshold: 8, spine_pfc_threshold: 6, pfc_threshold_1: 8, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 8 9 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 13873.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 8, pfc_threshold: 8, spine_pfc_threshold: 7, pfc_threshold_1: 8, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 14761.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 8, pfc_threshold: 8, spine_pfc_threshold: 8, pfc_threshold_1: 8, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 8 9 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 16101.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 8, pfc_threshold: 8, spine_pfc_threshold: 9, pfc_threshold_1: 8, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 9 10 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 16601.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 8, pfc_threshold: 9, spine_pfc_threshold: 5, pfc_threshold_1: 9, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 9 10 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 13456.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 8, pfc_threshold: 9, spine_pfc_threshold: 6, pfc_threshold_1: 9, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 9 10 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 15259.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 8, pfc_threshold: 9, spine_pfc_threshold: 7, pfc_threshold_1: 9, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 9 10 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 14746.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 8, pfc_threshold: 9, spine_pfc_threshold: 8, pfc_threshold_1: 9, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 13545.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 8, pfc_threshold: 9, spine_pfc_threshold: 9, pfc_threshold_1: 9, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 19075.0, min_finished_at: 236.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 9, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 22296.0, min_finished_at: 228.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 9, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 5, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 29319.0, min_finished_at: 228.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 9, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 5, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 26204.0, min_finished_at: 229.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 9, pfc_threshold: 5, spine_pfc_threshold: 8, pfc_threshold_1: 5, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 21744.0, min_finished_at: 228.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 9, pfc_threshold: 5, spine_pfc_threshold: 9, pfc_threshold_1: 5, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 17173.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 9, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 14428.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 9, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 15226.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 9, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 6, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 15148.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 9, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 6, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 16377.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 9, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 6, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 16505.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 9, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 7, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 15082.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 9, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 7, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 14529.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 9, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 7, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 15397.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 9, pfc_threshold: 7, spine_pfc_threshold: 8, pfc_threshold_1: 7, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 14009.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 9, pfc_threshold: 7, spine_pfc_threshold: 9, pfc_threshold_1: 7, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 8 9 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 16304.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 9, pfc_threshold: 8, spine_pfc_threshold: 5, pfc_threshold_1: 8, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 8 9 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 13984.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 9, pfc_threshold: 8, spine_pfc_threshold: 6, pfc_threshold_1: 8, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 8 9 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 14619.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 9, pfc_threshold: 8, spine_pfc_threshold: 7, pfc_threshold_1: 8, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 13965.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 9, pfc_threshold: 8, spine_pfc_threshold: 8, pfc_threshold_1: 8, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 8 9 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 13781.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 9, pfc_threshold: 8, spine_pfc_threshold: 9, pfc_threshold_1: 8, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 9 10 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 15688.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 9, pfc_threshold: 9, spine_pfc_threshold: 5, pfc_threshold_1: 9, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 9 10 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 13721.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 9, pfc_threshold: 9, spine_pfc_threshold: 6, pfc_threshold_1: 9, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 9 10 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 14203.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 9, pfc_threshold: 9, spine_pfc_threshold: 7, pfc_threshold_1: 9, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 9 10 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 13638.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 9, pfc_threshold: 9, spine_pfc_threshold: 8, pfc_threshold_1: 9, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 14028.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 9, pfc_threshold: 9, spine_pfc_threshold: 9, pfc_threshold_1: 9, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 21562.0, min_finished_at: 240.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 10, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 19869.0, min_finished_at: 228.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 10, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 5, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 20790.0, min_finished_at: 228.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 10, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 5, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 23985.0, min_finished_at: 229.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 10, pfc_threshold: 5, spine_pfc_threshold: 8, pfc_threshold_1: 5, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 37717.0, min_finished_at: 228.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 10, pfc_threshold: 5, spine_pfc_threshold: 9, pfc_threshold_1: 5, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 17012.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 10, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 14318.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 10, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 15522.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 10, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 6, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 14666.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 10, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 6, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 14724.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 10, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 6, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 16058.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 10, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 7, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 14642.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 10, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 7, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 13342.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 10, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 7, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 13600.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 10, pfc_threshold: 7, spine_pfc_threshold: 8, pfc_threshold_1: 7, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 12976.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 10, pfc_threshold: 7, spine_pfc_threshold: 9, pfc_threshold_1: 7, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 8 9 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 16129.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 10, pfc_threshold: 8, spine_pfc_threshold: 5, pfc_threshold_1: 8, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 8 9 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 13650.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 10, pfc_threshold: 8, spine_pfc_threshold: 6, pfc_threshold_1: 8, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 8 9 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 13804.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 10, pfc_threshold: 8, spine_pfc_threshold: 7, pfc_threshold_1: 8, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 13493.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 10, pfc_threshold: 8, spine_pfc_threshold: 8, pfc_threshold_1: 8, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 8 9 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 14086.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 10, pfc_threshold: 8, spine_pfc_threshold: 9, pfc_threshold_1: 8, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 9 10 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 15027.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 10, pfc_threshold: 9, spine_pfc_threshold: 5, pfc_threshold_1: 9, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 9 10 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 13491.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 10, pfc_threshold: 9, spine_pfc_threshold: 6, pfc_threshold_1: 9, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 9 10 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 13171.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 10, pfc_threshold: 9, spine_pfc_threshold: 7, pfc_threshold_1: 9, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 9 10 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 14039.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 10, pfc_threshold: 9, spine_pfc_threshold: 8, pfc_threshold_1: 9, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 13140.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 10, pfc_threshold: 9, spine_pfc_threshold: 9, pfc_threshold_1: 9, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 19823.0, min_finished_at: 240.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 11, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 20831.0, min_finished_at: 228.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 11, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 5, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 19167.0, min_finished_at: 230.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 11, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 5, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 19372.0, min_finished_at: 230.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 11, pfc_threshold: 5, spine_pfc_threshold: 8, pfc_threshold_1: 5, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 19089.0, min_finished_at: 228.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 11, pfc_threshold: 5, spine_pfc_threshold: 9, pfc_threshold_1: 5, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 18127.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 11, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 16408.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 11, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 14153.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 11, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 6, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 15699.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 11, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 6, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 14155.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 11, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 6, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 16147.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 11, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 7, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 13913.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 11, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 7, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 13848.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 11, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 7, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 14221.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 11, pfc_threshold: 7, spine_pfc_threshold: 8, pfc_threshold_1: 7, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 13677.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 11, pfc_threshold: 7, spine_pfc_threshold: 9, pfc_threshold_1: 7, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 8 9 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 15198.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 11, pfc_threshold: 8, spine_pfc_threshold: 5, pfc_threshold_1: 8, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 8 9 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 14823.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 11, pfc_threshold: 8, spine_pfc_threshold: 6, pfc_threshold_1: 8, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 8 9 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 13793.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 11, pfc_threshold: 8, spine_pfc_threshold: 7, pfc_threshold_1: 8, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 13143.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 11, pfc_threshold: 8, spine_pfc_threshold: 8, pfc_threshold_1: 8, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 8 9 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 16230.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 11, pfc_threshold: 8, spine_pfc_threshold: 9, pfc_threshold_1: 8, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 9 10 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 16160.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 11, pfc_threshold: 9, spine_pfc_threshold: 5, pfc_threshold_1: 9, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 9 10 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 13306.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 11, pfc_threshold: 9, spine_pfc_threshold: 6, pfc_threshold_1: 9, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 9 10 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 13289.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 11, pfc_threshold: 9, spine_pfc_threshold: 7, pfc_threshold_1: 9, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 9 10 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 13443.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 11, pfc_threshold: 9, spine_pfc_threshold: 8, pfc_threshold_1: 9, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 12919.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 11, pfc_threshold: 9, spine_pfc_threshold: 9, pfc_threshold_1: 9, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 20647.0, min_finished_at: 239.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 12, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 18500.0, min_finished_at: 228.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 12, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 5, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 20505.0, min_finished_at: 228.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 12, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 5, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 19029.0, min_finished_at: 229.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 12, pfc_threshold: 5, spine_pfc_threshold: 8, pfc_threshold_1: 5, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 19495.0, min_finished_at: 228.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 12, pfc_threshold: 5, spine_pfc_threshold: 9, pfc_threshold_1: 5, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 16178.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 12, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 15110.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 12, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 14860.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 12, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 6, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 14319.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 12, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 6, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 13604.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 12, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 6, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 15894.0, min_finished_at: 172.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 12, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 7, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 14302.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 12, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 7, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 14223.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 12, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 7, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 14920.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 12, pfc_threshold: 7, spine_pfc_threshold: 8, pfc_threshold_1: 7, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 13989.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 12, pfc_threshold: 7, spine_pfc_threshold: 9, pfc_threshold_1: 7, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 8 9 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 15361.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 12, pfc_threshold: 8, spine_pfc_threshold: 5, pfc_threshold_1: 8, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 8 9 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 13351.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 12, pfc_threshold: 8, spine_pfc_threshold: 6, pfc_threshold_1: 8, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 8 9 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 14878.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 12, pfc_threshold: 8, spine_pfc_threshold: 7, pfc_threshold_1: 8, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 13445.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 12, pfc_threshold: 8, spine_pfc_threshold: 8, pfc_threshold_1: 8, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 8 9 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 13725.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 12, pfc_threshold: 8, spine_pfc_threshold: 9, pfc_threshold_1: 8, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 9 10 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 15992.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 12, pfc_threshold: 9, spine_pfc_threshold: 5, pfc_threshold_1: 9, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 9 10 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 14357.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 12, pfc_threshold: 9, spine_pfc_threshold: 6, pfc_threshold_1: 9, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 9 10 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 13220.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 12, pfc_threshold: 9, spine_pfc_threshold: 7, pfc_threshold_1: 9, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 9 10 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 12770.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 12, pfc_threshold: 9, spine_pfc_threshold: 8, pfc_threshold_1: 9, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 13068.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 3968, ar_sticky_delta: 12, pfc_threshold: 9, spine_pfc_threshold: 9, pfc_threshold_1: 9, totalbytes: 7939968000.0, seed_hash: 0, seed_path: 0
