number of input parameters is 7
protocol is roce, collelctive is allreduce, connection matric is connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm , number of nodes is 128 , numbe of connections is 1920
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 8 9 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 8 9 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 8 9 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 8 9 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 9 10 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 9 10 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 9 10 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 9 10 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 8 9 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 8 9 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 8 9 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 8 9 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 9 10 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 9 10 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 9 10 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 9 10 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 8 9 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 8 9 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 8 9 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 8 9 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 9 10 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 9 10 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 9 10 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 9 10 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 8 9 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 8 9 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 8 9 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 8 9 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 9 10 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 9 10 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 9 10 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 9 10 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 8 9 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 8 9 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 8 9 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 8 9 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 9 10 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 9 10 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 9 10 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 9 10 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 5 6 pfc_thresholds_1 5 6 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 13644.0 us, fastest flow: 240.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 6 7 pfc_thresholds_1 5 6 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 14043.0 us, fastest flow: 234.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 7 8 pfc_thresholds_1 5 6 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 15680.0 us, fastest flow: 243.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 8 9 pfc_thresholds_1 5 6 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 18573.0 us, fastest flow: 231.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 9 10 pfc_thresholds_1 5 6 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 15784.0 us, fastest flow: 229.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 5 6 pfc_thresholds_1 6 7 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 10587.0 us, fastest flow: 172.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 6 7 pfc_thresholds_1 6 7 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8761.0 us, fastest flow: 172.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 7 8 pfc_thresholds_1 6 7 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 9511.0 us, fastest flow: 173.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 8 9 pfc_thresholds_1 6 7 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 10090.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 9 10 pfc_thresholds_1 6 7 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 10541.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 5 6 pfc_thresholds_1 7 8 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 10412.0 us, fastest flow: 170.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 6 7 pfc_thresholds_1 7 8 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 9108.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 7 8 pfc_thresholds_1 7 8 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 9464.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 8 9 pfc_thresholds_1 7 8 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 9340.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 9 10 pfc_thresholds_1 7 8 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 9006.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 5 6 pfc_thresholds_1 8 9 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 10583.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 6 7 pfc_thresholds_1 8 9 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8956.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 7 8 pfc_thresholds_1 8 9 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8834.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 8 9 pfc_thresholds_1 8 9 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 9179.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 9 10 pfc_thresholds_1 8 9 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8892.0 us, fastest flow: 172.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 5 6 pfc_thresholds_1 9 10 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 10679.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 6 7 pfc_thresholds_1 9 10 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 9156.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 7 8 pfc_thresholds_1 9 10 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8293.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 8 9 pfc_thresholds_1 9 10 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7956.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 9 10 pfc_thresholds_1 9 10 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8254.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 5 6 pfc_thresholds_1 5 6 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 14065.0 us, fastest flow: 242.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 6 7 pfc_thresholds_1 5 6 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 13703.0 us, fastest flow: 238.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 7 8 pfc_thresholds_1 5 6 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 13694.0 us, fastest flow: 237.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 8 9 pfc_thresholds_1 5 6 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 15609.0 us, fastest flow: 233.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 9 10 pfc_thresholds_1 5 6 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 20044.0 us, fastest flow: 227.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 5 6 pfc_thresholds_1 6 7 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 9275.0 us, fastest flow: 172.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 6 7 pfc_thresholds_1 6 7 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 9021.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 7 8 pfc_thresholds_1 6 7 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8528.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 8 9 pfc_thresholds_1 6 7 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8675.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 9 10 pfc_thresholds_1 6 7 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 9170.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 5 6 pfc_thresholds_1 7 8 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 10828.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 6 7 pfc_thresholds_1 7 8 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7995.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 7 8 pfc_thresholds_1 7 8 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8184.0 us, fastest flow: 170.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 8 9 pfc_thresholds_1 7 8 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7803.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 9 10 pfc_thresholds_1 7 8 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8637.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 5 6 pfc_thresholds_1 8 9 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 9695.0 us, fastest flow: 170.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 6 7 pfc_thresholds_1 8 9 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 10131.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 7 8 pfc_thresholds_1 8 9 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7864.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 8 9 pfc_thresholds_1 8 9 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 9071.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 9 10 pfc_thresholds_1 8 9 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8175.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 5 6 pfc_thresholds_1 9 10 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 11954.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 6 7 pfc_thresholds_1 9 10 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 9010.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 7 8 pfc_thresholds_1 9 10 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7589.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 8 9 pfc_thresholds_1 9 10 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7918.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 9 10 pfc_thresholds_1 9 10 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 9074.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 5 6 pfc_thresholds_1 5 6 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 11598.0 us, fastest flow: 241.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 6 7 pfc_thresholds_1 5 6 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 11800.0 us, fastest flow: 243.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 7 8 pfc_thresholds_1 5 6 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 20331.0 us, fastest flow: 241.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 8 9 pfc_thresholds_1 5 6 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 14490.0 us, fastest flow: 240.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 9 10 pfc_thresholds_1 5 6 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 13854.0 us, fastest flow: 239.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 5 6 pfc_thresholds_1 6 7 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 10279.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 6 7 pfc_thresholds_1 6 7 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8171.0 us, fastest flow: 172.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 7 8 pfc_thresholds_1 6 7 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7618.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 8 9 pfc_thresholds_1 6 7 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8319.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 9 10 pfc_thresholds_1 6 7 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 10212.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 5 6 pfc_thresholds_1 7 8 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 9859.0 us, fastest flow: 170.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 6 7 pfc_thresholds_1 7 8 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8196.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 7 8 pfc_thresholds_1 7 8 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7599.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 8 9 pfc_thresholds_1 7 8 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8519.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 9 10 pfc_thresholds_1 7 8 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8268.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 5 6 pfc_thresholds_1 8 9 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 9029.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 6 7 pfc_thresholds_1 8 9 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 10279.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 7 8 pfc_thresholds_1 8 9 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7638.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 8 9 pfc_thresholds_1 8 9 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7730.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 9 10 pfc_thresholds_1 8 9 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7565.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 5 6 pfc_thresholds_1 9 10 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 9454.0 us, fastest flow: 170.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 6 7 pfc_thresholds_1 9 10 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8170.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 7 8 pfc_thresholds_1 9 10 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7363.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 8 9 pfc_thresholds_1 9 10 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7646.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 9 10 pfc_thresholds_1 9 10 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8952.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 5 6 pfc_thresholds_1 5 6 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 12017.0 us, fastest flow: 242.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 6 7 pfc_thresholds_1 5 6 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 11979.0 us, fastest flow: 241.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 7 8 pfc_thresholds_1 5 6 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 12149.0 us, fastest flow: 240.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 8 9 pfc_thresholds_1 5 6 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 11340.0 us, fastest flow: 242.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 9 10 pfc_thresholds_1 5 6 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 12186.0 us, fastest flow: 234.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 5 6 pfc_thresholds_1 6 7 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 9145.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 6 7 pfc_thresholds_1 6 7 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8486.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 7 8 pfc_thresholds_1 6 7 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 9285.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 8 9 pfc_thresholds_1 6 7 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8104.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 9 10 pfc_thresholds_1 6 7 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7714.0 us, fastest flow: 172.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 5 6 pfc_thresholds_1 7 8 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 9103.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 6 7 pfc_thresholds_1 7 8 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7803.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 7 8 pfc_thresholds_1 7 8 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7926.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 8 9 pfc_thresholds_1 7 8 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7834.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 9 10 pfc_thresholds_1 7 8 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7757.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 5 6 pfc_thresholds_1 8 9 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 9869.0 us, fastest flow: 170.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 6 7 pfc_thresholds_1 8 9 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8164.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 7 8 pfc_thresholds_1 8 9 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7739.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 8 9 pfc_thresholds_1 8 9 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7235.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 9 10 pfc_thresholds_1 8 9 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 9176.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 5 6 pfc_thresholds_1 9 10 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8658.0 us, fastest flow: 170.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 6 7 pfc_thresholds_1 9 10 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8166.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 7 8 pfc_thresholds_1 9 10 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7787.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 8 9 pfc_thresholds_1 9 10 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7728.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 9 10 pfc_thresholds_1 9 10 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8302.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 5 6 pfc_thresholds_1 5 6 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 12130.0 us, fastest flow: 238.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 6 7 pfc_thresholds_1 5 6 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 10408.0 us, fastest flow: 243.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 7 8 pfc_thresholds_1 5 6 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 11674.0 us, fastest flow: 236.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 8 9 pfc_thresholds_1 5 6 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 11293.0 us, fastest flow: 230.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 9 10 pfc_thresholds_1 5 6 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 12999.0 us, fastest flow: 231.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 5 6 pfc_thresholds_1 6 7 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 9612.0 us, fastest flow: 172.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 6 7 pfc_thresholds_1 6 7 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8230.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 7 8 pfc_thresholds_1 6 7 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8332.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 8 9 pfc_thresholds_1 6 7 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8324.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 9 10 pfc_thresholds_1 6 7 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8209.0 us, fastest flow: 173.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 5 6 pfc_thresholds_1 7 8 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 9402.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 6 7 pfc_thresholds_1 7 8 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7728.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 7 8 pfc_thresholds_1 7 8 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8829.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 8 9 pfc_thresholds_1 7 8 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8094.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 9 10 pfc_thresholds_1 7 8 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7717.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 5 6 pfc_thresholds_1 8 9 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 9542.0 us, fastest flow: 172.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 6 7 pfc_thresholds_1 8 9 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7880.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 7 8 pfc_thresholds_1 8 9 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7694.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 8 9 pfc_thresholds_1 8 9 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7773.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 9 10 pfc_thresholds_1 8 9 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7401.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 5 6 pfc_thresholds_1 9 10 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 9936.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 6 7 pfc_thresholds_1 9 10 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8299.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 7 8 pfc_thresholds_1 9 10 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7981.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 8 9 pfc_thresholds_1 9 10 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7252.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 9 10 pfc_thresholds_1 9 10 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7185.0 us, fastest flow: 171.0us

The shortest jct with 0 drops:  7185.0us, with qsize = 100, pcf_thresholds = 9 10,pcf_thresholds_1 = 9 10, spine_pfc_threshold = 9 10, fastest_ar_sticky_delta = 11, seed_hash = 0, seed_path = 0 
The slowest jct :  20331.0us, with 0 drops at qsize = 100, pcf_thresholds = 5 6,pcf_thresholds_1 = 7 8, spine_pfc_threshold = 5 6, slowest_ar_sticky_delta = 9 ,seed_hash = 0, seed_path = 0

------------------------------------ Thw above results are for the following base command ----------------------
./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt
----------------------------------------------------------------------------------------------------------------
