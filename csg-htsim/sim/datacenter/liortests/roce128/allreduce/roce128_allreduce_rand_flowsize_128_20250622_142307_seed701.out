Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 13644.0, min_finished_at: 240.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 14043.0, min_finished_at: 234.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 15680.0, min_finished_at: 243.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 18573.0, min_finished_at: 231.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 5, spine_pfc_threshold: 8, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 15784.0, min_finished_at: 229.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 5, spine_pfc_threshold: 9, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 10587.0, min_finished_at: 172.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 8761.0, min_finished_at: 172.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 9511.0, min_finished_at: 173.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 10090.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 10541.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 10412.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 9108.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 9464.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 9340.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 7, spine_pfc_threshold: 8, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 9006.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 7, spine_pfc_threshold: 9, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 8 9 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 10583.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 8, spine_pfc_threshold: 5, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 8 9 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 8956.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 8, spine_pfc_threshold: 6, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 8 9 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 8834.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 8, spine_pfc_threshold: 7, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 9179.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 8, spine_pfc_threshold: 8, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 8 9 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 8892.0, min_finished_at: 172.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 8, spine_pfc_threshold: 9, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 9 10 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 10679.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 9, spine_pfc_threshold: 5, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 9 10 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 9156.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 9, spine_pfc_threshold: 6, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 9 10 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 8293.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 9, spine_pfc_threshold: 7, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 9 10 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 7956.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 9, spine_pfc_threshold: 8, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 8254.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 9, spine_pfc_threshold: 9, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 14065.0, min_finished_at: 242.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 13703.0, min_finished_at: 238.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 13694.0, min_finished_at: 237.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 15609.0, min_finished_at: 233.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 5, spine_pfc_threshold: 8, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 20044.0, min_finished_at: 227.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 5, spine_pfc_threshold: 9, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 9275.0, min_finished_at: 172.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 9021.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 8528.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 8675.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 9170.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 10828.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 7995.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 8184.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 7803.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 7, spine_pfc_threshold: 8, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 8637.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 7, spine_pfc_threshold: 9, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 8 9 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 9695.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 8, spine_pfc_threshold: 5, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 8 9 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 10131.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 8, spine_pfc_threshold: 6, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 8 9 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 7864.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 8, spine_pfc_threshold: 7, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 9071.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 8, spine_pfc_threshold: 8, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 8 9 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 8175.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 8, spine_pfc_threshold: 9, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 9 10 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 11954.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 9, spine_pfc_threshold: 5, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 9 10 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 9010.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 9, spine_pfc_threshold: 6, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 9 10 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 7589.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 9, spine_pfc_threshold: 7, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 9 10 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 7918.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 9, spine_pfc_threshold: 8, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 9074.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 9, spine_pfc_threshold: 9, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 11598.0, min_finished_at: 241.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 11800.0, min_finished_at: 243.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 20331.0, min_finished_at: 241.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 14490.0, min_finished_at: 240.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 5, spine_pfc_threshold: 8, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 13854.0, min_finished_at: 239.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 5, spine_pfc_threshold: 9, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 10279.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 8171.0, min_finished_at: 172.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 7618.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 8319.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 10212.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 9859.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 8196.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 7599.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 8519.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 7, spine_pfc_threshold: 8, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 8268.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 7, spine_pfc_threshold: 9, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 8 9 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 9029.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 8, spine_pfc_threshold: 5, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 8 9 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 10279.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 8, spine_pfc_threshold: 6, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 8 9 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 7638.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 8, spine_pfc_threshold: 7, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 7730.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 8, spine_pfc_threshold: 8, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 8 9 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 7565.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 8, spine_pfc_threshold: 9, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 9 10 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 9454.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 9, spine_pfc_threshold: 5, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 9 10 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 8170.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 9, spine_pfc_threshold: 6, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 9 10 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 7363.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 9, spine_pfc_threshold: 7, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 9 10 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 7646.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 9, spine_pfc_threshold: 8, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 8952.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 9, spine_pfc_threshold: 9, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 12017.0, min_finished_at: 242.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 11979.0, min_finished_at: 241.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 12149.0, min_finished_at: 240.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 11340.0, min_finished_at: 242.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 5, spine_pfc_threshold: 8, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 12186.0, min_finished_at: 234.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 5, spine_pfc_threshold: 9, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 9145.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 8486.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 9285.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 8104.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 7714.0, min_finished_at: 172.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 9103.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 7803.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 7926.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 7834.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 7, spine_pfc_threshold: 8, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 7757.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 7, spine_pfc_threshold: 9, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 8 9 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 9869.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 8, spine_pfc_threshold: 5, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 8 9 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 8164.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 8, spine_pfc_threshold: 6, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 8 9 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 7739.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 8, spine_pfc_threshold: 7, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 7235.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 8, spine_pfc_threshold: 8, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 8 9 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 9176.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 8, spine_pfc_threshold: 9, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 9 10 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 8658.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 9, spine_pfc_threshold: 5, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 9 10 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 8166.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 9, spine_pfc_threshold: 6, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 9 10 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 7787.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 9, spine_pfc_threshold: 7, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 9 10 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 7728.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 9, spine_pfc_threshold: 8, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 8302.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 9, spine_pfc_threshold: 9, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 12130.0, min_finished_at: 238.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 10408.0, min_finished_at: 243.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 11674.0, min_finished_at: 236.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 11293.0, min_finished_at: 230.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 5, spine_pfc_threshold: 8, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 12999.0, min_finished_at: 231.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 5, spine_pfc_threshold: 9, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 9612.0, min_finished_at: 172.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 8230.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 8332.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 8324.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 8209.0, min_finished_at: 173.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 9402.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 7728.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 8829.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 8094.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 7, spine_pfc_threshold: 8, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 7717.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 7, spine_pfc_threshold: 9, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 8 9 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 9542.0, min_finished_at: 172.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 8, spine_pfc_threshold: 5, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 8 9 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 7880.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 8, spine_pfc_threshold: 6, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 8 9 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 7694.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 8, spine_pfc_threshold: 7, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 7773.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 8, spine_pfc_threshold: 8, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 8 9 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 7401.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 8, spine_pfc_threshold: 9, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 9 10 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 9936.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 9, spine_pfc_threshold: 5, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 9 10 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 8299.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 9, spine_pfc_threshold: 6, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 9 10 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 7981.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 9, spine_pfc_threshold: 7, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 9 10 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 7252.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 9, spine_pfc_threshold: 8, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 7185.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 9, spine_pfc_threshold: 9, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
