Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 12486.0, min_finished_at: 242.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 13106.0, min_finished_at: 233.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 12441.0, min_finished_at: 239.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 14044.0, min_finished_at: 244.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 5, spine_pfc_threshold: 8, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 104551.0, min_finished_at: 244.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 5, spine_pfc_threshold: 9, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 10461.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 8465.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 8157.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 8647.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 8563.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 10073.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 9019.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 8768.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 7826.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 7, spine_pfc_threshold: 8, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 8951.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 7, spine_pfc_threshold: 9, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 8 9 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 10687.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 8, spine_pfc_threshold: 5, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 8 9 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 8538.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 8, spine_pfc_threshold: 6, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 8 9 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 8662.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 8, spine_pfc_threshold: 7, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 7548.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 8, spine_pfc_threshold: 8, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 8 9 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 8688.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 8, spine_pfc_threshold: 9, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 9 10 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 12247.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 9, spine_pfc_threshold: 5, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 9 10 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 8211.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 9, spine_pfc_threshold: 6, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 9 10 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 8031.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 9, spine_pfc_threshold: 7, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 9 10 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 8493.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 9, spine_pfc_threshold: 8, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 7593.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 9, spine_pfc_threshold: 9, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 12431.0, min_finished_at: 241.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 14134.0, min_finished_at: 237.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 12476.0, min_finished_at: 233.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 13436.0, min_finished_at: 234.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 5, spine_pfc_threshold: 8, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 20146.0, min_finished_at: 239.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 5, spine_pfc_threshold: 9, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 10330.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 7929.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 9163.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 9993.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 8762.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 10380.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 8454.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 8451.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 8702.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 7, spine_pfc_threshold: 8, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 9457.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 7, spine_pfc_threshold: 9, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 8 9 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 9633.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 8, spine_pfc_threshold: 5, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 8 9 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 7767.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 8, spine_pfc_threshold: 6, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 8 9 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 8431.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 8, spine_pfc_threshold: 7, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 8276.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 8, spine_pfc_threshold: 8, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 8 9 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 8596.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 8, spine_pfc_threshold: 9, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 9 10 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 10950.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 9, spine_pfc_threshold: 5, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 9 10 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 7840.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 9, spine_pfc_threshold: 6, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 9 10 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 8623.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 9, spine_pfc_threshold: 7, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 9 10 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 8909.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 9, spine_pfc_threshold: 8, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 8551.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 9, spine_pfc_threshold: 9, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 11716.0, min_finished_at: 242.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 15938.0, min_finished_at: 235.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 11802.0, min_finished_at: 236.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 11817.0, min_finished_at: 234.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 5, spine_pfc_threshold: 8, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 13279.0, min_finished_at: 236.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 5, spine_pfc_threshold: 9, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 9929.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 7924.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 8091.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 7682.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 8845.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 9504.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 8094.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 8637.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 8026.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 7, spine_pfc_threshold: 8, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 7431.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 7, spine_pfc_threshold: 9, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 8 9 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 9081.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 8, spine_pfc_threshold: 5, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 8 9 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 8084.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 8, spine_pfc_threshold: 6, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 8 9 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 9043.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 8, spine_pfc_threshold: 7, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 7041.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 8, spine_pfc_threshold: 8, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 8 9 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 7889.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 8, spine_pfc_threshold: 9, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 9 10 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 9278.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 9, spine_pfc_threshold: 5, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 9 10 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 7580.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 9, spine_pfc_threshold: 6, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 9 10 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 7919.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 9, spine_pfc_threshold: 7, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 9 10 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 7062.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 9, spine_pfc_threshold: 8, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 8459.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 9, spine_pfc_threshold: 9, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 11724.0, min_finished_at: 242.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 10734.0, min_finished_at: 237.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 11120.0, min_finished_at: 241.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 13200.0, min_finished_at: 242.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 5, spine_pfc_threshold: 8, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 11713.0, min_finished_at: 240.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 5, spine_pfc_threshold: 9, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 9686.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 7808.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 8269.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 7908.0, min_finished_at: 172.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 7754.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 10343.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 7965.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 7795.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 8675.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 7, spine_pfc_threshold: 8, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 7504.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 7, spine_pfc_threshold: 9, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 8 9 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 9296.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 8, spine_pfc_threshold: 5, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 8 9 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 8154.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 8, spine_pfc_threshold: 6, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 8 9 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 9304.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 8, spine_pfc_threshold: 7, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 7624.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 8, spine_pfc_threshold: 8, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 8 9 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 8196.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 8, spine_pfc_threshold: 9, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 9 10 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 9401.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 9, spine_pfc_threshold: 5, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 9 10 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 7260.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 9, spine_pfc_threshold: 6, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 9 10 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 7220.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 9, spine_pfc_threshold: 7, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 9 10 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 7346.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 9, spine_pfc_threshold: 8, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 8023.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 9, spine_pfc_threshold: 9, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 10703.0, min_finished_at: 243.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 11478.0, min_finished_at: 238.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 11715.0, min_finished_at: 242.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 12607.0, min_finished_at: 239.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 5, spine_pfc_threshold: 8, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 10835.0, min_finished_at: 244.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 5, spine_pfc_threshold: 9, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 9225.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 7858.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 7998.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 7365.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 8547.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 9107.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 8263.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 7602.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 7864.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 7, spine_pfc_threshold: 8, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 7053.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 7, spine_pfc_threshold: 9, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 8 9 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 8963.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 8, spine_pfc_threshold: 5, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 8 9 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 7914.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 8, spine_pfc_threshold: 6, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 8 9 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 8656.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 8, spine_pfc_threshold: 7, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 7620.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 8, spine_pfc_threshold: 8, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 8 9 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 7607.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 8, spine_pfc_threshold: 9, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 9 10 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 9741.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 9, spine_pfc_threshold: 5, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 9 10 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 7409.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 9, spine_pfc_threshold: 6, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 9 10 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 7690.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 9, spine_pfc_threshold: 7, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 9 10 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 7193.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 9, spine_pfc_threshold: 8, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 7164.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 9, spine_pfc_threshold: 9, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 11261.0, min_finished_at: 243.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 12, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 10775.0, min_finished_at: 242.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 12, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 9759.0, min_finished_at: 239.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 12, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 11296.0, min_finished_at: 239.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 12, pfc_threshold: 5, spine_pfc_threshold: 8, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 10811.0, min_finished_at: 240.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 12, pfc_threshold: 5, spine_pfc_threshold: 9, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 9942.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 12, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 7549.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 12, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 7695.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 12, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 8154.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 12, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 7293.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 12, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 8922.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 12, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 7402.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 12, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 7502.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 12, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 7420.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 12, pfc_threshold: 7, spine_pfc_threshold: 8, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 7631.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 12, pfc_threshold: 7, spine_pfc_threshold: 9, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 8 9 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 9004.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 12, pfc_threshold: 8, spine_pfc_threshold: 5, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 8 9 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 7935.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 12, pfc_threshold: 8, spine_pfc_threshold: 6, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 8 9 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 7651.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 12, pfc_threshold: 8, spine_pfc_threshold: 7, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 7433.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 12, pfc_threshold: 8, spine_pfc_threshold: 8, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 8 9 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 7895.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 12, pfc_threshold: 8, spine_pfc_threshold: 9, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 9 10 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 9975.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 12, pfc_threshold: 9, spine_pfc_threshold: 5, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 9 10 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 6999.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 12, pfc_threshold: 9, spine_pfc_threshold: 6, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 9 10 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 7352.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 12, pfc_threshold: 9, spine_pfc_threshold: 7, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 9 10 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 6962.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 12, pfc_threshold: 9, spine_pfc_threshold: 8, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed705.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 6949.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 12, pfc_threshold: 9, spine_pfc_threshold: 9, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
