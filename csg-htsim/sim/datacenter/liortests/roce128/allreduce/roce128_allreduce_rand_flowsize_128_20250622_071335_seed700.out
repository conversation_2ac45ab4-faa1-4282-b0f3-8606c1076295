Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 20653.0, min_finished_at: 239.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 15743.0, min_finished_at: 239.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 20505.0, min_finished_at: 240.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 26679.0, min_finished_at: 243.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 8, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 40615.0, min_finished_at: 239.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 9, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 16358.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 11097.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 12243.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 10205.0, min_finished_at: 172.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 21493.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 14772.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 11386.0, min_finished_at: 172.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 10075.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 10946.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 8, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 11507.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 9, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 8 9 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 17531.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 5, pfc_threshold: 8, spine_pfc_threshold: 5, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 8 9 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 10486.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 5, pfc_threshold: 8, spine_pfc_threshold: 6, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 8 9 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 11190.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 5, pfc_threshold: 8, spine_pfc_threshold: 7, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 12659.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 5, pfc_threshold: 8, spine_pfc_threshold: 8, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 8 9 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 17336.0, min_finished_at: 172.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 5, pfc_threshold: 8, spine_pfc_threshold: 9, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 9 10 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 15825.0, min_finished_at: 172.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 5, pfc_threshold: 9, spine_pfc_threshold: 5, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 9 10 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 14100.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 5, pfc_threshold: 9, spine_pfc_threshold: 6, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 9 10 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 11347.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 5, pfc_threshold: 9, spine_pfc_threshold: 7, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 9 10 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 11744.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 5, pfc_threshold: 9, spine_pfc_threshold: 8, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 15939.0, min_finished_at: 172.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 5, pfc_threshold: 9, spine_pfc_threshold: 9, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 19052.0, min_finished_at: 241.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 14946.0, min_finished_at: 234.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 24326.0, min_finished_at: 240.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 24283.0, min_finished_at: 236.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 8, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 26718.0, min_finished_at: 238.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 9, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 12617.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 10318.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 9399.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 9629.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 10112.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 14697.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 9157.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 9698.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 9434.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 8, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 10310.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 9, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 12455.0, min_finished_at: 172.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 5, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 9541.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 6, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 10279.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 7, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 9030.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 10474.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 9, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 12450.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 5, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 10144.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 6, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 8822.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 7, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 9628.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 8, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 12356.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 14027.0, min_finished_at: 243.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 14842.0, min_finished_at: 244.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 18635.0, min_finished_at: 233.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 33728.0, min_finished_at: 236.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 5, spine_pfc_threshold: 8, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 21157.0, min_finished_at: 231.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 5, spine_pfc_threshold: 9, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 12142.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 9625.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 8934.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 11805.0, min_finished_at: 172.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 10801.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 11241.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 8833.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 11444.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 9309.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 7, spine_pfc_threshold: 8, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 8902.0, min_finished_at: 172.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 7, spine_pfc_threshold: 9, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 8 9 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 10389.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 8, spine_pfc_threshold: 5, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 8 9 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 8524.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 8, spine_pfc_threshold: 6, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 8 9 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 8506.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 8, spine_pfc_threshold: 7, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 9746.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 8, spine_pfc_threshold: 8, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 8 9 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 8952.0, min_finished_at: 172.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 8, spine_pfc_threshold: 9, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 9 10 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 11856.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 9, spine_pfc_threshold: 5, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 9 10 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 9534.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 9, spine_pfc_threshold: 6, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 9 10 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 8244.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 9, spine_pfc_threshold: 7, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 9 10 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 8528.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 9, spine_pfc_threshold: 8, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 10376.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 7, pfc_threshold: 9, spine_pfc_threshold: 9, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 12885.0, min_finished_at: 241.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 13527.0, min_finished_at: 246.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 13415.0, min_finished_at: 237.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 15510.0, min_finished_at: 243.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 5, spine_pfc_threshold: 8, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 14821.0, min_finished_at: 235.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 5, spine_pfc_threshold: 9, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 10230.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 9758.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 9780.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 9969.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 8163.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 11558.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 9180.0, min_finished_at: 172.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 8384.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 9154.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 7, spine_pfc_threshold: 8, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 8482.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 7, spine_pfc_threshold: 9, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 8 9 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 11134.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 8, spine_pfc_threshold: 5, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 8 9 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 9142.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 8, spine_pfc_threshold: 6, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 8 9 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 8103.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 8, spine_pfc_threshold: 7, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 8397.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 8, spine_pfc_threshold: 8, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 8 9 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 8179.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 8, spine_pfc_threshold: 9, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 9 10 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 10004.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 9, spine_pfc_threshold: 5, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 9 10 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 8677.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 9, spine_pfc_threshold: 6, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 9 10 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 8449.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 9, spine_pfc_threshold: 7, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 9 10 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 8052.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 9, spine_pfc_threshold: 8, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 7573.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 8, pfc_threshold: 9, spine_pfc_threshold: 9, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 17695.0, min_finished_at: 242.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 15399.0, min_finished_at: 237.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 12530.0, min_finished_at: 244.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 14705.0, min_finished_at: 238.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 5, spine_pfc_threshold: 8, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 19549.0, min_finished_at: 234.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 5, spine_pfc_threshold: 9, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 9609.0, min_finished_at: 172.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 8636.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 10203.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 9732.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 8994.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 10746.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 8395.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 8495.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 7712.0, min_finished_at: 172.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 7, spine_pfc_threshold: 8, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 9432.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 7, spine_pfc_threshold: 9, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 8 9 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 9497.0, min_finished_at: 172.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 8, spine_pfc_threshold: 5, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 8 9 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 8521.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 8, spine_pfc_threshold: 6, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 8 9 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 8169.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 8, spine_pfc_threshold: 7, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 9565.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 8, spine_pfc_threshold: 8, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 8 9 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 7163.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 8, spine_pfc_threshold: 9, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 9 10 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 8719.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 9, spine_pfc_threshold: 5, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 9 10 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 10147.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 9, spine_pfc_threshold: 6, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 9 10 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 8291.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 9, spine_pfc_threshold: 7, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 9 10 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 8151.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 9, spine_pfc_threshold: 8, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 8020.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 9, pfc_threshold: 9, spine_pfc_threshold: 9, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 14552.0, min_finished_at: 244.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 13915.0, min_finished_at: 243.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 13226.0, min_finished_at: 240.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 14314.0, min_finished_at: 238.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 5, spine_pfc_threshold: 8, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 11179.0, min_finished_at: 242.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 5, spine_pfc_threshold: 9, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 9640.0, min_finished_at: 172.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 8139.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 7989.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 9563.0, min_finished_at: 172.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 8320.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 9919.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 7814.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 7481.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 7509.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 7, spine_pfc_threshold: 8, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 7250.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 7, spine_pfc_threshold: 9, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 8 9 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 10124.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 8, spine_pfc_threshold: 5, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 8 9 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 7753.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 8, spine_pfc_threshold: 6, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 8 9 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 8298.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 8, spine_pfc_threshold: 7, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 8070.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 8, spine_pfc_threshold: 8, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 8 9 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 7470.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 8, spine_pfc_threshold: 9, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 9 10 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 10484.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 9, spine_pfc_threshold: 5, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 9 10 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 8207.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 9, spine_pfc_threshold: 6, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 9 10 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 7733.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 9, spine_pfc_threshold: 7, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 9 10 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 7553.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 9, spine_pfc_threshold: 8, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 7582.0, min_finished_at: 172.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 10, pfc_threshold: 9, spine_pfc_threshold: 9, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 11334.0, min_finished_at: 243.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 10855.0, min_finished_at: 243.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 10943.0, min_finished_at: 239.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 12545.0, min_finished_at: 242.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 5, spine_pfc_threshold: 8, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 16260.0, min_finished_at: 242.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 5, spine_pfc_threshold: 9, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 9993.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 8625.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 8626.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 7906.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 8352.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 9443.0, min_finished_at: 172.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 8455.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 7687.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 7476.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 7, spine_pfc_threshold: 8, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 8705.0, min_finished_at: 173.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 7, spine_pfc_threshold: 9, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 8 9 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 8866.0, min_finished_at: 172.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 8, spine_pfc_threshold: 5, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 8 9 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 9207.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 8, spine_pfc_threshold: 6, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 8 9 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 7736.0, min_finished_at: 173.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 8, spine_pfc_threshold: 7, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 8313.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 8, spine_pfc_threshold: 8, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 8 9 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 7893.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 8, spine_pfc_threshold: 9, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 9 10 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 9212.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 9, spine_pfc_threshold: 5, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 9 10 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 8175.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 9, spine_pfc_threshold: 6, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 9 10 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 7715.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 9, spine_pfc_threshold: 7, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 9 10 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 7492.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 9, spine_pfc_threshold: 8, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 8599.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 11, pfc_threshold: 9, spine_pfc_threshold: 9, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 11976.0, min_finished_at: 240.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 12, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 12921.0, min_finished_at: 240.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 12, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 13713.0, min_finished_at: 242.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 12, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 11716.0, min_finished_at: 231.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 12, pfc_threshold: 5, spine_pfc_threshold: 8, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 13991.0, min_finished_at: 242.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 12, pfc_threshold: 5, spine_pfc_threshold: 9, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 9629.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 12, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 8038.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 12, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 8692.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 12, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 7973.0, min_finished_at: 172.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 12, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 7845.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 12, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 9511.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 12, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 8352.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 12, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 7932.0, min_finished_at: 172.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 12, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 8135.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 12, pfc_threshold: 7, spine_pfc_threshold: 8, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 8010.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 12, pfc_threshold: 7, spine_pfc_threshold: 9, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 8 9 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 9377.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 12, pfc_threshold: 8, spine_pfc_threshold: 5, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 8 9 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 8165.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 12, pfc_threshold: 8, spine_pfc_threshold: 6, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 8 9 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 7988.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 12, pfc_threshold: 8, spine_pfc_threshold: 7, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 8155.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 12, pfc_threshold: 8, spine_pfc_threshold: 8, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 8 9 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 8008.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 12, pfc_threshold: 8, spine_pfc_threshold: 9, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 9 10 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 10987.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 12, pfc_threshold: 9, spine_pfc_threshold: 5, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 9 10 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 8453.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 12, pfc_threshold: 9, spine_pfc_threshold: 6, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 9 10 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 7690.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 12, pfc_threshold: 9, spine_pfc_threshold: 7, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 9 10 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 8055.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 12, pfc_threshold: 9, spine_pfc_threshold: 8, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 7390.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 12, pfc_threshold: 9, spine_pfc_threshold: 9, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 13 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 11182.0, min_finished_at: 243.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 13, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 13 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 10387.0, min_finished_at: 242.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 13, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 13 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 12009.0, min_finished_at: 243.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 13, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 13 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 10987.0, min_finished_at: 238.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 13, pfc_threshold: 5, spine_pfc_threshold: 8, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 13 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 11551.0, min_finished_at: 241.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 13, pfc_threshold: 5, spine_pfc_threshold: 9, pfc_threshold_1: 5, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 13 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 9289.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 13, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 13 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 8372.0, min_finished_at: 172.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 13, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 13 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 7864.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 13, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 13 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 7817.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 13, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 13 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 8651.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 13, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 6, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 13 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 9444.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 13, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 13 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 8175.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 13, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 13 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 7750.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 13, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 13 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 7850.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 13, pfc_threshold: 7, spine_pfc_threshold: 8, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 13 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 7993.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 13, pfc_threshold: 7, spine_pfc_threshold: 9, pfc_threshold_1: 7, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 13 -pfc_thresholds 8 9 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 9928.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 13, pfc_threshold: 8, spine_pfc_threshold: 5, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 13 -pfc_thresholds 8 9 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 8285.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 13, pfc_threshold: 8, spine_pfc_threshold: 6, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 13 -pfc_thresholds 8 9 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 7585.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 13, pfc_threshold: 8, spine_pfc_threshold: 7, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 13 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 8414.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 13, pfc_threshold: 8, spine_pfc_threshold: 8, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 13 -pfc_thresholds 8 9 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 8345.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 13, pfc_threshold: 8, spine_pfc_threshold: 9, pfc_threshold_1: 8, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 9470.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 13, pfc_threshold: 9, spine_pfc_threshold: 5, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 8567.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 13, pfc_threshold: 9, spine_pfc_threshold: 6, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 8017.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 13, pfc_threshold: 9, spine_pfc_threshold: 7, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 7272.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 13, pfc_threshold: 9, spine_pfc_threshold: 8, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 7878.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 13, pfc_threshold: 9, spine_pfc_threshold: 9, pfc_threshold_1: 9, totalbytes: 3841920000.0, seed_hash: 0, seed_path: 0
