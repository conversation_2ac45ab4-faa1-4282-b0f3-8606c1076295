number of input parameters is 7
protocol is roce, collelctive is allreduce, connection matric is connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm , number of nodes is 128 , numbe of connections is 1920
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 8 9 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 8 9 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 8 9 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 8 9 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 9 10 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 9 10 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 9 10 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 9 10 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 8 9 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 8 9 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 8 9 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 8 9 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 9 10 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 9 10 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 9 10 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 9 10 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 8 9 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 8 9 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 8 9 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 8 9 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 9 10 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 9 10 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 9 10 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 9 10 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 8 9 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 8 9 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 8 9 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 8 9 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 9 10 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 9 10 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 9 10 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 9 10 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 8 9 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 8 9 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 8 9 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 8 9 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 9 10 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 9 10 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 9 10 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 9 10 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 8 9 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 8 9 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 8 9 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 8 9 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 9 10 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 9 10 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 9 10 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 9 10 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed704.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 5 6 pfc_thresholds_1 5 6 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 13342.0 us, fastest flow: 243.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 6 7 pfc_thresholds_1 5 6 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 13095.0 us, fastest flow: 234.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 7 8 pfc_thresholds_1 5 6 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 12655.0 us, fastest flow: 228.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 8 9 pfc_thresholds_1 5 6 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 15839.0 us, fastest flow: 231.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 9 10 pfc_thresholds_1 5 6 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 24577.0 us, fastest flow: 226.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 5 6 pfc_thresholds_1 6 7 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 10428.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 6 7 pfc_thresholds_1 6 7 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 10103.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 7 8 pfc_thresholds_1 6 7 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 9660.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 8 9 pfc_thresholds_1 6 7 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 12025.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 9 10 pfc_thresholds_1 6 7 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 9598.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 5 6 pfc_thresholds_1 7 8 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 10401.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 6 7 pfc_thresholds_1 7 8 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 9277.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 7 8 pfc_thresholds_1 7 8 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8932.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 8 9 pfc_thresholds_1 7 8 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8973.0 us, fastest flow: 172.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 9 10 pfc_thresholds_1 7 8 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8284.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 5 6 pfc_thresholds_1 8 9 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 11452.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 6 7 pfc_thresholds_1 8 9 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8234.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 7 8 pfc_thresholds_1 8 9 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8907.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 8 9 pfc_thresholds_1 8 9 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7778.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 9 10 pfc_thresholds_1 8 9 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8519.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 5 6 pfc_thresholds_1 9 10 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 10609.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 6 7 pfc_thresholds_1 9 10 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 9545.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 7 8 pfc_thresholds_1 9 10 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8414.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 8 9 pfc_thresholds_1 9 10 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 9255.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 9 10 pfc_thresholds_1 9 10 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 9717.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 5 6 pfc_thresholds_1 5 6 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 12201.0 us, fastest flow: 244.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 6 7 pfc_thresholds_1 5 6 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 13829.0 us, fastest flow: 230.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 7 8 pfc_thresholds_1 5 6 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 14459.0 us, fastest flow: 227.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 8 9 pfc_thresholds_1 5 6 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 20146.0 us, fastest flow: 234.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 9 10 pfc_thresholds_1 5 6 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 35945.0 us, fastest flow: 227.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 5 6 pfc_thresholds_1 6 7 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 10288.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 6 7 pfc_thresholds_1 6 7 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 9312.0 us, fastest flow: 172.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 7 8 pfc_thresholds_1 6 7 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8888.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 8 9 pfc_thresholds_1 6 7 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8803.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 9 10 pfc_thresholds_1 6 7 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 9831.0 us, fastest flow: 172.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 5 6 pfc_thresholds_1 7 8 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 10788.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 6 7 pfc_thresholds_1 7 8 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 9701.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 7 8 pfc_thresholds_1 7 8 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 9423.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 8 9 pfc_thresholds_1 7 8 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 9423.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 9 10 pfc_thresholds_1 7 8 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8992.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 5 6 pfc_thresholds_1 8 9 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 10195.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 6 7 pfc_thresholds_1 8 9 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8513.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 7 8 pfc_thresholds_1 8 9 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7961.0 us, fastest flow: 172.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 8 9 pfc_thresholds_1 8 9 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8072.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 9 10 pfc_thresholds_1 8 9 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7983.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 5 6 pfc_thresholds_1 9 10 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 10492.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 6 7 pfc_thresholds_1 9 10 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8647.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 7 8 pfc_thresholds_1 9 10 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8368.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 8 9 pfc_thresholds_1 9 10 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8263.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 9 10 pfc_thresholds_1 9 10 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8838.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 5 6 pfc_thresholds_1 5 6 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 13490.0 us, fastest flow: 243.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 6 7 pfc_thresholds_1 5 6 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 12186.0 us, fastest flow: 231.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 7 8 pfc_thresholds_1 5 6 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 12972.0 us, fastest flow: 226.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 8 9 pfc_thresholds_1 5 6 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 15653.0 us, fastest flow: 229.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 9 10 pfc_thresholds_1 5 6 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 20972.0 us, fastest flow: 232.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 5 6 pfc_thresholds_1 6 7 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 9668.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 6 7 pfc_thresholds_1 6 7 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8855.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 7 8 pfc_thresholds_1 6 7 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8567.0 us, fastest flow: 172.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 8 9 pfc_thresholds_1 6 7 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7782.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 9 10 pfc_thresholds_1 6 7 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8973.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 5 6 pfc_thresholds_1 7 8 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 10012.0 us, fastest flow: 172.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 6 7 pfc_thresholds_1 7 8 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7888.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 7 8 pfc_thresholds_1 7 8 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7500.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 8 9 pfc_thresholds_1 7 8 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8610.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 9 10 pfc_thresholds_1 7 8 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8272.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 5 6 pfc_thresholds_1 8 9 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 9390.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 6 7 pfc_thresholds_1 8 9 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8089.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 7 8 pfc_thresholds_1 8 9 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8550.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 8 9 pfc_thresholds_1 8 9 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7896.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 9 10 pfc_thresholds_1 8 9 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8228.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 5 6 pfc_thresholds_1 9 10 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 9654.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 6 7 pfc_thresholds_1 9 10 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 9217.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 7 8 pfc_thresholds_1 9 10 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8275.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 8 9 pfc_thresholds_1 9 10 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7766.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 9 10 pfc_thresholds_1 9 10 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7651.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 5 6 pfc_thresholds_1 5 6 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 12081.0 us, fastest flow: 242.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 6 7 pfc_thresholds_1 5 6 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 10914.0 us, fastest flow: 232.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 7 8 pfc_thresholds_1 5 6 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 16674.0 us, fastest flow: 227.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 8 9 pfc_thresholds_1 5 6 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 12702.0 us, fastest flow: 230.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 9 10 pfc_thresholds_1 5 6 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 14207.0 us, fastest flow: 234.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 5 6 pfc_thresholds_1 6 7 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 9628.0 us, fastest flow: 170.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 6 7 pfc_thresholds_1 6 7 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8992.0 us, fastest flow: 172.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 7 8 pfc_thresholds_1 6 7 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8851.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 8 9 pfc_thresholds_1 6 7 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 10034.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 9 10 pfc_thresholds_1 6 7 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8892.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 5 6 pfc_thresholds_1 7 8 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8960.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 6 7 pfc_thresholds_1 7 8 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7794.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 7 8 pfc_thresholds_1 7 8 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8321.0 us, fastest flow: 172.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 8 9 pfc_thresholds_1 7 8 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7959.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 9 10 pfc_thresholds_1 7 8 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7590.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 5 6 pfc_thresholds_1 8 9 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8750.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 6 7 pfc_thresholds_1 8 9 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7843.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 7 8 pfc_thresholds_1 8 9 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8072.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 8 9 pfc_thresholds_1 8 9 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8216.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 9 10 pfc_thresholds_1 8 9 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8184.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 5 6 pfc_thresholds_1 9 10 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 9092.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 6 7 pfc_thresholds_1 9 10 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7657.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 7 8 pfc_thresholds_1 9 10 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7549.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 8 9 pfc_thresholds_1 9 10 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7658.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 9 10 pfc_thresholds_1 9 10 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8935.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 5 6 pfc_thresholds_1 5 6 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 11255.0 us, fastest flow: 236.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 6 7 pfc_thresholds_1 5 6 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 10632.0 us, fastest flow: 231.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 7 8 pfc_thresholds_1 5 6 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 11916.0 us, fastest flow: 227.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 8 9 pfc_thresholds_1 5 6 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 12705.0 us, fastest flow: 230.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 9 10 pfc_thresholds_1 5 6 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 12259.0 us, fastest flow: 231.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 5 6 pfc_thresholds_1 6 7 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 10596.0 us, fastest flow: 170.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 6 7 pfc_thresholds_1 6 7 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8675.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 7 8 pfc_thresholds_1 6 7 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8027.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 8 9 pfc_thresholds_1 6 7 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8714.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 9 10 pfc_thresholds_1 6 7 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8283.0 us, fastest flow: 172.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 5 6 pfc_thresholds_1 7 8 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 9694.0 us, fastest flow: 170.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 6 7 pfc_thresholds_1 7 8 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7867.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 7 8 pfc_thresholds_1 7 8 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7969.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 8 9 pfc_thresholds_1 7 8 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8864.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 9 10 pfc_thresholds_1 7 8 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7447.0 us, fastest flow: 172.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 5 6 pfc_thresholds_1 8 9 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 9488.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 6 7 pfc_thresholds_1 8 9 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7965.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 7 8 pfc_thresholds_1 8 9 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7774.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 8 9 pfc_thresholds_1 8 9 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7157.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 9 10 pfc_thresholds_1 8 9 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7832.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 5 6 pfc_thresholds_1 9 10 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8607.0 us, fastest flow: 170.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 6 7 pfc_thresholds_1 9 10 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8013.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 7 8 pfc_thresholds_1 9 10 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7850.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 8 9 pfc_thresholds_1 9 10 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8219.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 9 10 pfc_thresholds_1 9 10 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7559.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 5 6 pfc_thresholds_1 5 6 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 12153.0 us, fastest flow: 243.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 6 7 pfc_thresholds_1 5 6 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 11393.0 us, fastest flow: 230.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 7 8 pfc_thresholds_1 5 6 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 11108.0 us, fastest flow: 229.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 8 9 pfc_thresholds_1 5 6 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 15369.0 us, fastest flow: 229.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 9 10 pfc_thresholds_1 5 6 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 11717.0 us, fastest flow: 228.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 5 6 pfc_thresholds_1 6 7 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 9662.0 us, fastest flow: 172.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 6 7 pfc_thresholds_1 6 7 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 9081.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 7 8 pfc_thresholds_1 6 7 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7807.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 8 9 pfc_thresholds_1 6 7 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8437.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 9 10 pfc_thresholds_1 6 7 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 9241.0 us, fastest flow: 173.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 5 6 pfc_thresholds_1 7 8 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 9054.0 us, fastest flow: 170.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 6 7 pfc_thresholds_1 7 8 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8087.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 7 8 pfc_thresholds_1 7 8 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7424.0 us, fastest flow: 172.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 8 9 pfc_thresholds_1 7 8 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7813.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 9 10 pfc_thresholds_1 7 8 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7796.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 5 6 pfc_thresholds_1 8 9 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 9539.0 us, fastest flow: 170.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 6 7 pfc_thresholds_1 8 9 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7413.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 7 8 pfc_thresholds_1 8 9 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7600.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 8 9 pfc_thresholds_1 8 9 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7649.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 9 10 pfc_thresholds_1 8 9 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7213.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 5 6 pfc_thresholds_1 9 10 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8608.0 us, fastest flow: 170.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 6 7 pfc_thresholds_1 9 10 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7501.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 7 8 pfc_thresholds_1 9 10 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 7677.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 8 9 pfc_thresholds_1 9 10 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8336.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 9 10 pfc_thresholds_1 9 10 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 3841920000.0 and 1920 flows completed . jct is: 8085.0 us, fastest flow: 171.0us

The shortest jct with 0 drops:  7157.0us, with qsize = 100, pcf_thresholds = 8 9,pcf_thresholds_1 = 8 9, spine_pfc_threshold = 8 9, fastest_ar_sticky_delta = 11, seed_hash = 0, seed_path = 0 
The slowest jct :  35945.0us, with 0 drops at qsize = 100, pcf_thresholds = 5 6,pcf_thresholds_1 = 9 10, spine_pfc_threshold = 5 6, slowest_ar_sticky_delta = 8 ,seed_hash = 0, seed_path = 0

------------------------------------ Thw above results are for the following base command ----------------------
./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt
----------------------------------------------------------------------------------------------------------------
