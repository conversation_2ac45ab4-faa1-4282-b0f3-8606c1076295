number of input parameters is 7
protocol is roce, collelctive is allreduce, connection matric is connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm , number of nodes is 128 , numbe of connections is 3968
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 8 9 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 8 9 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 8 9 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 8 9 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 9 10 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 9 10 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 9 10 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 9 10 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 8 9 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 8 9 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 8 9 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 8 9 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 9 10 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 9 10 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 9 10 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 9 10 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 8 9 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 8 9 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 8 9 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 8 9 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 9 10 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 9 10 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 9 10 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 9 10 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 8 9 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 8 9 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 8 9 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 8 9 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 9 10 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 9 10 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 9 10 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 9 10 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 8 9 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 8 9 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 8 9 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 8 9 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 9 10 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 9 10 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 9 10 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 9 10 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 8 9 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 8 9 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 8 9 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 8 9 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 9 10 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 9 10 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 9 10 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 9 10 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_16_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 5 6 pfc_thresholds_1 5 6 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 22051.0 us, fastest flow: 239.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 6 7 pfc_thresholds_1 5 6 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 20546.0 us, fastest flow: 229.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 7 8 pfc_thresholds_1 5 6 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 25168.0 us, fastest flow: 228.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 8 9 pfc_thresholds_1 5 6 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 27603.0 us, fastest flow: 229.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 9 10 pfc_thresholds_1 5 6 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 34070.0 us, fastest flow: 226.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 5 6 pfc_thresholds_1 6 7 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 16218.0 us, fastest flow: 170.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 6 7 pfc_thresholds_1 6 7 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 15156.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 7 8 pfc_thresholds_1 6 7 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 16384.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 8 9 pfc_thresholds_1 6 7 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 15019.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 9 10 pfc_thresholds_1 6 7 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 15051.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 5 6 pfc_thresholds_1 7 8 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 17283.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 6 7 pfc_thresholds_1 7 8 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 15125.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 7 8 pfc_thresholds_1 7 8 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 13748.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 8 9 pfc_thresholds_1 7 8 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 15696.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 9 10 pfc_thresholds_1 7 8 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 15484.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 5 6 pfc_thresholds_1 8 9 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 19480.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 6 7 pfc_thresholds_1 8 9 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 14533.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 7 8 pfc_thresholds_1 8 9 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 15005.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 8 9 pfc_thresholds_1 8 9 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 14068.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 9 10 pfc_thresholds_1 8 9 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 14594.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 5 6 pfc_thresholds_1 9 10 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 16151.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 6 7 pfc_thresholds_1 9 10 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 15202.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 7 8 pfc_thresholds_1 9 10 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 13771.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 8 9 pfc_thresholds_1 9 10 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 13365.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 9 10 pfc_thresholds_1 9 10 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 15551.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 5 6 pfc_thresholds_1 5 6 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 21330.0 us, fastest flow: 240.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 6 7 pfc_thresholds_1 5 6 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 21050.0 us, fastest flow: 228.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 7 8 pfc_thresholds_1 5 6 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 37706.0 us, fastest flow: 228.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 8 9 pfc_thresholds_1 5 6 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 21553.0 us, fastest flow: 229.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 9 10 pfc_thresholds_1 5 6 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 23618.0 us, fastest flow: 228.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 5 6 pfc_thresholds_1 6 7 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 16709.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 6 7 pfc_thresholds_1 6 7 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 15165.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 7 8 pfc_thresholds_1 6 7 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 14932.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 8 9 pfc_thresholds_1 6 7 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 15297.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 9 10 pfc_thresholds_1 6 7 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 15985.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 5 6 pfc_thresholds_1 7 8 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 16580.0 us, fastest flow: 170.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 6 7 pfc_thresholds_1 7 8 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 15444.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 7 8 pfc_thresholds_1 7 8 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 14624.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 8 9 pfc_thresholds_1 7 8 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 15043.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 9 10 pfc_thresholds_1 7 8 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 15047.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 5 6 pfc_thresholds_1 8 9 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 15727.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 6 7 pfc_thresholds_1 8 9 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 14324.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 7 8 pfc_thresholds_1 8 9 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 13873.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 8 9 pfc_thresholds_1 8 9 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 14761.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 9 10 pfc_thresholds_1 8 9 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 16101.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 5 6 pfc_thresholds_1 9 10 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 16601.0 us, fastest flow: 170.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 6 7 pfc_thresholds_1 9 10 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 13456.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 7 8 pfc_thresholds_1 9 10 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 15259.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 8 9 pfc_thresholds_1 9 10 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 14746.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 9 10 pfc_thresholds_1 9 10 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 13545.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 5 6 pfc_thresholds_1 5 6 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 19075.0 us, fastest flow: 236.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 6 7 pfc_thresholds_1 5 6 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 22296.0 us, fastest flow: 228.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 7 8 pfc_thresholds_1 5 6 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 29319.0 us, fastest flow: 228.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 8 9 pfc_thresholds_1 5 6 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 26204.0 us, fastest flow: 229.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 9 10 pfc_thresholds_1 5 6 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 21744.0 us, fastest flow: 228.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 5 6 pfc_thresholds_1 6 7 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 17173.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 6 7 pfc_thresholds_1 6 7 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 14428.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 7 8 pfc_thresholds_1 6 7 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 15226.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 8 9 pfc_thresholds_1 6 7 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 15148.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 9 10 pfc_thresholds_1 6 7 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 16377.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 5 6 pfc_thresholds_1 7 8 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 16505.0 us, fastest flow: 170.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 6 7 pfc_thresholds_1 7 8 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 15082.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 7 8 pfc_thresholds_1 7 8 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 14529.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 8 9 pfc_thresholds_1 7 8 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 15397.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 9 10 pfc_thresholds_1 7 8 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 14009.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 5 6 pfc_thresholds_1 8 9 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 16304.0 us, fastest flow: 170.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 6 7 pfc_thresholds_1 8 9 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 13984.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 7 8 pfc_thresholds_1 8 9 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 14619.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 8 9 pfc_thresholds_1 8 9 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 13965.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 9 10 pfc_thresholds_1 8 9 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 13781.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 5 6 pfc_thresholds_1 9 10 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 15688.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 6 7 pfc_thresholds_1 9 10 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 13721.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 7 8 pfc_thresholds_1 9 10 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 14203.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 8 9 pfc_thresholds_1 9 10 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 13638.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 9 10 pfc_thresholds_1 9 10 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 14028.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 5 6 pfc_thresholds_1 5 6 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 21562.0 us, fastest flow: 240.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 6 7 pfc_thresholds_1 5 6 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 19869.0 us, fastest flow: 228.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 7 8 pfc_thresholds_1 5 6 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 20790.0 us, fastest flow: 228.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 8 9 pfc_thresholds_1 5 6 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 23985.0 us, fastest flow: 229.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 9 10 pfc_thresholds_1 5 6 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 37717.0 us, fastest flow: 228.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 5 6 pfc_thresholds_1 6 7 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 17012.0 us, fastest flow: 170.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 6 7 pfc_thresholds_1 6 7 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 14318.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 7 8 pfc_thresholds_1 6 7 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 15522.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 8 9 pfc_thresholds_1 6 7 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 14666.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 9 10 pfc_thresholds_1 6 7 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 14724.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 5 6 pfc_thresholds_1 7 8 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 16058.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 6 7 pfc_thresholds_1 7 8 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 14642.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 7 8 pfc_thresholds_1 7 8 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 13342.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 8 9 pfc_thresholds_1 7 8 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 13600.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 9 10 pfc_thresholds_1 7 8 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 12976.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 5 6 pfc_thresholds_1 8 9 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 16129.0 us, fastest flow: 170.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 6 7 pfc_thresholds_1 8 9 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 13650.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 7 8 pfc_thresholds_1 8 9 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 13804.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 8 9 pfc_thresholds_1 8 9 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 13493.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 9 10 pfc_thresholds_1 8 9 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 14086.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 5 6 pfc_thresholds_1 9 10 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 15027.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 6 7 pfc_thresholds_1 9 10 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 13491.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 7 8 pfc_thresholds_1 9 10 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 13171.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 8 9 pfc_thresholds_1 9 10 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 14039.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 9 10 pfc_thresholds_1 9 10 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 13140.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 5 6 pfc_thresholds_1 5 6 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 19823.0 us, fastest flow: 240.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 6 7 pfc_thresholds_1 5 6 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 20831.0 us, fastest flow: 228.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 7 8 pfc_thresholds_1 5 6 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 19167.0 us, fastest flow: 230.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 8 9 pfc_thresholds_1 5 6 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 19372.0 us, fastest flow: 230.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 9 10 pfc_thresholds_1 5 6 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 19089.0 us, fastest flow: 228.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 5 6 pfc_thresholds_1 6 7 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 18127.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 6 7 pfc_thresholds_1 6 7 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 16408.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 7 8 pfc_thresholds_1 6 7 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 14153.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 8 9 pfc_thresholds_1 6 7 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 15699.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 9 10 pfc_thresholds_1 6 7 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 14155.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 5 6 pfc_thresholds_1 7 8 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 16147.0 us, fastest flow: 170.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 6 7 pfc_thresholds_1 7 8 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 13913.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 7 8 pfc_thresholds_1 7 8 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 13848.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 8 9 pfc_thresholds_1 7 8 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 14221.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 9 10 pfc_thresholds_1 7 8 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 13677.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 5 6 pfc_thresholds_1 8 9 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 15198.0 us, fastest flow: 170.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 6 7 pfc_thresholds_1 8 9 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 14823.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 7 8 pfc_thresholds_1 8 9 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 13793.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 8 9 pfc_thresholds_1 8 9 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 13143.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 9 10 pfc_thresholds_1 8 9 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 16230.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 5 6 pfc_thresholds_1 9 10 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 16160.0 us, fastest flow: 170.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 6 7 pfc_thresholds_1 9 10 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 13306.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 7 8 pfc_thresholds_1 9 10 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 13289.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 8 9 pfc_thresholds_1 9 10 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 13443.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 9 10 pfc_thresholds_1 9 10 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 12919.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 5 6 pfc_thresholds_1 5 6 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 20647.0 us, fastest flow: 239.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 6 7 pfc_thresholds_1 5 6 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 18500.0 us, fastest flow: 228.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 7 8 pfc_thresholds_1 5 6 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 20505.0 us, fastest flow: 228.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 8 9 pfc_thresholds_1 5 6 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 19029.0 us, fastest flow: 229.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 9 10 pfc_thresholds_1 5 6 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 19495.0 us, fastest flow: 228.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 5 6 pfc_thresholds_1 6 7 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 16178.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 6 7 pfc_thresholds_1 6 7 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 15110.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 7 8 pfc_thresholds_1 6 7 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 14860.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 8 9 pfc_thresholds_1 6 7 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 14319.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 9 10 pfc_thresholds_1 6 7 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 13604.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 5 6 pfc_thresholds_1 7 8 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 15894.0 us, fastest flow: 172.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 6 7 pfc_thresholds_1 7 8 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 14302.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 7 8 pfc_thresholds_1 7 8 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 14223.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 8 9 pfc_thresholds_1 7 8 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 14920.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 9 10 pfc_thresholds_1 7 8 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 13989.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 5 6 pfc_thresholds_1 8 9 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 15361.0 us, fastest flow: 170.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 6 7 pfc_thresholds_1 8 9 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 13351.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 7 8 pfc_thresholds_1 8 9 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 14878.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 8 9 pfc_thresholds_1 8 9 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 13445.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 9 10 pfc_thresholds_1 8 9 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 13725.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 5 6 pfc_thresholds_1 9 10 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 15992.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 6 7 pfc_thresholds_1 9 10 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 14357.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 7 8 pfc_thresholds_1 9 10 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 13220.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 8 9 pfc_thresholds_1 9 10 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 12770.0 us, fastest flow: 171.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 9 10 pfc_thresholds_1 9 10 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 7939968000.0 and 3968 flows completed . jct is: 13068.0 us, fastest flow: 171.0us

The shortest jct with 0 drops:  12770.0us, with qsize = 100, pcf_thresholds = 9 10,pcf_thresholds_1 = 8 9, spine_pfc_threshold = 9 10, fastest_ar_sticky_delta = 12, seed_hash = 0, seed_path = 0 
The slowest jct :  37717.0us, with 0 drops at qsize = 100, pcf_thresholds = 5 6,pcf_thresholds_1 = 9 10, spine_pfc_threshold = 5 6, slowest_ar_sticky_delta = 10 ,seed_hash = 0, seed_path = 0

------------------------------------ Thw above results are for the following base command ----------------------
./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 3968 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt
----------------------------------------------------------------------------------------------------------------
