Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4 -pfc_thresholds_1 3 4 
For q: 100, max_finished_at: 108047.0, min_finished_at: 31349.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 3, spine_pfc_threshold: 3, pfc_threshold_1: 3, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed701.cm -q 110 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4 -pfc_thresholds_1 3 4 
For q: 110, max_finished_at: 113883.0, min_finished_at: 31039.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 3, spine_pfc_threshold: 3, pfc_threshold_1: 3, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 3 4 -spine_pfc_thresholds 4 5 -pfc_thresholds_1 3 4 
For q: 100, max_finished_at: 93471.0, min_finished_at: 31077.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 3, spine_pfc_threshold: 4, pfc_threshold_1: 3, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed701.cm -q 110 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 3 4 -spine_pfc_thresholds 4 5 -pfc_thresholds_1 3 4 
For q: 110, max_finished_at: 110300.0, min_finished_at: 30711.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 3, spine_pfc_threshold: 4, pfc_threshold_1: 3, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 3 4 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 3 4 
For q: 100, max_finished_at: 101563.0, min_finished_at: 31728.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 3, spine_pfc_threshold: 5, pfc_threshold_1: 3, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed701.cm -q 110 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 3 4 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 3 4 
For q: 110, max_finished_at: 101410.0, min_finished_at: 30763.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 3, spine_pfc_threshold: 5, pfc_threshold_1: 3, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 3 4 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 3 4 
For q: 100, max_finished_at: 99618.0, min_finished_at: 31830.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 3, spine_pfc_threshold: 6, pfc_threshold_1: 3, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed701.cm -q 110 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 3 4 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 3 4 
For q: 110, max_finished_at: 94215.0, min_finished_at: 32685.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 3, spine_pfc_threshold: 6, pfc_threshold_1: 3, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 3 4 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 3 4 
For q: 100, max_finished_at: 128248.0, min_finished_at: 33104.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 3, spine_pfc_threshold: 7, pfc_threshold_1: 3, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed701.cm -q 110 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 3 4 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 3 4 
For q: 110, max_finished_at: 102303.0, min_finished_at: 31726.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 3, spine_pfc_threshold: 7, pfc_threshold_1: 3, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 3 4 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 3 4 
For q: 100, max_finished_at: 135863.0, min_finished_at: 32218.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 3, spine_pfc_threshold: 8, pfc_threshold_1: 3, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed701.cm -q 110 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 3 4 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 3 4 
For q: 110, max_finished_at: 102996.0, min_finished_at: 34006.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 3, spine_pfc_threshold: 8, pfc_threshold_1: 3, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 4 5 -spine_pfc_thresholds 3 4 -pfc_thresholds_1 4 5 
For q: 100, max_finished_at: 93572.0, min_finished_at: 29893.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 4, spine_pfc_threshold: 3, pfc_threshold_1: 4, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed701.cm -q 110 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 4 5 -spine_pfc_thresholds 3 4 -pfc_thresholds_1 4 5 
For q: 110, max_finished_at: 117281.0, min_finished_at: 29100.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 4, spine_pfc_threshold: 3, pfc_threshold_1: 4, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5 -pfc_thresholds_1 4 5 
For q: 100, max_finished_at: 109959.0, min_finished_at: 29258.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 4, spine_pfc_threshold: 4, pfc_threshold_1: 4, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed701.cm -q 110 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5 -pfc_thresholds_1 4 5 
For q: 110, max_finished_at: 98682.0, min_finished_at: 29497.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 4, spine_pfc_threshold: 4, pfc_threshold_1: 4, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 4 5 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 4 5 
For q: 100, max_finished_at: 105116.0, min_finished_at: 28996.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 4, spine_pfc_threshold: 5, pfc_threshold_1: 4, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed701.cm -q 110 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 4 5 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 4 5 
For q: 110, max_finished_at: 101835.0, min_finished_at: 30315.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 4, spine_pfc_threshold: 5, pfc_threshold_1: 4, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 4 5 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 4 5 
For q: 100, max_finished_at: 88562.0, min_finished_at: 31359.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 4, spine_pfc_threshold: 6, pfc_threshold_1: 4, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed701.cm -q 110 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 4 5 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 4 5 
For q: 110, max_finished_at: 116862.0, min_finished_at: 29937.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 4, spine_pfc_threshold: 6, pfc_threshold_1: 4, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 4 5 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 4 5 
For q: 100, max_finished_at: 89065.0, min_finished_at: 31705.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 4, spine_pfc_threshold: 7, pfc_threshold_1: 4, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed701.cm -q 110 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 4 5 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 4 5 
For q: 110, max_finished_at: 114358.0, min_finished_at: 30411.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 4, spine_pfc_threshold: 7, pfc_threshold_1: 4, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 4 5 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 4 5 
For q: 100, max_finished_at: 128456.0, min_finished_at: 31836.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 4, spine_pfc_threshold: 8, pfc_threshold_1: 4, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed701.cm -q 110 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 4 5 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 4 5 
For q: 110, max_finished_at: 95865.0, min_finished_at: 31812.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 4, spine_pfc_threshold: 8, pfc_threshold_1: 4, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 4 5 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 4 5 
For q: 100, max_finished_at: 138498.0, min_finished_at: 29815.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 4, spine_pfc_threshold: 9, pfc_threshold_1: 4, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed701.cm -q 110 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 4 5 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 4 5 
For q: 110, max_finished_at: 109310.0, min_finished_at: 29904.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 4, spine_pfc_threshold: 9, pfc_threshold_1: 4, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 3 4 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 130898.0, min_finished_at: 30270.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 3, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed701.cm -q 110 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 3 4 -pfc_thresholds_1 5 6 
For q: 110, max_finished_at: 102113.0, min_finished_at: 30201.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 3, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 4 5 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 97745.0, min_finished_at: 28437.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 4, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed701.cm -q 110 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 4 5 -pfc_thresholds_1 5 6 
For q: 110, max_finished_at: 86839.0, min_finished_at: 28894.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 4, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
