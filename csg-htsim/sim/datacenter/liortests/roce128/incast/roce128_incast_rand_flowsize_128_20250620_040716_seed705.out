Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 30, max_finished_at: 107098.0, min_finished_at: 37427.0, dropped_count: 33563, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 40, max_finished_at: 104356.0, min_finished_at: 36898.0, dropped_count: 403, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 50, max_finished_at: 81518.0, min_finished_at: 37583.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 60, max_finished_at: 77726.0, min_finished_at: 37607.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 30, max_finished_at: 103313.0, min_finished_at: 34889.0, dropped_count: 45168, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 40, max_finished_at: 106703.0, min_finished_at: 35746.0, dropped_count: 577, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 50, max_finished_at: 86762.0, min_finished_at: 36339.0, dropped_count: 6, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 60, max_finished_at: 90405.0, min_finished_at: 37259.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
For q: 30, max_finished_at: 119834.0, min_finished_at: 35192.0, dropped_count: 91552, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
For q: 40, max_finished_at: 121042.0, min_finished_at: 34453.0, dropped_count: 851, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
For q: 50, max_finished_at: 94836.0, min_finished_at: 36315.0, dropped_count: 3, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
For q: 60, max_finished_at: 89934.0, min_finished_at: 36240.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
For q: 40, max_finished_at: 118597.0, min_finished_at: 34062.0, dropped_count: 1858, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
For q: 50, max_finished_at: 101653.0, min_finished_at: 35108.0, dropped_count: 9, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
For q: 60, max_finished_at: 101114.0, min_finished_at: 35281.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
For q: 30, max_finished_at: 125291.0, min_finished_at: 34512.0, dropped_count: 208551, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
For q: 40, max_finished_at: 123066.0, min_finished_at: 35428.0, dropped_count: 9710, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
For q: 50, max_finished_at: 106653.0, min_finished_at: 34868.0, dropped_count: 28, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
For q: 60, max_finished_at: 103754.0, min_finished_at: 36058.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
For q: 30, max_finished_at: 102707.0, min_finished_at: 36426.0, dropped_count: 80662, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
For q: 40, max_finished_at: 101164.0, min_finished_at: 37128.0, dropped_count: 1584, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
For q: 50, max_finished_at: 78791.0, min_finished_at: 37472.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
For q: 60, max_finished_at: 77509.0, min_finished_at: 37642.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 30, max_finished_at: 117151.0, min_finished_at: 35805.0, dropped_count: 93106, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 40, max_finished_at: 106335.0, min_finished_at: 34696.0, dropped_count: 2567, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 50, max_finished_at: 84868.0, min_finished_at: 36529.0, dropped_count: 8, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 60, max_finished_at: 84655.0, min_finished_at: 37023.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
For q: 30, max_finished_at: 121855.0, min_finished_at: 34979.0, dropped_count: 167655, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
For q: 40, max_finished_at: 122963.0, min_finished_at: 34547.0, dropped_count: 3768, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
For q: 50, max_finished_at: 94792.0, min_finished_at: 36475.0, dropped_count: 23, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
For q: 60, max_finished_at: 94074.0, min_finished_at: 36868.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
For q: 30, max_finished_at: 126999.0, min_finished_at: 35300.0, dropped_count: 215729, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
For q: 40, max_finished_at: 123174.0, min_finished_at: 34269.0, dropped_count: 5170, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
For q: 50, max_finished_at: 103732.0, min_finished_at: 35332.0, dropped_count: 9, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
For q: 60, max_finished_at: 105118.0, min_finished_at: 34356.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
For q: 30, max_finished_at: 124920.0, min_finished_at: 34712.0, dropped_count: 311036, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
For q: 40, max_finished_at: 125708.0, min_finished_at: 34767.0, dropped_count: 17624, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
For q: 50, max_finished_at: 108797.0, min_finished_at: 35812.0, dropped_count: 45, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
For q: 60, max_finished_at: 108942.0, min_finished_at: 34526.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
For q: 30, max_finished_at: 128020.0, min_finished_at: 35702.0, dropped_count: 264170, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
For q: 50, max_finished_at: 91314.0, min_finished_at: 36740.0, dropped_count: 163, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
For q: 60, max_finished_at: 85667.0, min_finished_at: 37158.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
For q: 30, max_finished_at: 147417.0, min_finished_at: 35280.0, dropped_count: 371504, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
For q: 40, max_finished_at: 143238.0, min_finished_at: 34907.0, dropped_count: 19729, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
For q: 50, max_finished_at: 97458.0, min_finished_at: 35678.0, dropped_count: 92, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
For q: 60, max_finished_at: 97089.0, min_finished_at: 35996.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
For q: 30, max_finished_at: 156016.0, min_finished_at: 34537.0, dropped_count: 467977, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
For q: 40, max_finished_at: 161325.0, min_finished_at: 34356.0, dropped_count: 24924, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
For q: 50, max_finished_at: 98803.0, min_finished_at: 35791.0, dropped_count: 72, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
For q: 60, max_finished_at: 99124.0, min_finished_at: 36055.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
For q: 30, max_finished_at: 163922.0, min_finished_at: 33389.0, dropped_count: 535049, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
For q: 40, max_finished_at: 165869.0, min_finished_at: 33103.0, dropped_count: 29989, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
For q: 50, max_finished_at: 113263.0, min_finished_at: 34803.0, dropped_count: 406, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
For q: 60, max_finished_at: 109989.0, min_finished_at: 34906.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
For q: 30, max_finished_at: 172206.0, min_finished_at: 33839.0, dropped_count: 690080, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
For q: 40, max_finished_at: 163743.0, min_finished_at: 33934.0, dropped_count: 49546, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
For q: 50, max_finished_at: 117397.0, min_finished_at: 35496.0, dropped_count: 278, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
For q: 60, max_finished_at: 116326.0, min_finished_at: 35496.0, dropped_count: 2, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
For q: 30, max_finished_at: 172809.0, min_finished_at: 34886.0, dropped_count: 1007372, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 8, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
For q: 50, max_finished_at: 112552.0, min_finished_at: 35275.0, dropped_count: 755, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 8, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
For q: 60, max_finished_at: 112069.0, min_finished_at: 36811.0, dropped_count: 23, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 8, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
For q: 40, max_finished_at: 176024.0, min_finished_at: 33956.0, dropped_count: 131192, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 8, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
For q: 30, max_finished_at: 188627.0, min_finished_at: 33692.0, dropped_count: 1190943, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 8, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
For q: 40, max_finished_at: 183659.0, min_finished_at: 33343.0, dropped_count: 141013, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 8, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
For q: 50, max_finished_at: 146270.0, min_finished_at: 34249.0, dropped_count: 3219, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 8, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
For q: 60, max_finished_at: 142769.0, min_finished_at: 34372.0, dropped_count: 26, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 8, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
For q: 30, max_finished_at: 195347.0, min_finished_at: 33277.0, dropped_count: 1285042, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 8, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
For q: 40, max_finished_at: 192426.0, min_finished_at: 33320.0, dropped_count: 157150, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 8, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
For q: 50, max_finished_at: 156236.0, min_finished_at: 33811.0, dropped_count: 3920, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 8, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
For q: 30, max_finished_at: 193732.0, min_finished_at: 32894.0, dropped_count: 1442348, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 8, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
For q: 60, max_finished_at: 166947.0, min_finished_at: 34607.0, dropped_count: 77, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 8, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
For q: 30, max_finished_at: 174324.0, min_finished_at: 35397.0, dropped_count: 1613986, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 9, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
For q: 40, max_finished_at: 172986.0, min_finished_at: 35262.0, dropped_count: 306738, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 9, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
For q: 50, max_finished_at: 164884.0, min_finished_at: 35478.0, dropped_count: 22125, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 9, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
For q: 60, max_finished_at: 161722.0, min_finished_at: 35461.0, dropped_count: 570, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 9, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
For q: 30, max_finished_at: 179674.0, min_finished_at: 33921.0, dropped_count: 1716776, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 9, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
For q: 50, max_finished_at: 172944.0, min_finished_at: 33264.0, dropped_count: 28094, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 9, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
For q: 60, max_finished_at: 169721.0, min_finished_at: 33716.0, dropped_count: 750, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 9, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
For q: 50, max_finished_at: 179931.0, min_finished_at: 34392.0, dropped_count: 27670, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 9, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
For q: 60, max_finished_at: 174611.0, min_finished_at: 33565.0, dropped_count: 1031, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 9, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
For q: 50, max_finished_at: 187314.0, min_finished_at: 33101.0, dropped_count: 31626, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 9, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
For q: 60, max_finished_at: 188226.0, min_finished_at: 33802.0, dropped_count: 1220, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 9, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
For q: 30, max_finished_at: 192533.0, min_finished_at: 33811.0, dropped_count: 2190345, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 9, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 30, max_finished_at: 107564.0, min_finished_at: 35897.0, dropped_count: 68375, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 40, max_finished_at: 106430.0, min_finished_at: 35503.0, dropped_count: 1133, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 50, max_finished_at: 84777.0, min_finished_at: 37576.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 60, max_finished_at: 78970.0, min_finished_at: 37157.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 30, max_finished_at: 112948.0, min_finished_at: 34743.0, dropped_count: 88595, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 40, max_finished_at: 113302.0, min_finished_at: 34390.0, dropped_count: 1436, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 50, max_finished_at: 93994.0, min_finished_at: 35673.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 60, max_finished_at: 88037.0, min_finished_at: 35346.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
For q: 30, max_finished_at: 131848.0, min_finished_at: 34125.0, dropped_count: 177845, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
For q: 50, max_finished_at: 95237.0, min_finished_at: 35238.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
For q: 60, max_finished_at: 96542.0, min_finished_at: 34993.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
For q: 30, max_finished_at: 135838.0, min_finished_at: 33483.0, dropped_count: 243397, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
For q: 40, max_finished_at: 133517.0, min_finished_at: 33743.0, dropped_count: 4433, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
For q: 50, max_finished_at: 111768.0, min_finished_at: 34568.0, dropped_count: 18, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
For q: 60, max_finished_at: 106394.0, min_finished_at: 35095.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
For q: 30, max_finished_at: 129254.0, min_finished_at: 33955.0, dropped_count: 338454, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
For q: 40, max_finished_at: 132967.0, min_finished_at: 33460.0, dropped_count: 18666, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
For q: 50, max_finished_at: 114459.0, min_finished_at: 34611.0, dropped_count: 62, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
For q: 60, max_finished_at: 110711.0, min_finished_at: 34730.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
For q: 30, max_finished_at: 109237.0, min_finished_at: 36050.0, dropped_count: 155286, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
For q: 40, max_finished_at: 106928.0, min_finished_at: 35870.0, dropped_count: 4628, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
For q: 50, max_finished_at: 88586.0, min_finished_at: 35577.0, dropped_count: 13, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
For q: 60, max_finished_at: 89326.0, min_finished_at: 37658.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 30, max_finished_at: 114064.0, min_finished_at: 35134.0, dropped_count: 182116, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 40, max_finished_at: 111658.0, min_finished_at: 34534.0, dropped_count: 5306, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 50, max_finished_at: 96566.0, min_finished_at: 35993.0, dropped_count: 63, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 60, max_finished_at: 92483.0, min_finished_at: 35601.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
For q: 30, max_finished_at: 121337.0, min_finished_at: 35109.0, dropped_count: 237044, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
For q: 40, max_finished_at: 121597.0, min_finished_at: 34739.0, dropped_count: 6067, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
For q: 50, max_finished_at: 100818.0, min_finished_at: 35622.0, dropped_count: 46, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
For q: 60, max_finished_at: 98290.0, min_finished_at: 35503.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
For q: 30, max_finished_at: 136927.0, min_finished_at: 34417.0, dropped_count: 401909, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
For q: 40, max_finished_at: 136859.0, min_finished_at: 34014.0, dropped_count: 12962, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
For q: 50, max_finished_at: 107874.0, min_finished_at: 34536.0, dropped_count: 160, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
For q: 60, max_finished_at: 103897.0, min_finished_at: 34959.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
For q: 30, max_finished_at: 133448.0, min_finished_at: 33637.0, dropped_count: 523284, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
For q: 40, max_finished_at: 136682.0, min_finished_at: 34234.0, dropped_count: 34542, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
For q: 50, max_finished_at: 117756.0, min_finished_at: 35121.0, dropped_count: 418, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
For q: 60, max_finished_at: 118432.0, min_finished_at: 34958.0, dropped_count: 13, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
For q: 30, max_finished_at: 116853.0, min_finished_at: 35309.0, dropped_count: 287273, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
For q: 40, max_finished_at: 116898.0, min_finished_at: 35653.0, dropped_count: 16138, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
For q: 50, max_finished_at: 92682.0, min_finished_at: 36804.0, dropped_count: 101, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
For q: 60, max_finished_at: 91938.0, min_finished_at: 36260.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
For q: 30, max_finished_at: 123635.0, min_finished_at: 35000.0, dropped_count: 365023, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
For q: 40, max_finished_at: 127486.0, min_finished_at: 34783.0, dropped_count: 19538, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
For q: 50, max_finished_at: 104330.0, min_finished_at: 36000.0, dropped_count: 224, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
For q: 60, max_finished_at: 101034.0, min_finished_at: 35424.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
For q: 40, max_finished_at: 129998.0, min_finished_at: 34839.0, dropped_count: 20635, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
For q: 50, max_finished_at: 110393.0, min_finished_at: 34842.0, dropped_count: 208, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
For q: 60, max_finished_at: 107503.0, min_finished_at: 35264.0, dropped_count: 4, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
For q: 30, max_finished_at: 150253.0, min_finished_at: 33767.0, dropped_count: 692127, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
For q: 40, max_finished_at: 148431.0, min_finished_at: 34323.0, dropped_count: 40371, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
For q: 50, max_finished_at: 116617.0, min_finished_at: 35149.0, dropped_count: 453, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
For q: 60, max_finished_at: 111759.0, min_finished_at: 34745.0, dropped_count: 3, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
For q: 30, max_finished_at: 152047.0, min_finished_at: 33806.0, dropped_count: 846415, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
For q: 40, max_finished_at: 150622.0, min_finished_at: 34058.0, dropped_count: 65832, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
For q: 50, max_finished_at: 129834.0, min_finished_at: 33568.0, dropped_count: 1240, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
For q: 60, max_finished_at: 129068.0, min_finished_at: 33804.0, dropped_count: 2, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
For q: 30, max_finished_at: 189390.0, min_finished_at: 34774.0, dropped_count: 1238118, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
For q: 40, max_finished_at: 186923.0, min_finished_at: 35311.0, dropped_count: 160519, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
For q: 50, max_finished_at: 104631.0, min_finished_at: 35717.0, dropped_count: 849, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
For q: 60, max_finished_at: 103681.0, min_finished_at: 35295.0, dropped_count: 26, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
For q: 30, max_finished_at: 187468.0, min_finished_at: 33571.0, dropped_count: 1407400, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
For q: 40, max_finished_at: 186224.0, min_finished_at: 33901.0, dropped_count: 165934, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
For q: 50, max_finished_at: 116006.0, min_finished_at: 35304.0, dropped_count: 2070, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
For q: 60, max_finished_at: 112538.0, min_finished_at: 36232.0, dropped_count: 77, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
For q: 30, max_finished_at: 190172.0, min_finished_at: 33648.0, dropped_count: 1489145, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
For q: 40, max_finished_at: 191020.0, min_finished_at: 33361.0, dropped_count: 180355, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
For q: 50, max_finished_at: 120408.0, min_finished_at: 34469.0, dropped_count: 2392, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
For q: 60, max_finished_at: 118557.0, min_finished_at: 34715.0, dropped_count: 86, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
For q: 40, max_finished_at: 202519.0, min_finished_at: 33547.0, dropped_count: 209961, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
For q: 50, max_finished_at: 125045.0, min_finished_at: 34773.0, dropped_count: 4446, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
For q: 60, max_finished_at: 125499.0, min_finished_at: 33999.0, dropped_count: 88, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
For q: 30, max_finished_at: 203538.0, min_finished_at: 33794.0, dropped_count: 1953596, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
For q: 50, max_finished_at: 148907.0, min_finished_at: 34227.0, dropped_count: 5071, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
For q: 60, max_finished_at: 141413.0, min_finished_at: 34526.0, dropped_count: 144, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
For q: 30, max_finished_at: 194938.0, min_finished_at: 35003.0, dropped_count: 1952604, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
For q: 40, max_finished_at: 185419.0, min_finished_at: 35430.0, dropped_count: 363800, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
For q: 50, max_finished_at: 179851.0, min_finished_at: 34694.0, dropped_count: 29071, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
For q: 60, max_finished_at: 175462.0, min_finished_at: 34592.0, dropped_count: 722, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
For q: 30, max_finished_at: 195808.0, min_finished_at: 33184.0, dropped_count: 2133576, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
For q: 40, max_finished_at: 191517.0, min_finished_at: 34271.0, dropped_count: 398012, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
For q: 50, max_finished_at: 181871.0, min_finished_at: 33808.0, dropped_count: 35570, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
For q: 60, max_finished_at: 177431.0, min_finished_at: 33668.0, dropped_count: 1080, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
For q: 30, max_finished_at: 192986.0, min_finished_at: 33053.0, dropped_count: 2269694, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
For q: 40, max_finished_at: 192637.0, min_finished_at: 33185.0, dropped_count: 418787, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
For q: 50, max_finished_at: 183962.0, min_finished_at: 33710.0, dropped_count: 38128, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
For q: 60, max_finished_at: 187199.0, min_finished_at: 33667.0, dropped_count: 1404, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
For q: 30, max_finished_at: 204231.0, min_finished_at: 32792.0, dropped_count: 2570140, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
For q: 40, max_finished_at: 204285.0, min_finished_at: 33293.0, dropped_count: 468605, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
For q: 30, max_finished_at: 206434.0, min_finished_at: 33094.0, dropped_count: 2829422, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
For q: 40, max_finished_at: 207970.0, min_finished_at: 33527.0, dropped_count: 542994, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
For q: 50, max_finished_at: 201503.0, min_finished_at: 33404.0, dropped_count: 47247, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
For q: 60, max_finished_at: 200064.0, min_finished_at: 34156.0, dropped_count: 1697, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 30, max_finished_at: 149176.0, min_finished_at: 35379.0, dropped_count: 227443, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 40, max_finished_at: 144411.0, min_finished_at: 35162.0, dropped_count: 4139, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 50, max_finished_at: 92482.0, min_finished_at: 35752.0, dropped_count: 4, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 60, max_finished_at: 93065.0, min_finished_at: 36037.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 30, max_finished_at: 157134.0, min_finished_at: 34173.0, dropped_count: 285120, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 40, max_finished_at: 158046.0, min_finished_at: 33859.0, dropped_count: 4510, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 50, max_finished_at: 100834.0, min_finished_at: 35043.0, dropped_count: 6, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 60, max_finished_at: 99256.0, min_finished_at: 34591.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
For q: 30, max_finished_at: 181063.0, min_finished_at: 33897.0, dropped_count: 495003, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
For q: 40, max_finished_at: 180044.0, min_finished_at: 33234.0, dropped_count: 8734, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
For q: 50, max_finished_at: 107650.0, min_finished_at: 33696.0, dropped_count: 5, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
For q: 60, max_finished_at: 107646.0, min_finished_at: 34380.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
For q: 30, max_finished_at: 184190.0, min_finished_at: 33068.0, dropped_count: 618627, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
For q: 40, max_finished_at: 181596.0, min_finished_at: 32959.0, dropped_count: 17127, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
For q: 50, max_finished_at: 123851.0, min_finished_at: 34057.0, dropped_count: 28, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
For q: 60, max_finished_at: 121565.0, min_finished_at: 33608.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
For q: 30, max_finished_at: 183616.0, min_finished_at: 33312.0, dropped_count: 781732, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
For q: 40, max_finished_at: 178745.0, min_finished_at: 33494.0, dropped_count: 52815, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
For q: 50, max_finished_at: 126510.0, min_finished_at: 34404.0, dropped_count: 272, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
For q: 60, max_finished_at: 128500.0, min_finished_at: 34638.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
For q: 30, max_finished_at: 118531.0, min_finished_at: 35787.0, dropped_count: 279571, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
For q: 50, max_finished_at: 94281.0, min_finished_at: 35863.0, dropped_count: 13, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
For q: 60, max_finished_at: 93460.0, min_finished_at: 36158.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 30, max_finished_at: 129676.0, min_finished_at: 34752.0, dropped_count: 361183, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 40, max_finished_at: 127204.0, min_finished_at: 34968.0, dropped_count: 9940, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 50, max_finished_at: 109103.0, min_finished_at: 35601.0, dropped_count: 94, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 60, max_finished_at: 103842.0, min_finished_at: 36754.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
For q: 30, max_finished_at: 138368.0, min_finished_at: 33885.0, dropped_count: 458312, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
For q: 40, max_finished_at: 135853.0, min_finished_at: 34420.0, dropped_count: 12624, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
For q: 50, max_finished_at: 112135.0, min_finished_at: 34478.0, dropped_count: 143, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
For q: 60, max_finished_at: 107059.0, min_finished_at: 35106.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
For q: 30, max_finished_at: 153557.0, min_finished_at: 33635.0, dropped_count: 715832, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
For q: 40, max_finished_at: 149535.0, min_finished_at: 33963.0, dropped_count: 24823, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
For q: 50, max_finished_at: 117794.0, min_finished_at: 34856.0, dropped_count: 182, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
For q: 60, max_finished_at: 112278.0, min_finished_at: 34524.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
For q: 30, max_finished_at: 157670.0, min_finished_at: 33672.0, dropped_count: 856656, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
For q: 40, max_finished_at: 152449.0, min_finished_at: 33897.0, dropped_count: 59642, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
For q: 50, max_finished_at: 131406.0, min_finished_at: 34141.0, dropped_count: 633, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
For q: 60, max_finished_at: 129134.0, min_finished_at: 34095.0, dropped_count: 1, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
For q: 30, max_finished_at: 130877.0, min_finished_at: 35272.0, dropped_count: 475919, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
For q: 40, max_finished_at: 133710.0, min_finished_at: 35301.0, dropped_count: 25745, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
For q: 50, max_finished_at: 103170.0, min_finished_at: 36020.0, dropped_count: 309, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
For q: 60, max_finished_at: 98061.0, min_finished_at: 36061.0, dropped_count: 27, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
For q: 30, max_finished_at: 135966.0, min_finished_at: 34288.0, dropped_count: 633768, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
For q: 40, max_finished_at: 135331.0, min_finished_at: 33649.0, dropped_count: 32468, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
For q: 50, max_finished_at: 112036.0, min_finished_at: 35072.0, dropped_count: 620, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
For q: 60, max_finished_at: 110423.0, min_finished_at: 36142.0, dropped_count: 24, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
For q: 30, max_finished_at: 141057.0, min_finished_at: 33725.0, dropped_count: 739860, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
For q: 40, max_finished_at: 139889.0, min_finished_at: 34355.0, dropped_count: 38070, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
For q: 50, max_finished_at: 117686.0, min_finished_at: 35077.0, dropped_count: 895, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
For q: 60, max_finished_at: 118196.0, min_finished_at: 34382.0, dropped_count: 7, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
For q: 30, max_finished_at: 167780.0, min_finished_at: 33407.0, dropped_count: 1112904, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
For q: 40, max_finished_at: 156135.0, min_finished_at: 33527.0, dropped_count: 65455, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
For q: 50, max_finished_at: 124006.0, min_finished_at: 33986.0, dropped_count: 1344, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
For q: 60, max_finished_at: 122233.0, min_finished_at: 34410.0, dropped_count: 8, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
For q: 30, max_finished_at: 162456.0, min_finished_at: 33153.0, dropped_count: 1309055, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
For q: 40, max_finished_at: 163049.0, min_finished_at: 33665.0, dropped_count: 119352, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
For q: 50, max_finished_at: 138670.0, min_finished_at: 33693.0, dropped_count: 1700, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
For q: 60, max_finished_at: 136454.0, min_finished_at: 34256.0, dropped_count: 6, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
For q: 40, max_finished_at: 198595.0, min_finished_at: 35127.0, dropped_count: 201748, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 8, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
For q: 50, max_finished_at: 111853.0, min_finished_at: 35636.0, dropped_count: 2493, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 8, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
For q: 60, max_finished_at: 110962.0, min_finished_at: 35294.0, dropped_count: 40, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 8, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
For q: 30, max_finished_at: 194694.0, min_finished_at: 33451.0, dropped_count: 1821698, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 8, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
For q: 40, max_finished_at: 191617.0, min_finished_at: 34081.0, dropped_count: 215982, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 8, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
For q: 50, max_finished_at: 124752.0, min_finished_at: 34608.0, dropped_count: 4043, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 8, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
For q: 60, max_finished_at: 118511.0, min_finished_at: 34610.0, dropped_count: 81, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 8, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
For q: 30, max_finished_at: 195240.0, min_finished_at: 32953.0, dropped_count: 1943286, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 8, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
For q: 40, max_finished_at: 192089.0, min_finished_at: 32962.0, dropped_count: 227906, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 8, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
For q: 50, max_finished_at: 130908.0, min_finished_at: 33576.0, dropped_count: 5930, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 8, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
For q: 60, max_finished_at: 129382.0, min_finished_at: 34846.0, dropped_count: 259, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 8, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
For q: 30, max_finished_at: 211428.0, min_finished_at: 32748.0, dropped_count: 2295392, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 8, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
For q: 40, max_finished_at: 204841.0, min_finished_at: 33059.0, dropped_count: 269596, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 8, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
For q: 50, max_finished_at: 136585.0, min_finished_at: 33517.0, dropped_count: 5869, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 8, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
For q: 60, max_finished_at: 137254.0, min_finished_at: 34512.0, dropped_count: 172, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 8, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
For q: 40, max_finished_at: 203599.0, min_finished_at: 33221.0, dropped_count: 323402, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 8, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
For q: 50, max_finished_at: 157334.0, min_finished_at: 34112.0, dropped_count: 8435, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 8, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
For q: 60, max_finished_at: 152474.0, min_finished_at: 33907.0, dropped_count: 202, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 8, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
For q: 30, max_finished_at: 204389.0, min_finished_at: 35144.0, dropped_count: 2382682, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 9, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
For q: 40, max_finished_at: 204181.0, min_finished_at: 34907.0, dropped_count: 452464, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 9, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
For q: 50, max_finished_at: 194907.0, min_finished_at: 35101.0, dropped_count: 41695, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 9, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
For q: 30, max_finished_at: 197147.0, min_finished_at: 34253.0, dropped_count: 2620031, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 9, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
For q: 60, max_finished_at: 186642.0, min_finished_at: 33481.0, dropped_count: 1762, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 9, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
For q: 30, max_finished_at: 198451.0, min_finished_at: 33629.0, dropped_count: 2821659, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 9, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
For q: 40, max_finished_at: 199875.0, min_finished_at: 33736.0, dropped_count: 497373, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 9, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
For q: 60, max_finished_at: 190857.0, min_finished_at: 32969.0, dropped_count: 2200, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 9, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
For q: 30, max_finished_at: 209309.0, min_finished_at: 33030.0, dropped_count: 3158223, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 9, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
For q: 40, max_finished_at: 209336.0, min_finished_at: 33123.0, dropped_count: 549317, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 9, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
For q: 50, max_finished_at: 194946.0, min_finished_at: 33079.0, dropped_count: 56053, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 9, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
For q: 60, max_finished_at: 195877.0, min_finished_at: 33148.0, dropped_count: 1620, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 9, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
For q: 30, max_finished_at: 211002.0, min_finished_at: 33754.0, dropped_count: 3467612, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 9, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
For q: 40, max_finished_at: 209530.0, min_finished_at: 32863.0, dropped_count: 634180, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 9, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
For q: 60, max_finished_at: 202108.0, min_finished_at: 33087.0, dropped_count: 3258, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 9, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 8 9 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 30, max_finished_at: 150008.0, min_finished_at: 35199.0, dropped_count: 426462, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 8, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 8 9 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 40, max_finished_at: 150324.0, min_finished_at: 35414.0, dropped_count: 13613, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 8, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 8 9 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 50, max_finished_at: 131623.0, min_finished_at: 35154.0, dropped_count: 166, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 8, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 8 9 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 60, max_finished_at: 126612.0, min_finished_at: 35334.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 8, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 8 9 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 30, max_finished_at: 160585.0, min_finished_at: 33900.0, dropped_count: 529458, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 8, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 8 9 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 40, max_finished_at: 159826.0, min_finished_at: 33532.0, dropped_count: 16065, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 8, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 8 9 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 50, max_finished_at: 145088.0, min_finished_at: 34244.0, dropped_count: 226, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 8, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
