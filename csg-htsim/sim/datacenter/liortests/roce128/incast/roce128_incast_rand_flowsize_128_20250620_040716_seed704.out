Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 30, max_finished_at: 150217.0, min_finished_at: 55324.0, dropped_count: 151353, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 50, max_finished_at: 120773.0, min_finished_at: 52450.0, dropped_count: 86, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 30, max_finished_at: 155590.0, min_finished_at: 56068.0, dropped_count: 171427, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 40, max_finished_at: 146718.0, min_finished_at: 54785.0, dropped_count: 6076, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 50, max_finished_at: 120557.0, min_finished_at: 52555.0, dropped_count: 76, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 60, max_finished_at: 127581.0, min_finished_at: 50653.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
For q: 30, max_finished_at: 207254.0, min_finished_at: 58795.0, dropped_count: 253521, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
For q: 40, max_finished_at: 205182.0, min_finished_at: 59031.0, dropped_count: 9807, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
For q: 60, max_finished_at: 126340.0, min_finished_at: 54669.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
For q: 40, max_finished_at: 210600.0, min_finished_at: 56422.0, dropped_count: 11290, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
For q: 50, max_finished_at: 193158.0, min_finished_at: 53987.0, dropped_count: 233, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
For q: 60, max_finished_at: 190614.0, min_finished_at: 52656.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
For q: 40, max_finished_at: 212994.0, min_finished_at: 58645.0, dropped_count: 13283, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
For q: 50, max_finished_at: 194437.0, min_finished_at: 52425.0, dropped_count: 221, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
For q: 40, max_finished_at: 138550.0, min_finished_at: 53454.0, dropped_count: 9123, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
For q: 50, max_finished_at: 114997.0, min_finished_at: 52728.0, dropped_count: 129, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
For q: 60, max_finished_at: 115541.0, min_finished_at: 48632.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 40, max_finished_at: 145521.0, min_finished_at: 54906.0, dropped_count: 11085, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
For q: 30, max_finished_at: 207856.0, min_finished_at: 59734.0, dropped_count: 392425, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
For q: 60, max_finished_at: 135818.0, min_finished_at: 53392.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
For q: 40, max_finished_at: 217054.0, min_finished_at: 58245.0, dropped_count: 19306, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
For q: 50, max_finished_at: 202097.0, min_finished_at: 53772.0, dropped_count: 433, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 6, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
For q: 40, max_finished_at: 167707.0, min_finished_at: 55412.0, dropped_count: 29442, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
For q: 60, max_finished_at: 114075.0, min_finished_at: 51424.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
For q: 50, max_finished_at: 134443.0, min_finished_at: 52972.0, dropped_count: 255, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
For q: 60, max_finished_at: 127895.0, min_finished_at: 50889.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
For q: 50, max_finished_at: 137743.0, min_finished_at: 52036.0, dropped_count: 275, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
For q: 60, max_finished_at: 131155.0, min_finished_at: 54505.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
For q: 50, max_finished_at: 201132.0, min_finished_at: 56945.0, dropped_count: 390, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
For q: 60, max_finished_at: 200362.0, min_finished_at: 55279.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
For q: 40, max_finished_at: 244419.0, min_finished_at: 58115.0, dropped_count: 61680, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
For q: 50, max_finished_at: 213929.0, min_finished_at: 54215.0, dropped_count: 706, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
For q: 60, max_finished_at: 209507.0, min_finished_at: 57562.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 7, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
For q: 30, max_finished_at: 216517.0, min_finished_at: 54899.0, dropped_count: 1647894, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 8, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
For q: 60, max_finished_at: 147517.0, min_finished_at: 52294.0, dropped_count: 2, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 8, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
For q: 50, max_finished_at: 191600.0, min_finished_at: 54648.0, dropped_count: 2524, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 8, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
For q: 60, max_finished_at: 204494.0, min_finished_at: 57496.0, dropped_count: 25, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 8, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
For q: 50, max_finished_at: 240967.0, min_finished_at: 55282.0, dropped_count: 4878, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 8, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
For q: 60, max_finished_at: 238808.0, min_finished_at: 57946.0, dropped_count: 27, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 8, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
For q: 40, max_finished_at: 260295.0, min_finished_at: 61888.0, dropped_count: 573687, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 9, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 40, max_finished_at: 147850.0, min_finished_at: 60562.0, dropped_count: 10039, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 50, max_finished_at: 116106.0, min_finished_at: 55128.0, dropped_count: 96, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 60, max_finished_at: 114455.0, min_finished_at: 53206.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 30, max_finished_at: 150043.0, min_finished_at: 61197.0, dropped_count: 263871, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 40, max_finished_at: 149154.0, min_finished_at: 59930.0, dropped_count: 11479, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 50, max_finished_at: 118495.0, min_finished_at: 55381.0, dropped_count: 123, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 60, max_finished_at: 112777.0, min_finished_at: 54644.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
For q: 30, max_finished_at: 198569.0, min_finished_at: 63155.0, dropped_count: 372559, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
For q: 40, max_finished_at: 196329.0, min_finished_at: 63850.0, dropped_count: 19612, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
For q: 50, max_finished_at: 130943.0, min_finished_at: 58727.0, dropped_count: 145, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
For q: 60, max_finished_at: 125754.0, min_finished_at: 55667.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
For q: 30, max_finished_at: 204500.0, min_finished_at: 61849.0, dropped_count: 413220, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
For q: 40, max_finished_at: 201688.0, min_finished_at: 63114.0, dropped_count: 21490, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
For q: 50, max_finished_at: 181741.0, min_finished_at: 59059.0, dropped_count: 596, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
For q: 60, max_finished_at: 180252.0, min_finished_at: 58583.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
For q: 30, max_finished_at: 205413.0, min_finished_at: 62216.0, dropped_count: 469567, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
For q: 40, max_finished_at: 201091.0, min_finished_at: 61339.0, dropped_count: 23649, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
For q: 50, max_finished_at: 187213.0, min_finished_at: 59501.0, dropped_count: 261, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
For q: 60, max_finished_at: 184733.0, min_finished_at: 58259.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 5, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
For q: 30, max_finished_at: 158531.0, min_finished_at: 56896.0, dropped_count: 376760, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
For q: 50, max_finished_at: 119765.0, min_finished_at: 53741.0, dropped_count: 586, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
For q: 60, max_finished_at: 118186.0, min_finished_at: 52454.0, dropped_count: 1, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 30, max_finished_at: 147299.0, min_finished_at: 57024.0, dropped_count: 445102, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 40, max_finished_at: 143708.0, min_finished_at: 60067.0, dropped_count: 27642, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 50, max_finished_at: 119127.0, min_finished_at: 54187.0, dropped_count: 382, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 60, max_finished_at: 119883.0, min_finished_at: 53715.0, dropped_count: 11, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
For q: 30, max_finished_at: 152327.0, min_finished_at: 58284.0, dropped_count: 493317, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
For q: 40, max_finished_at: 163893.0, min_finished_at: 58514.0, dropped_count: 28601, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
For q: 50, max_finished_at: 134348.0, min_finished_at: 54346.0, dropped_count: 921, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
For q: 60, max_finished_at: 127863.0, min_finished_at: 54093.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
For q: 40, max_finished_at: 208817.0, min_finished_at: 60221.0, dropped_count: 45985, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
For q: 50, max_finished_at: 150385.0, min_finished_at: 55222.0, dropped_count: 979, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
For q: 60, max_finished_at: 138720.0, min_finished_at: 56750.0, dropped_count: 5, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
For q: 30, max_finished_at: 210108.0, min_finished_at: 62273.0, dropped_count: 759781, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
For q: 40, max_finished_at: 208354.0, min_finished_at: 60015.0, dropped_count: 52324, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
For q: 60, max_finished_at: 193748.0, min_finished_at: 57138.0, dropped_count: 10, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
For q: 50, max_finished_at: 131997.0, min_finished_at: 52619.0, dropped_count: 359, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
For q: 60, max_finished_at: 132147.0, min_finished_at: 54527.0, dropped_count: 1, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
For q: 30, max_finished_at: 161649.0, min_finished_at: 57593.0, dropped_count: 760532, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
For q: 40, max_finished_at: 156965.0, min_finished_at: 59209.0, dropped_count: 52371, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
For q: 50, max_finished_at: 130971.0, min_finished_at: 57182.0, dropped_count: 871, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
For q: 60, max_finished_at: 130394.0, min_finished_at: 54835.0, dropped_count: 10, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
For q: 40, max_finished_at: 174684.0, min_finished_at: 61535.0, dropped_count: 56641, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
For q: 30, max_finished_at: 217771.0, min_finished_at: 64687.0, dropped_count: 1068322, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
For q: 40, max_finished_at: 219453.0, min_finished_at: 61830.0, dropped_count: 82597, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
For q: 50, max_finished_at: 160554.0, min_finished_at: 58099.0, dropped_count: 879, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
For q: 60, max_finished_at: 161401.0, min_finished_at: 56318.0, dropped_count: 14, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
For q: 50, max_finished_at: 205931.0, min_finished_at: 59707.0, dropped_count: 2141, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
For q: 60, max_finished_at: 208581.0, min_finished_at: 58669.0, dropped_count: 7, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 7, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
For q: 50, max_finished_at: 153735.0, min_finished_at: 57369.0, dropped_count: 3127, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
For q: 60, max_finished_at: 151065.0, min_finished_at: 55024.0, dropped_count: 35, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
For q: 60, max_finished_at: 146769.0, min_finished_at: 53166.0, dropped_count: 42, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
For q: 30, max_finished_at: 238433.0, min_finished_at: 63676.0, dropped_count: 2184291, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
For q: 50, max_finished_at: 165447.0, min_finished_at: 58731.0, dropped_count: 4780, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
For q: 60, max_finished_at: 163469.0, min_finished_at: 56441.0, dropped_count: 173, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
For q: 30, max_finished_at: 252628.0, min_finished_at: 64464.0, dropped_count: 2351342, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
For q: 40, max_finished_at: 252281.0, min_finished_at: 65447.0, dropped_count: 297431, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
For q: 50, max_finished_at: 177569.0, min_finished_at: 58069.0, dropped_count: 4691, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
For q: 60, max_finished_at: 177549.0, min_finished_at: 58072.0, dropped_count: 80, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
For q: 30, max_finished_at: 254672.0, min_finished_at: 66519.0, dropped_count: 2479901, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
For q: 40, max_finished_at: 254589.0, min_finished_at: 68161.0, dropped_count: 320698, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
For q: 50, max_finished_at: 218172.0, min_finished_at: 61114.0, dropped_count: 7981, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
For q: 60, max_finished_at: 217573.0, min_finished_at: 60140.0, dropped_count: 39, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 8, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
For q: 40, max_finished_at: 233347.0, min_finished_at: 61176.0, dropped_count: 618364, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
For q: 40, max_finished_at: 230092.0, min_finished_at: 63429.0, dropped_count: 599811, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
For q: 50, max_finished_at: 213317.0, min_finished_at: 59645.0, dropped_count: 48313, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
For q: 40, max_finished_at: 243437.0, min_finished_at: 64954.0, dropped_count: 601347, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
For q: 60, max_finished_at: 227038.0, min_finished_at: 59435.0, dropped_count: 951, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
For q: 50, max_finished_at: 238991.0, min_finished_at: 61686.0, dropped_count: 51687, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
For q: 40, max_finished_at: 259328.0, min_finished_at: 67848.0, dropped_count: 687966, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 6, spine_pfc_threshold: 9, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 30, max_finished_at: 204618.0, min_finished_at: 75388.0, dropped_count: 556614, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 40, max_finished_at: 200787.0, min_finished_at: 71864.0, dropped_count: 26292, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 50, max_finished_at: 134685.0, min_finished_at: 58784.0, dropped_count: 274, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 60, max_finished_at: 127796.0, min_finished_at: 59320.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 30, max_finished_at: 183342.0, min_finished_at: 76324.0, dropped_count: 626580, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 40, max_finished_at: 178995.0, min_finished_at: 72619.0, dropped_count: 32987, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 50, max_finished_at: 122203.0, min_finished_at: 62873.0, dropped_count: 267, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
For q: 60, max_finished_at: 121104.0, min_finished_at: 60383.0, dropped_count: 1, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
For q: 30, max_finished_at: 213682.0, min_finished_at: 77930.0, dropped_count: 851816, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
For q: 40, max_finished_at: 211440.0, min_finished_at: 71133.0, dropped_count: 54241, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
For q: 50, max_finished_at: 133701.0, min_finished_at: 66441.0, dropped_count: 515, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
For q: 60, max_finished_at: 127928.0, min_finished_at: 63502.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
For q: 30, max_finished_at: 219826.0, min_finished_at: 72994.0, dropped_count: 894107, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
For q: 40, max_finished_at: 217271.0, min_finished_at: 72839.0, dropped_count: 60054, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
For q: 50, max_finished_at: 184565.0, min_finished_at: 65478.0, dropped_count: 1222, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
For q: 60, max_finished_at: 179582.0, min_finished_at: 61865.0, dropped_count: 10, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
For q: 30, max_finished_at: 220861.0, min_finished_at: 75757.0, dropped_count: 1054192, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
For q: 40, max_finished_at: 218502.0, min_finished_at: 72944.0, dropped_count: 70528, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
For q: 50, max_finished_at: 191048.0, min_finished_at: 62476.0, dropped_count: 1877, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
For q: 60, max_finished_at: 187468.0, min_finished_at: 64403.0, dropped_count: 8, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 5, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
For q: 30, max_finished_at: 178368.0, min_finished_at: 64252.0, dropped_count: 615320, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
For q: 40, max_finished_at: 178793.0, min_finished_at: 65753.0, dropped_count: 35215, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
For q: 50, max_finished_at: 138999.0, min_finished_at: 58188.0, dropped_count: 908, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 30, max_finished_at: 155872.0, min_finished_at: 68146.0, dropped_count: 708525, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 40, max_finished_at: 152400.0, min_finished_at: 67719.0, dropped_count: 50222, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 50, max_finished_at: 129237.0, min_finished_at: 61216.0, dropped_count: 1193, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 60, max_finished_at: 127584.0, min_finished_at: 60347.0, dropped_count: 14, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
For q: 30, max_finished_at: 167971.0, min_finished_at: 69063.0, dropped_count: 811517, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
For q: 40, max_finished_at: 164096.0, min_finished_at: 69405.0, dropped_count: 62259, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
For q: 50, max_finished_at: 135895.0, min_finished_at: 61857.0, dropped_count: 1493, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
For q: 60, max_finished_at: 133196.0, min_finished_at: 63206.0, dropped_count: 23, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
For q: 30, max_finished_at: 208888.0, min_finished_at: 71498.0, dropped_count: 1068512, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
For q: 40, max_finished_at: 209108.0, min_finished_at: 69580.0, dropped_count: 81445, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
For q: 60, max_finished_at: 141433.0, min_finished_at: 65327.0, dropped_count: 47, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
For q: 30, max_finished_at: 210617.0, min_finished_at: 70045.0, dropped_count: 1153241, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
For q: 50, max_finished_at: 195968.0, min_finished_at: 65890.0, dropped_count: 3904, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
For q: 60, max_finished_at: 195054.0, min_finished_at: 63506.0, dropped_count: 45, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 6, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
For q: 30, max_finished_at: 189762.0, min_finished_at: 61230.0, dropped_count: 974316, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
For q: 40, max_finished_at: 186751.0, min_finished_at: 63047.0, dropped_count: 77741, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
For q: 50, max_finished_at: 155056.0, min_finished_at: 60562.0, dropped_count: 2271, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
For q: 30, max_finished_at: 165301.0, min_finished_at: 65580.0, dropped_count: 1141686, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
For q: 40, max_finished_at: 161889.0, min_finished_at: 63726.0, dropped_count: 95576, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
For q: 50, max_finished_at: 138527.0, min_finished_at: 60581.0, dropped_count: 3109, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
For q: 60, max_finished_at: 133265.0, min_finished_at: 58304.0, dropped_count: 85, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 6, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
For q: 30, max_finished_at: 177831.0, min_finished_at: 68859.0, dropped_count: 1268476, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
For q: 40, max_finished_at: 175237.0, min_finished_at: 67406.0, dropped_count: 115206, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
For q: 50, max_finished_at: 145423.0, min_finished_at: 62513.0, dropped_count: 4507, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
For q: 60, max_finished_at: 143605.0, min_finished_at: 58293.0, dropped_count: 161, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 7, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
For q: 30, max_finished_at: 217800.0, min_finished_at: 72264.0, dropped_count: 1572804, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
For q: 40, max_finished_at: 215568.0, min_finished_at: 69706.0, dropped_count: 152971, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
For q: 50, max_finished_at: 160821.0, min_finished_at: 63928.0, dropped_count: 5127, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
For q: 60, max_finished_at: 149389.0, min_finished_at: 58539.0, dropped_count: 110, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 8, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
For q: 40, max_finished_at: 216353.0, min_finished_at: 67882.0, dropped_count: 173316, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
For q: 50, max_finished_at: 206259.0, min_finished_at: 63739.0, dropped_count: 8281, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 9, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
For q: 40, max_finished_at: 242689.0, min_finished_at: 63820.0, dropped_count: 358956, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 8, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
For q: 50, max_finished_at: 170399.0, min_finished_at: 60789.0, dropped_count: 5936, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 8, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
For q: 60, max_finished_at: 169773.0, min_finished_at: 58103.0, dropped_count: 113, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 7, spine_pfc_threshold: 8, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
