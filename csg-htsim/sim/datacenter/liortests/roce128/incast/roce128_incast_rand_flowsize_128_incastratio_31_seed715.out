number of input parameters is 7
protocol is roce, collelctive is incast, connection matric is connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed715.cm , number of nodes is 128 , numbe of connections is 124
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed715.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed715.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed715.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed715.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed715.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed715.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed715.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed715.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed715.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed715.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed715.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed715.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed715.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed715.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed715.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed715.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed715.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed715.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed715.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed715.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed715.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed715.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed715.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed715.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed715.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed715.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed715.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed715.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed715.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed715.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 5 6 pfc_thresholds_1 5 6 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 524129.0 us, fastest flow: 81606.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 6 7 pfc_thresholds_1 6 7 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 542658.0 us, fastest flow: 81538.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 7 8 pfc_thresholds_1 7 8 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 710420.0 us, fastest flow: 79205.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 8 9 pfc_thresholds_1 8 9 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 2351 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 872153.0 us, fastest flow: 71818.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 9 10 pfc_thresholds_1 9 10 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 244573 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 830114.0 us, fastest flow: 79494.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 5 6 pfc_thresholds_1 5 6 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 552814.0 us, fastest flow: 81423.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 6 7 pfc_thresholds_1 6 7 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 546251.0 us, fastest flow: 81544.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 7 8 pfc_thresholds_1 7 8 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 707942.0 us, fastest flow: 77176.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 8 9 pfc_thresholds_1 8 9 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 2094 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 904722.0 us, fastest flow: 73140.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 9 10 pfc_thresholds_1 9 10 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 268878 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 791008.0 us, fastest flow: 71855.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 5 6 pfc_thresholds_1 5 6 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 531293.0 us, fastest flow: 81586.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 6 7 pfc_thresholds_1 6 7 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 533127.0 us, fastest flow: 81519.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 7 8 pfc_thresholds_1 7 8 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 705764.0 us, fastest flow: 78298.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 8 9 pfc_thresholds_1 8 9 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 2420 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 885341.0 us, fastest flow: 74910.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 9 10 pfc_thresholds_1 9 10 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 270930 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 794427.0 us, fastest flow: 71242.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 5 6 pfc_thresholds_1 5 6 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 522089.0 us, fastest flow: 81583.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 6 7 pfc_thresholds_1 6 7 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 555438.0 us, fastest flow: 81434.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 7 8 pfc_thresholds_1 7 8 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 726774.0 us, fastest flow: 80034.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 8 9 pfc_thresholds_1 8 9 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 1301 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 876384.0 us, fastest flow: 78125.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 9 10 pfc_thresholds_1 9 10 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 232803 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 822153.0 us, fastest flow: 77689.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 5 6 pfc_thresholds_1 5 6 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 552315.0 us, fastest flow: 81662.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 6 7 pfc_thresholds_1 6 7 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 536838.0 us, fastest flow: 81425.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 7 8 pfc_thresholds_1 7 8 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 716480.0 us, fastest flow: 78881.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 8 9 pfc_thresholds_1 8 9 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 1901 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 892504.0 us, fastest flow: 76552.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 9 10 pfc_thresholds_1 9 10 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 214756 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 812712.0 us, fastest flow: 75238.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 5 6 pfc_thresholds_1 5 6 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 523632.0 us, fastest flow: 81419.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 6 7 pfc_thresholds_1 6 7 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 555933.0 us, fastest flow: 81572.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 7 8 pfc_thresholds_1 7 8 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 706461.0 us, fastest flow: 80365.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 8 9 pfc_thresholds_1 8 9 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 1660 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 850841.0 us, fastest flow: 76063.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 9 10 pfc_thresholds_1 9 10 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 243322 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 746037.0 us, fastest flow: 77781.0us

The shortest jct with 0 drops:  522089.0us, with qsize = 100, pcf_thresholds = 5 6,pcf_thresholds_1 = 5 6, spine_pfc_threshold = 5 6, fastest_ar_sticky_delta = 10, seed_hash = 0, seed_path = 0 
The slowest jct :  904722.0us, with 243322 drops at qsize = 100, pcf_thresholds = 8 9,pcf_thresholds_1 = 8 9, spine_pfc_threshold = 8 9, slowest_ar_sticky_delta = 8 ,seed_hash = 0, seed_path = 0

------------------------------------ Thw above results are for the following base command ----------------------
./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt
----------------------------------------------------------------------------------------------------------------
