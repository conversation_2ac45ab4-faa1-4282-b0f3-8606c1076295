Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed702.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4 -pfc_thresholds_1 3 4 
For q: 100, max_finished_at: 140780.0, min_finished_at: 30705.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 3, spine_pfc_threshold: 3, pfc_threshold_1: 3, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed702.cm -q 110 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4 -pfc_thresholds_1 3 4 
For q: 110, max_finished_at: 128023.0, min_finished_at: 30467.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 3, spine_pfc_threshold: 3, pfc_threshold_1: 3, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed702.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 3 4 -spine_pfc_thresholds 4 5 -pfc_thresholds_1 3 4 
For q: 100, max_finished_at: 122691.0, min_finished_at: 30901.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 3, spine_pfc_threshold: 4, pfc_threshold_1: 3, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed702.cm -q 110 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 3 4 -spine_pfc_thresholds 4 5 -pfc_thresholds_1 3 4 
For q: 110, max_finished_at: 131143.0, min_finished_at: 30774.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 3, spine_pfc_threshold: 4, pfc_threshold_1: 3, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed702.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 3 4 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 3 4 
For q: 100, max_finished_at: 135724.0, min_finished_at: 30895.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 3, spine_pfc_threshold: 5, pfc_threshold_1: 3, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed702.cm -q 110 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 3 4 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 3 4 
For q: 110, max_finished_at: 121687.0, min_finished_at: 30674.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 3, spine_pfc_threshold: 5, pfc_threshold_1: 3, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed702.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 3 4 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 3 4 
For q: 100, max_finished_at: 125305.0, min_finished_at: 30895.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 3, spine_pfc_threshold: 6, pfc_threshold_1: 3, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed702.cm -q 110 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 3 4 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 3 4 
For q: 110, max_finished_at: 122019.0, min_finished_at: 30685.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 3, spine_pfc_threshold: 6, pfc_threshold_1: 3, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed702.cm -q 110 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 3 4 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 3 4 
For q: 110, max_finished_at: 127252.0, min_finished_at: 30497.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 3, spine_pfc_threshold: 7, pfc_threshold_1: 3, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed702.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 3 4 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 3 4 
For q: 100, max_finished_at: 140300.0, min_finished_at: 31573.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 3, spine_pfc_threshold: 8, pfc_threshold_1: 3, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed702.cm -q 110 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 3 4 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 3 4 
For q: 110, max_finished_at: 119945.0, min_finished_at: 31667.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 3, spine_pfc_threshold: 8, pfc_threshold_1: 3, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed702.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 3 4 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 3 4 
For q: 100, max_finished_at: 180383.0, min_finished_at: 31066.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 3, spine_pfc_threshold: 9, pfc_threshold_1: 3, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed702.cm -q 110 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 3 4 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 3 4 
For q: 110, max_finished_at: 152285.0, min_finished_at: 31813.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 3, spine_pfc_threshold: 9, pfc_threshold_1: 3, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed702.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 4 5 -spine_pfc_thresholds 3 4 -pfc_thresholds_1 4 5 
For q: 100, max_finished_at: 158512.0, min_finished_at: 31126.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 4, spine_pfc_threshold: 3, pfc_threshold_1: 4, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed702.cm -q 110 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 4 5 -spine_pfc_thresholds 3 4 -pfc_thresholds_1 4 5 
For q: 110, max_finished_at: 117105.0, min_finished_at: 29548.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 4, spine_pfc_threshold: 3, pfc_threshold_1: 4, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed702.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5 -pfc_thresholds_1 4 5 
For q: 100, max_finished_at: 114114.0, min_finished_at: 27943.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 4, spine_pfc_threshold: 4, pfc_threshold_1: 4, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed702.cm -q 110 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5 -pfc_thresholds_1 4 5 
For q: 110, max_finished_at: 109436.0, min_finished_at: 30832.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 4, spine_pfc_threshold: 4, pfc_threshold_1: 4, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed702.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 4 5 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 4 5 
For q: 100, max_finished_at: 113061.0, min_finished_at: 28292.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 4, spine_pfc_threshold: 5, pfc_threshold_1: 4, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed702.cm -q 110 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 4 5 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 4 5 
For q: 110, max_finished_at: 134625.0, min_finished_at: 29192.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 4, spine_pfc_threshold: 5, pfc_threshold_1: 4, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed702.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 4 5 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 4 5 
For q: 100, max_finished_at: 110919.0, min_finished_at: 30800.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 4, spine_pfc_threshold: 6, pfc_threshold_1: 4, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed702.cm -q 110 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 4 5 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 4 5 
For q: 110, max_finished_at: 132414.0, min_finished_at: 26960.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 4, spine_pfc_threshold: 6, pfc_threshold_1: 4, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed702.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 4 5 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 4 5 
For q: 100, max_finished_at: 114734.0, min_finished_at: 32020.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 4, spine_pfc_threshold: 7, pfc_threshold_1: 4, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed702.cm -q 110 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 4 5 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 4 5 
For q: 110, max_finished_at: 112590.0, min_finished_at: 29561.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 4, spine_pfc_threshold: 7, pfc_threshold_1: 4, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed702.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 4 5 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 4 5 
For q: 100, max_finished_at: 123287.0, min_finished_at: 29963.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 4, spine_pfc_threshold: 8, pfc_threshold_1: 4, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed702.cm -q 110 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 4 5 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 4 5 
For q: 110, max_finished_at: 114421.0, min_finished_at: 29654.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 4, spine_pfc_threshold: 8, pfc_threshold_1: 4, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed702.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 4 5 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 4 5 
For q: 100, max_finished_at: 153799.0, min_finished_at: 28474.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 4, spine_pfc_threshold: 9, pfc_threshold_1: 4, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed702.cm -q 110 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 4 5 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 4 5 
For q: 110, max_finished_at: 124157.0, min_finished_at: 31186.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 4, spine_pfc_threshold: 9, pfc_threshold_1: 4, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed702.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 3 4 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 112326.0, min_finished_at: 30773.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 3, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed702.cm -q 110 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 3 4 -pfc_thresholds_1 5 6 
For q: 110, max_finished_at: 98304.0, min_finished_at: 30883.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 3, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed702.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 4 5 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 123822.0, min_finished_at: 29336.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 4, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed702.cm -q 110 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 4 5 -pfc_thresholds_1 5 6 
For q: 110, max_finished_at: 102512.0, min_finished_at: 29498.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 4, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed702.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 83642.0, min_finished_at: 27910.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
