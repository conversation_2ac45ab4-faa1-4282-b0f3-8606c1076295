number of input parameters is 7
protocol is roce, collelctive is incast, connection matric is connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed701.cm , number of nodes is 128 , numbe of connections is 124
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed701.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 5 6 pfc_thresholds_1 5 6 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 529912.0 us, fastest flow: 195606.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 6 7 pfc_thresholds_1 6 7 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 549195.0 us, fastest flow: 119720.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 7 8 pfc_thresholds_1 7 8 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 709558.0 us, fastest flow: 231607.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 8 9 pfc_thresholds_1 8 9 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 3253 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 923132.0 us, fastest flow: 252557.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 9 10 pfc_thresholds_1 9 10 ar_sticky_delta = 7  seed_hash = 0  seed_path = 0 there are 408358 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 863284.0 us, fastest flow: 242794.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 5 6 pfc_thresholds_1 5 6 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 522586.0 us, fastest flow: 121682.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 6 7 pfc_thresholds_1 6 7 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 524337.0 us, fastest flow: 268781.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 7 8 pfc_thresholds_1 7 8 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 709807.0 us, fastest flow: 199142.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 8 9 pfc_thresholds_1 8 9 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 4991 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 907533.0 us, fastest flow: 229244.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 9 10 pfc_thresholds_1 9 10 ar_sticky_delta = 8  seed_hash = 0  seed_path = 0 there are 360298 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 905258.0 us, fastest flow: 248786.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 5 6 pfc_thresholds_1 5 6 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 543777.0 us, fastest flow: 260829.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 6 7 pfc_thresholds_1 6 7 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 548841.0 us, fastest flow: 267274.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 7 8 pfc_thresholds_1 7 8 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 710875.0 us, fastest flow: 199140.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 8 9 pfc_thresholds_1 8 9 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 2242 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 895688.0 us, fastest flow: 251969.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 9 10 pfc_thresholds_1 9 10 ar_sticky_delta = 9  seed_hash = 0  seed_path = 0 there are 418083 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 808991.0 us, fastest flow: 229655.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 5 6 pfc_thresholds_1 5 6 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 529094.0 us, fastest flow: 196404.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 6 7 pfc_thresholds_1 6 7 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 548316.0 us, fastest flow: 138068.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 7 8 pfc_thresholds_1 7 8 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 705434.0 us, fastest flow: 244129.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 8 9 pfc_thresholds_1 8 9 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 1551 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 915854.0 us, fastest flow: 237122.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 9 10 pfc_thresholds_1 9 10 ar_sticky_delta = 10  seed_hash = 0  seed_path = 0 there are 334480 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 825891.0 us, fastest flow: 245341.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 5 6 pfc_thresholds_1 5 6 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 538947.0 us, fastest flow: 156402.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 6 7 pfc_thresholds_1 6 7 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 541895.0 us, fastest flow: 192906.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 7 8 pfc_thresholds_1 7 8 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 729208.0 us, fastest flow: 262988.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 8 9 pfc_thresholds_1 8 9 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 1271 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 895528.0 us, fastest flow: 236711.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 9 10 pfc_thresholds_1 9 10 ar_sticky_delta = 11  seed_hash = 0  seed_path = 0 there are 223743 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 930740.0 us, fastest flow: 258244.0us
For q = 100 and pfc_thresholds 5 6 spine_pfc_thresholds 5 6 pfc_thresholds_1 5 6 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 527924.0 us, fastest flow: 196171.0us
For q = 100 and pfc_thresholds 6 7 spine_pfc_thresholds 6 7 pfc_thresholds_1 6 7 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 550565.0 us, fastest flow: 176837.0us
For q = 100 and pfc_thresholds 7 8 spine_pfc_thresholds 7 8 pfc_thresholds_1 7 8 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 0 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 704506.0 us, fastest flow: 236118.0us
For q = 100 and pfc_thresholds 8 9 spine_pfc_thresholds 8 9 pfc_thresholds_1 8 9 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 2232 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 895532.0 us, fastest flow: 266593.0us
For q = 100 and pfc_thresholds 9 10 spine_pfc_thresholds 9 10 pfc_thresholds_1 9 10 ar_sticky_delta = 12  seed_hash = 0  seed_path = 0 there are 285953 drops, Total simualtion bytes 24800124000.0 and 124 flows completed . jct is: 795898.0 us, fastest flow: 253740.0us

The shortest jct with 0 drops:  522586.0us, with qsize = 100, pcf_thresholds = 5 6,pcf_thresholds_1 = 5 6, spine_pfc_threshold = 5 6, fastest_ar_sticky_delta = 8, seed_hash = 0, seed_path = 0 
The slowest jct :  930740.0us, with 285953 drops at qsize = 100, pcf_thresholds = 9 10,pcf_thresholds_1 = 9 10, spine_pfc_threshold = 9 10, slowest_ar_sticky_delta = 11 ,seed_hash = 0, seed_path = 0

------------------------------------ Thw above results are for the following base command ----------------------
./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt
----------------------------------------------------------------------------------------------------------------
