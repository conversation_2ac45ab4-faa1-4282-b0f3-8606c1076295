Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4 -pfc_thresholds_1 3 4 
For q: 100, max_finished_at: 154790.0, min_finished_at: 29863.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 3, spine_pfc_threshold: 3, pfc_threshold_1: 3, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed700.cm -q 110 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4 -pfc_thresholds_1 3 4 
For q: 110, max_finished_at: 139224.0, min_finished_at: 32041.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 3, spine_pfc_threshold: 3, pfc_threshold_1: 3, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 3 4 -spine_pfc_thresholds 4 5 -pfc_thresholds_1 3 4 
For q: 100, max_finished_at: 138959.0, min_finished_at: 30043.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 3, spine_pfc_threshold: 4, pfc_threshold_1: 3, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed700.cm -q 110 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 3 4 -spine_pfc_thresholds 4 5 -pfc_thresholds_1 3 4 
For q: 110, max_finished_at: 129641.0, min_finished_at: 31190.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 3, spine_pfc_threshold: 4, pfc_threshold_1: 3, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 3 4 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 3 4 
For q: 100, max_finished_at: 126763.0, min_finished_at: 30151.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 3, spine_pfc_threshold: 5, pfc_threshold_1: 3, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed700.cm -q 110 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 3 4 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 3 4 
For q: 110, max_finished_at: 132482.0, min_finished_at: 30532.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 3, spine_pfc_threshold: 5, pfc_threshold_1: 3, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed700.cm -q 110 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 3 4 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 3 4 
For q: 110, max_finished_at: 167204.0, min_finished_at: 29894.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 3, spine_pfc_threshold: 6, pfc_threshold_1: 3, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 3 4 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 3 4 
For q: 100, max_finished_at: 140362.0, min_finished_at: 33916.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 3, spine_pfc_threshold: 7, pfc_threshold_1: 3, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed700.cm -q 110 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 3 4 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 3 4 
For q: 110, max_finished_at: 100675.0, min_finished_at: 32829.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 3, spine_pfc_threshold: 7, pfc_threshold_1: 3, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 3 4 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 3 4 
For q: 100, max_finished_at: 133561.0, min_finished_at: 32067.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 3, spine_pfc_threshold: 8, pfc_threshold_1: 3, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed700.cm -q 110 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 3 4 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 3 4 
For q: 110, max_finished_at: 166811.0, min_finished_at: 32147.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 3, spine_pfc_threshold: 8, pfc_threshold_1: 3, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed700.cm -q 110 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 3 4 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 3 4 
For q: 110, max_finished_at: 178652.0, min_finished_at: 32429.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 3, spine_pfc_threshold: 9, pfc_threshold_1: 3, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 4 5 -spine_pfc_thresholds 3 4 -pfc_thresholds_1 4 5 
For q: 100, max_finished_at: 119125.0, min_finished_at: 31270.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 4, spine_pfc_threshold: 3, pfc_threshold_1: 4, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed700.cm -q 110 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 4 5 -spine_pfc_thresholds 3 4 -pfc_thresholds_1 4 5 
For q: 110, max_finished_at: 143852.0, min_finished_at: 30733.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 4, spine_pfc_threshold: 3, pfc_threshold_1: 4, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5 -pfc_thresholds_1 4 5 
For q: 100, max_finished_at: 119040.0, min_finished_at: 30302.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 4, spine_pfc_threshold: 4, pfc_threshold_1: 4, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed700.cm -q 110 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5 -pfc_thresholds_1 4 5 
For q: 110, max_finished_at: 122886.0, min_finished_at: 28160.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 4, spine_pfc_threshold: 4, pfc_threshold_1: 4, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 4 5 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 4 5 
For q: 100, max_finished_at: 133836.0, min_finished_at: 29440.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 4, spine_pfc_threshold: 5, pfc_threshold_1: 4, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed700.cm -q 110 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 4 5 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 4 5 
For q: 110, max_finished_at: 145283.0, min_finished_at: 31431.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 4, spine_pfc_threshold: 5, pfc_threshold_1: 4, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 4 5 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 4 5 
For q: 100, max_finished_at: 135898.0, min_finished_at: 28494.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 4, spine_pfc_threshold: 6, pfc_threshold_1: 4, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed700.cm -q 110 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 4 5 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 4 5 
For q: 110, max_finished_at: 133050.0, min_finished_at: 28058.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 4, spine_pfc_threshold: 6, pfc_threshold_1: 4, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 4 5 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 4 5 
For q: 100, max_finished_at: 138300.0, min_finished_at: 31573.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 4, spine_pfc_threshold: 7, pfc_threshold_1: 4, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed700.cm -q 110 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 4 5 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 4 5 
For q: 110, max_finished_at: 115760.0, min_finished_at: 32225.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 4, spine_pfc_threshold: 7, pfc_threshold_1: 4, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 4 5 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 4 5 
For q: 100, max_finished_at: 167896.0, min_finished_at: 32196.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 4, spine_pfc_threshold: 8, pfc_threshold_1: 4, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed700.cm -q 110 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 4 5 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 4 5 
For q: 110, max_finished_at: 110661.0, min_finished_at: 31938.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 4, spine_pfc_threshold: 8, pfc_threshold_1: 4, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 4 5 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 4 5 
For q: 100, max_finished_at: 173641.0, min_finished_at: 29426.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 4, spine_pfc_threshold: 9, pfc_threshold_1: 4, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed700.cm -q 110 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 4 5 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 4 5 
For q: 110, max_finished_at: 143712.0, min_finished_at: 32251.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 4, spine_pfc_threshold: 9, pfc_threshold_1: 4, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 3 4 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 124570.0, min_finished_at: 30564.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 3, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed700.cm -q 110 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 3 4 -pfc_thresholds_1 5 6 
For q: 110, max_finished_at: 117915.0, min_finished_at: 29952.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 3, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed700.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 4 5 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 72417.0, min_finished_at: 28394.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 5, pfc_threshold: 5, spine_pfc_threshold: 4, pfc_threshold_1: 5, totalbytes: 19200096000.0, seed_hash: 0, seed_path: 0
