protocol is roce, collelctive is incast, connection matric is connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm , number of nodes is 128 , numbe of connections is 96
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 5 6 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 6 7 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 6 7 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 8 9 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 9 10 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
roce full command is : ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -seed_path 0 -seed 0 -ar_sticky_delta 5 -pfc_thresholds 7 8 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 5 6 
