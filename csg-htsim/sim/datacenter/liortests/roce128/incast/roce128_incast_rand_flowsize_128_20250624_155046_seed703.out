Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed703.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 537481.0, min_finished_at: 81659.0, dropped_count: 0, finished_count: 124, ar_sticky_delta: 7, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 24800124000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed703.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 541974.0, min_finished_at: 80315.0, dropped_count: 0, finished_count: 124, ar_sticky_delta: 7, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 24800124000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed703.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 731642.0, min_finished_at: 79908.0, dropped_count: 0, finished_count: 124, ar_sticky_delta: 7, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 7, totalbytes: 24800124000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed703.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 881141.0, min_finished_at: 67246.0, dropped_count: 2188, finished_count: 124, ar_sticky_delta: 7, pfc_threshold: 8, spine_pfc_threshold: 8, pfc_threshold_1: 8, totalbytes: 24800124000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed703.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 7 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 820227.0, min_finished_at: 72016.0, dropped_count: 291887, finished_count: 124, ar_sticky_delta: 7, pfc_threshold: 9, spine_pfc_threshold: 9, pfc_threshold_1: 9, totalbytes: 24800124000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed703.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 524054.0, min_finished_at: 81581.0, dropped_count: 0, finished_count: 124, ar_sticky_delta: 8, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 24800124000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed703.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 536415.0, min_finished_at: 81531.0, dropped_count: 0, finished_count: 124, ar_sticky_delta: 8, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 24800124000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed703.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 727304.0, min_finished_at: 79964.0, dropped_count: 0, finished_count: 124, ar_sticky_delta: 8, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 7, totalbytes: 24800124000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed703.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 890302.0, min_finished_at: 71874.0, dropped_count: 1707, finished_count: 124, ar_sticky_delta: 8, pfc_threshold: 8, spine_pfc_threshold: 8, pfc_threshold_1: 8, totalbytes: 24800124000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed703.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 8 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 813668.0, min_finished_at: 66541.0, dropped_count: 257295, finished_count: 124, ar_sticky_delta: 8, pfc_threshold: 9, spine_pfc_threshold: 9, pfc_threshold_1: 9, totalbytes: 24800124000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed703.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 520050.0, min_finished_at: 81611.0, dropped_count: 0, finished_count: 124, ar_sticky_delta: 9, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 24800124000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed703.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 562814.0, min_finished_at: 81539.0, dropped_count: 0, finished_count: 124, ar_sticky_delta: 9, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 24800124000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed703.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 709780.0, min_finished_at: 79696.0, dropped_count: 0, finished_count: 124, ar_sticky_delta: 9, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 7, totalbytes: 24800124000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed703.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 892433.0, min_finished_at: 71555.0, dropped_count: 1833, finished_count: 124, ar_sticky_delta: 9, pfc_threshold: 8, spine_pfc_threshold: 8, pfc_threshold_1: 8, totalbytes: 24800124000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed703.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 9 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 810257.0, min_finished_at: 77365.0, dropped_count: 215212, finished_count: 124, ar_sticky_delta: 9, pfc_threshold: 9, spine_pfc_threshold: 9, pfc_threshold_1: 9, totalbytes: 24800124000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed703.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 552167.0, min_finished_at: 81637.0, dropped_count: 0, finished_count: 124, ar_sticky_delta: 10, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 24800124000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed703.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 550621.0, min_finished_at: 80288.0, dropped_count: 0, finished_count: 124, ar_sticky_delta: 10, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 24800124000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed703.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 708374.0, min_finished_at: 78103.0, dropped_count: 0, finished_count: 124, ar_sticky_delta: 10, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 7, totalbytes: 24800124000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed703.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 908514.0, min_finished_at: 75475.0, dropped_count: 1497, finished_count: 124, ar_sticky_delta: 10, pfc_threshold: 8, spine_pfc_threshold: 8, pfc_threshold_1: 8, totalbytes: 24800124000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed703.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 10 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 753680.0, min_finished_at: 69819.0, dropped_count: 258371, finished_count: 124, ar_sticky_delta: 10, pfc_threshold: 9, spine_pfc_threshold: 9, pfc_threshold_1: 9, totalbytes: 24800124000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed703.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 531524.0, min_finished_at: 81538.0, dropped_count: 0, finished_count: 124, ar_sticky_delta: 11, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 24800124000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed703.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 559187.0, min_finished_at: 81537.0, dropped_count: 0, finished_count: 124, ar_sticky_delta: 11, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 24800124000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed703.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 695053.0, min_finished_at: 74644.0, dropped_count: 0, finished_count: 124, ar_sticky_delta: 11, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 7, totalbytes: 24800124000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed703.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 892008.0, min_finished_at: 69260.0, dropped_count: 2108, finished_count: 124, ar_sticky_delta: 11, pfc_threshold: 8, spine_pfc_threshold: 8, pfc_threshold_1: 8, totalbytes: 24800124000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed703.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 11 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 772099.0, min_finished_at: 71072.0, dropped_count: 280664, finished_count: 124, ar_sticky_delta: 11, pfc_threshold: 9, spine_pfc_threshold: 9, pfc_threshold_1: 9, totalbytes: 24800124000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed703.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6 -pfc_thresholds_1 5 6 
For q: 100, max_finished_at: 541732.0, min_finished_at: 81587.0, dropped_count: 0, finished_count: 124, ar_sticky_delta: 12, pfc_threshold: 5, spine_pfc_threshold: 5, pfc_threshold_1: 5, totalbytes: 24800124000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed703.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7 -pfc_thresholds_1 6 7 
For q: 100, max_finished_at: 558781.0, min_finished_at: 80814.0, dropped_count: 0, finished_count: 124, ar_sticky_delta: 12, pfc_threshold: 6, spine_pfc_threshold: 6, pfc_threshold_1: 6, totalbytes: 24800124000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed703.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8 -pfc_thresholds_1 7 8 
For q: 100, max_finished_at: 717389.0, min_finished_at: 77476.0, dropped_count: 0, finished_count: 124, ar_sticky_delta: 12, pfc_threshold: 7, spine_pfc_threshold: 7, pfc_threshold_1: 7, totalbytes: 24800124000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed703.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9 -pfc_thresholds_1 8 9 
For q: 100, max_finished_at: 887054.0, min_finished_at: 72875.0, dropped_count: 1531, finished_count: 124, ar_sticky_delta: 12, pfc_threshold: 8, spine_pfc_threshold: 8, pfc_threshold_1: 8, totalbytes: 24800124000.0, seed_hash: 0, seed_path: 0
Running command: ./htsim_roce -topo topologies/fat_tree_128.topo -nodes 128 -conns 124 -strat ecmp_ar -end 50000000 -mtu 3000 -roce_cfg roce_config_file999.txt -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_31_seed703.cm -q 100 -seed_path 0 -seed 0 -ar_sticky_delta 12 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10 -pfc_thresholds_1 9 10 
For q: 100, max_finished_at: 854477.0, min_finished_at: 74376.0, dropped_count: 154152, finished_count: 124, ar_sticky_delta: 12, pfc_threshold: 9, spine_pfc_threshold: 9, pfc_threshold_1: 9, totalbytes: 24800124000.0, seed_hash: 0, seed_path: 0
