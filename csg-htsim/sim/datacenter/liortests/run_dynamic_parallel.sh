#!/bin/bash

cd ~/POC/csg-htsim/sim/datacenter

nodes=128
conns=128
incast_dest_value=999
protocol=roce # roce / ndp
collelctive=incast # (allreduce, incast)
flowsize=2000000
locality=1
cmdfile="liortests/"$collelctive"_"$protocol"_sim_commands.txt"
rm -f $cmdfile
touch $cmdfile

echo "=======Generating commands for $protocol with $collelctive collective operation=========="
echo "nodes: $nodes, conns: $conns, incast_dest_value: $incast_dest_value, protocol: $protocol, collelctive: $collelctive, flowsize: $flowsize, locality: $locality"
echo "This scripts generates a a file ($cmdfile) with list of poctests.py commands , to run the commands in prallel on multiple (8) cores use the command : cat liortests/sim_commands.txt | parallel -j 8"
echo "========================================================================================"
if [[ "$collelctive" == "allreduce" ]]; then
  for groupsize in 8 ; do
    for seed_value in 100 ; do
    #for seed_value in 701 702 703 704 715 720; do
#    for seed_value in 700 701 702 703 704 705 706 707 708 709 710 711 712 713 714 715 716 717 718 719 720; do
      cmfile="connection_matrices/allreduce${nodes}/allreduce_rand_flowsize_groupsize_${groupsize}_seed${seed_value}.cm"
      python3 connection_matrices/gen_allreduce.py $cmfile $nodes $conns $groupsize $flowsize $locality $seed_value
      # python3 connection_matrices/gen_allreduce_contiguous_by_groupsize.py $cmfile $nodes $conns $groupsize $flowsize $locality $seed_value
      outfile="liortests/${protocol}${nodes}/allreduce/${protocol}${nodes}_allreduce_rand_flowsize_${nodes}_groupsize_${groupsize}_seed${seed_value}.out"
      echo "python3 liortests/poctests.py $protocol $cmfile $nodes $conns $seed_value $collelctive > $outfile" >> $cmdfile
    done
  done  
else
  for incastratio in 63 ; do
    # for seed_value in 900 901; do
    for seed_value in 700 701 702 703 704 705 706 715 720; do
        cmfile="connection_matrices/incast${nodes}/incast_rand_flowsize_incastratio_${incastratio}_seed${seed_value}.cm"
        python3 connection_matrices/gen_incast_random_flowsize.py $cmfile $nodes $conns $incast_dest_value $incastratio 0 100 0 $seed_value
        outfile="liortests/${protocol}${nodes}/incast/${protocol}${nodes}_incast_rand_flowsize_${nodes}_incastratio_${incastratio}_seed${seed_value}.out"
        echo "python3 liortests/poctests.py $protocol $cmfile $nodes $conns $seed_value $collelctive > $outfile" >> $cmdfile
    done
  done
fi
echo "======================================================================================================================================================="
echo "This scripts generates a a file ($cmdfile) with list of poctests.py commands, to run the commands in prallel on multiple (1:16) cores use the command:
 "cat $cmdfile " | parallel -j 8"
echo "======================================================================================================================================================="

