protocol is ndp, connection matric is connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm , number of nodes is 128 , numbe of connections is 96
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q = 30 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 408 , JCT is: 55835.0us, fastest flow: 39884.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 111 , JCT is: 55801.0us, fastest flow: 39888.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 609 , JCT is: 62238.0us, fastest flow: 38680.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 61 , JCT is: 62281.0us, fastest flow: 38743.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 1137 , JCT is: 49574.0us, fastest flow: 49492.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 780 , JCT is: 49638.0us, fastest flow: 49465.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 415 , JCT is: 49655.0us, fastest flow: 49303.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 188 , JCT is: 49817.0us, fastest flow: 49051.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 22823 , JCT is: 51854.0us, fastest flow: 49487.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 25538 , JCT is: 51957.0us, fastest flow: 49485.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 92485 , JCT is: 54988.0us, fastest flow: 49485.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 94330 , JCT is: 54917.0us, fastest flow: 49484.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 14873 , JCT is: 50941.0us, fastest flow: 49484.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 23981 , JCT is: 51841.0us, fastest flow: 49480.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 55476 , JCT is: 54762.0us, fastest flow: 49481.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 53194 , JCT is: 54929.0us, fastest flow: 49480.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 408 , JCT is: 55835.0us, fastest flow: 39884.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 111 , JCT is: 55801.0us, fastest flow: 39888.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 609 , JCT is: 62238.0us, fastest flow: 38680.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 61 , JCT is: 62281.0us, fastest flow: 38743.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 1137 , JCT is: 49574.0us, fastest flow: 49492.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 780 , JCT is: 49638.0us, fastest flow: 49465.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 415 , JCT is: 49655.0us, fastest flow: 49303.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 188 , JCT is: 49817.0us, fastest flow: 49051.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 22823 , JCT is: 51854.0us, fastest flow: 49487.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 25538 , JCT is: 51957.0us, fastest flow: 49485.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 92485 , JCT is: 54988.0us, fastest flow: 49485.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 94330 , JCT is: 54917.0us, fastest flow: 49484.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 14873 , JCT is: 50941.0us, fastest flow: 49484.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 23981 , JCT is: 51841.0us, fastest flow: 49480.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 55476 , JCT is: 54762.0us, fastest flow: 49481.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 53194 , JCT is: 54929.0us, fastest flow: 49480.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 408 , JCT is: 55835.0us, fastest flow: 39884.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 111 , JCT is: 55801.0us, fastest flow: 39888.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 609 , JCT is: 62238.0us, fastest flow: 38680.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 61 , JCT is: 62281.0us, fastest flow: 38743.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 1137 , JCT is: 49574.0us, fastest flow: 49492.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 780 , JCT is: 49638.0us, fastest flow: 49465.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 415 , JCT is: 49655.0us, fastest flow: 49303.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 188 , JCT is: 49817.0us, fastest flow: 49051.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 22823 , JCT is: 51854.0us, fastest flow: 49487.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 25538 , JCT is: 51957.0us, fastest flow: 49485.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 92485 , JCT is: 54988.0us, fastest flow: 49485.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 94330 , JCT is: 54917.0us, fastest flow: 49484.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 14873 , JCT is: 50941.0us, fastest flow: 49484.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 23981 , JCT is: 51841.0us, fastest flow: 49480.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 55476 , JCT is: 54762.0us, fastest flow: 49481.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 53194 , JCT is: 54929.0us, fastest flow: 49480.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 408 , JCT is: 55835.0us, fastest flow: 39884.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 111 , JCT is: 55801.0us, fastest flow: 39888.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 609 , JCT is: 62238.0us, fastest flow: 38680.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 61 , JCT is: 62281.0us, fastest flow: 38743.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 1137 , JCT is: 49574.0us, fastest flow: 49492.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 780 , JCT is: 49638.0us, fastest flow: 49465.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 415 , JCT is: 49655.0us, fastest flow: 49303.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 188 , JCT is: 49817.0us, fastest flow: 49051.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 22823 , JCT is: 51854.0us, fastest flow: 49487.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 25538 , JCT is: 51957.0us, fastest flow: 49485.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 92485 , JCT is: 54988.0us, fastest flow: 49485.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 94330 , JCT is: 54917.0us, fastest flow: 49484.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 14873 , JCT is: 50941.0us, fastest flow: 49484.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 23981 , JCT is: 51841.0us, fastest flow: 49480.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 55476 , JCT is: 54762.0us, fastest flow: 49481.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 53194 , JCT is: 54929.0us, fastest flow: 49480.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 408 , JCT is: 55835.0us, fastest flow: 39884.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 111 , JCT is: 55801.0us, fastest flow: 39888.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 609 , JCT is: 62238.0us, fastest flow: 38680.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 61 , JCT is: 62281.0us, fastest flow: 38743.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 1137 , JCT is: 49574.0us, fastest flow: 49492.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 780 , JCT is: 49638.0us, fastest flow: 49465.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 415 , JCT is: 49655.0us, fastest flow: 49303.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 188 , JCT is: 49817.0us, fastest flow: 49051.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 22823 , JCT is: 51854.0us, fastest flow: 49487.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 25538 , JCT is: 51957.0us, fastest flow: 49485.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 92485 , JCT is: 54988.0us, fastest flow: 49485.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 94330 , JCT is: 54917.0us, fastest flow: 49484.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 14873 , JCT is: 50941.0us, fastest flow: 49484.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 23981 , JCT is: 51841.0us, fastest flow: 49480.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 55476 , JCT is: 54762.0us, fastest flow: 49481.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 53194 , JCT is: 54929.0us, fastest flow: 49480.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 408 , JCT is: 55835.0us, fastest flow: 39884.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 111 , JCT is: 55801.0us, fastest flow: 39888.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 609 , JCT is: 62238.0us, fastest flow: 38680.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 61 , JCT is: 62281.0us, fastest flow: 38743.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 1137 , JCT is: 49574.0us, fastest flow: 49492.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 780 , JCT is: 49638.0us, fastest flow: 49465.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 415 , JCT is: 49655.0us, fastest flow: 49303.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 188 , JCT is: 49817.0us, fastest flow: 49051.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 22823 , JCT is: 51854.0us, fastest flow: 49487.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 25538 , JCT is: 51957.0us, fastest flow: 49485.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 92485 , JCT is: 54988.0us, fastest flow: 49485.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 94330 , JCT is: 54917.0us, fastest flow: 49484.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 14873 , JCT is: 50941.0us, fastest flow: 49484.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 23981 , JCT is: 51841.0us, fastest flow: 49480.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 55476 , JCT is: 54762.0us, fastest flow: 49481.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 53194 , JCT is: 54929.0us, fastest flow: 49480.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 408 , JCT is: 55835.0us, fastest flow: 39884.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 111 , JCT is: 55801.0us, fastest flow: 39888.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 609 , JCT is: 62238.0us, fastest flow: 38680.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 61 , JCT is: 62281.0us, fastest flow: 38743.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 1137 , JCT is: 49574.0us, fastest flow: 49492.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 780 , JCT is: 49638.0us, fastest flow: 49465.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 415 , JCT is: 49655.0us, fastest flow: 49303.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 188 , JCT is: 49817.0us, fastest flow: 49051.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 22823 , JCT is: 51854.0us, fastest flow: 49487.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 25538 , JCT is: 51957.0us, fastest flow: 49485.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 92485 , JCT is: 54988.0us, fastest flow: 49485.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 94330 , JCT is: 54917.0us, fastest flow: 49484.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 14873 , JCT is: 50941.0us, fastest flow: 49484.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 23981 , JCT is: 51841.0us, fastest flow: 49480.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 55476 , JCT is: 54762.0us, fastest flow: 49481.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 53194 , JCT is: 54929.0us, fastest flow: 49480.0us 

The shortest jct : 49574.0us, with qsize = 30, cwnd = 40, ar_sticky_delta = 6 ,pcf_thresholds = 3 4 ,spine_pfc_threshold = 3 4
The slowest jct : 62281.0us, with qsize = 60, cwnd = 30 , ar_sticky_delta = 6,pcf_thresholds = 3 4 ,spine_pfc_threshold = 3 4

------------------------------------ Thw above results are for the following base command ----------------------
./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000
----------------------------------------------------------------------------------------------------------------
