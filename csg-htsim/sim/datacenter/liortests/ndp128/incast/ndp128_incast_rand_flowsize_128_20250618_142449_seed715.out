Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 30, max_finished_at: 58025.0, min_finished_at: 40669.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 40, max_finished_at: 58265.0, min_finished_at: 40886.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 50, max_finished_at: 63370.0, min_finished_at: 41542.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 60, max_finished_at: 63776.0, min_finished_at: 41514.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 30, max_finished_at: 51844.0, min_finished_at: 49492.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 40, max_finished_at: 52143.0, min_finished_at: 48436.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 50, max_finished_at: 56657.0, min_finished_at: 45639.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 60, max_finished_at: 57419.0, min_finished_at: 43960.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 30, max_finished_at: 51365.0, min_finished_at: 49490.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 40, max_finished_at: 51829.0, min_finished_at: 49488.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 50, max_finished_at: 56129.0, min_finished_at: 49487.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 60, max_finished_at: 56172.0, min_finished_at: 49482.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 30, max_finished_at: 49546.0, min_finished_at: 49484.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 40, max_finished_at: 49544.0, min_finished_at: 49480.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 50, max_finished_at: 49568.0, min_finished_at: 49479.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 60, max_finished_at: 49583.0, min_finished_at: 49482.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 30, max_finished_at: 58025.0, min_finished_at: 40669.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 40, max_finished_at: 58265.0, min_finished_at: 40886.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 50, max_finished_at: 63370.0, min_finished_at: 41542.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 60, max_finished_at: 63776.0, min_finished_at: 41514.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 30, max_finished_at: 51844.0, min_finished_at: 49492.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 40, max_finished_at: 52143.0, min_finished_at: 48436.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 50, max_finished_at: 56657.0, min_finished_at: 45639.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 60, max_finished_at: 57419.0, min_finished_at: 43960.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 30, max_finished_at: 51365.0, min_finished_at: 49490.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 40, max_finished_at: 51829.0, min_finished_at: 49488.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 50, max_finished_at: 56129.0, min_finished_at: 49487.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 60, max_finished_at: 56172.0, min_finished_at: 49482.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 30, max_finished_at: 49546.0, min_finished_at: 49484.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 40, max_finished_at: 49544.0, min_finished_at: 49480.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 50, max_finished_at: 49568.0, min_finished_at: 49479.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 60, max_finished_at: 49583.0, min_finished_at: 49482.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 30, max_finished_at: 58025.0, min_finished_at: 40669.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 40, max_finished_at: 58265.0, min_finished_at: 40886.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 50, max_finished_at: 63370.0, min_finished_at: 41542.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 60, max_finished_at: 63776.0, min_finished_at: 41514.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 30, max_finished_at: 51844.0, min_finished_at: 49492.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 40, max_finished_at: 52143.0, min_finished_at: 48436.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 50, max_finished_at: 56657.0, min_finished_at: 45639.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 60, max_finished_at: 57419.0, min_finished_at: 43960.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 30, max_finished_at: 51365.0, min_finished_at: 49490.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 40, max_finished_at: 51829.0, min_finished_at: 49488.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 50, max_finished_at: 56129.0, min_finished_at: 49487.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 60, max_finished_at: 56172.0, min_finished_at: 49482.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 30, max_finished_at: 49546.0, min_finished_at: 49484.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 40, max_finished_at: 49544.0, min_finished_at: 49480.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 50, max_finished_at: 49568.0, min_finished_at: 49479.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 60, max_finished_at: 49583.0, min_finished_at: 49482.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 30, max_finished_at: 58025.0, min_finished_at: 40669.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 40, max_finished_at: 58265.0, min_finished_at: 40886.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 50, max_finished_at: 63370.0, min_finished_at: 41542.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 60, max_finished_at: 63776.0, min_finished_at: 41514.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 30, max_finished_at: 51844.0, min_finished_at: 49492.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 40, max_finished_at: 52143.0, min_finished_at: 48436.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 50, max_finished_at: 56657.0, min_finished_at: 45639.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 60, max_finished_at: 57419.0, min_finished_at: 43960.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 30, max_finished_at: 51365.0, min_finished_at: 49490.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 40, max_finished_at: 51829.0, min_finished_at: 49488.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 50, max_finished_at: 56129.0, min_finished_at: 49487.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 60, max_finished_at: 56172.0, min_finished_at: 49482.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 30, max_finished_at: 49546.0, min_finished_at: 49484.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 40, max_finished_at: 49544.0, min_finished_at: 49480.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 50, max_finished_at: 49568.0, min_finished_at: 49479.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 60, max_finished_at: 49583.0, min_finished_at: 49482.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 30, max_finished_at: 58025.0, min_finished_at: 40669.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 40, max_finished_at: 58265.0, min_finished_at: 40886.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 50, max_finished_at: 63370.0, min_finished_at: 41542.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 60, max_finished_at: 63776.0, min_finished_at: 41514.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 30, max_finished_at: 51844.0, min_finished_at: 49492.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 40, max_finished_at: 52143.0, min_finished_at: 48436.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 50, max_finished_at: 56657.0, min_finished_at: 45639.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 60, max_finished_at: 57419.0, min_finished_at: 43960.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 30, max_finished_at: 51365.0, min_finished_at: 49490.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 40, max_finished_at: 51829.0, min_finished_at: 49488.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 50, max_finished_at: 56129.0, min_finished_at: 49487.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 60, max_finished_at: 56172.0, min_finished_at: 49482.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 30, max_finished_at: 49546.0, min_finished_at: 49484.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 40, max_finished_at: 49544.0, min_finished_at: 49480.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 50, max_finished_at: 49568.0, min_finished_at: 49479.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 60, max_finished_at: 49583.0, min_finished_at: 49482.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 30, max_finished_at: 58025.0, min_finished_at: 40669.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 40, max_finished_at: 58265.0, min_finished_at: 40886.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 50, max_finished_at: 63370.0, min_finished_at: 41542.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 60, max_finished_at: 63776.0, min_finished_at: 41514.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 30, max_finished_at: 51844.0, min_finished_at: 49492.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 40, max_finished_at: 52143.0, min_finished_at: 48436.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 50, max_finished_at: 56657.0, min_finished_at: 45639.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 60, max_finished_at: 57419.0, min_finished_at: 43960.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 30, max_finished_at: 51365.0, min_finished_at: 49490.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 40, max_finished_at: 51829.0, min_finished_at: 49488.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 50, max_finished_at: 56129.0, min_finished_at: 49487.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 60, max_finished_at: 56172.0, min_finished_at: 49482.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 30, max_finished_at: 49546.0, min_finished_at: 49484.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 40, max_finished_at: 49544.0, min_finished_at: 49480.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 50, max_finished_at: 49568.0, min_finished_at: 49479.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 60, max_finished_at: 49583.0, min_finished_at: 49482.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 30, max_finished_at: 58025.0, min_finished_at: 40669.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 40, max_finished_at: 58265.0, min_finished_at: 40886.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 50, max_finished_at: 63370.0, min_finished_at: 41542.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 60, max_finished_at: 63776.0, min_finished_at: 41514.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 30, max_finished_at: 51844.0, min_finished_at: 49492.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 40, max_finished_at: 52143.0, min_finished_at: 48436.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 50, max_finished_at: 56657.0, min_finished_at: 45639.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 60, max_finished_at: 57419.0, min_finished_at: 43960.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 30, max_finished_at: 51365.0, min_finished_at: 49490.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 40, max_finished_at: 51829.0, min_finished_at: 49488.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 50, max_finished_at: 56129.0, min_finished_at: 49487.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 60, max_finished_at: 56172.0, min_finished_at: 49482.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 30, max_finished_at: 49546.0, min_finished_at: 49484.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 40, max_finished_at: 49544.0, min_finished_at: 49480.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 50, max_finished_at: 49568.0, min_finished_at: 49479.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 60, max_finished_at: 49583.0, min_finished_at: 49482.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 60, totalbytes: 19200096000.0
