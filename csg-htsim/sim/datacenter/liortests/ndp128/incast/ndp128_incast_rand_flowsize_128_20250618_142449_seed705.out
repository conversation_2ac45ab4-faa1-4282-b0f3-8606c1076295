Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 30, max_finished_at: 54196.0, min_finished_at: 41852.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 40, max_finished_at: 54225.0, min_finished_at: 41893.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 50, max_finished_at: 54313.0, min_finished_at: 41714.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 60, max_finished_at: 54301.0, min_finished_at: 41780.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 30, max_finished_at: 49550.0, min_finished_at: 49490.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 40, max_finished_at: 49549.0, min_finished_at: 49489.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 50, max_finished_at: 50323.0, min_finished_at: 48623.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 60, max_finished_at: 50679.0, min_finished_at: 47738.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 30, max_finished_at: 49548.0, min_finished_at: 49486.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 40, max_finished_at: 49545.0, min_finished_at: 49491.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 50, max_finished_at: 49651.0, min_finished_at: 49484.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 60, max_finished_at: 49540.0, min_finished_at: 49480.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 30, max_finished_at: 49545.0, min_finished_at: 49483.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 40, max_finished_at: 49544.0, min_finished_at: 49483.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 50, max_finished_at: 49541.0, min_finished_at: 49480.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 60, max_finished_at: 49538.0, min_finished_at: 49481.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 30, max_finished_at: 54196.0, min_finished_at: 41852.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 40, max_finished_at: 54225.0, min_finished_at: 41893.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 50, max_finished_at: 54313.0, min_finished_at: 41714.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 60, max_finished_at: 54301.0, min_finished_at: 41780.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 30, max_finished_at: 49550.0, min_finished_at: 49490.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 40, max_finished_at: 49549.0, min_finished_at: 49489.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 50, max_finished_at: 50323.0, min_finished_at: 48623.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 60, max_finished_at: 50679.0, min_finished_at: 47738.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 30, max_finished_at: 49548.0, min_finished_at: 49486.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 40, max_finished_at: 49545.0, min_finished_at: 49491.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 50, max_finished_at: 49651.0, min_finished_at: 49484.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 60, max_finished_at: 49540.0, min_finished_at: 49480.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 30, max_finished_at: 49545.0, min_finished_at: 49483.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 40, max_finished_at: 49544.0, min_finished_at: 49483.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 50, max_finished_at: 49541.0, min_finished_at: 49480.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 60, max_finished_at: 49538.0, min_finished_at: 49481.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 30, max_finished_at: 54196.0, min_finished_at: 41852.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 40, max_finished_at: 54225.0, min_finished_at: 41893.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 50, max_finished_at: 54313.0, min_finished_at: 41714.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 60, max_finished_at: 54301.0, min_finished_at: 41780.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 30, max_finished_at: 49550.0, min_finished_at: 49490.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 40, max_finished_at: 49549.0, min_finished_at: 49489.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 50, max_finished_at: 50323.0, min_finished_at: 48623.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 60, max_finished_at: 50679.0, min_finished_at: 47738.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 30, max_finished_at: 49548.0, min_finished_at: 49486.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 40, max_finished_at: 49545.0, min_finished_at: 49491.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 50, max_finished_at: 49651.0, min_finished_at: 49484.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 60, max_finished_at: 49540.0, min_finished_at: 49480.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 30, max_finished_at: 49545.0, min_finished_at: 49483.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 40, max_finished_at: 49544.0, min_finished_at: 49483.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 50, max_finished_at: 49541.0, min_finished_at: 49480.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 60, max_finished_at: 49538.0, min_finished_at: 49481.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 30, max_finished_at: 54196.0, min_finished_at: 41852.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 40, max_finished_at: 54225.0, min_finished_at: 41893.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 50, max_finished_at: 54313.0, min_finished_at: 41714.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 60, max_finished_at: 54301.0, min_finished_at: 41780.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 30, max_finished_at: 49550.0, min_finished_at: 49490.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 40, max_finished_at: 49549.0, min_finished_at: 49489.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 50, max_finished_at: 50323.0, min_finished_at: 48623.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 60, max_finished_at: 50679.0, min_finished_at: 47738.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 30, max_finished_at: 49548.0, min_finished_at: 49486.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 40, max_finished_at: 49545.0, min_finished_at: 49491.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 50, max_finished_at: 49651.0, min_finished_at: 49484.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 60, max_finished_at: 49540.0, min_finished_at: 49480.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 30, max_finished_at: 49545.0, min_finished_at: 49483.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 40, max_finished_at: 49544.0, min_finished_at: 49483.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 50, max_finished_at: 49541.0, min_finished_at: 49480.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 60, max_finished_at: 49538.0, min_finished_at: 49481.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 30, max_finished_at: 54196.0, min_finished_at: 41852.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 40, max_finished_at: 54225.0, min_finished_at: 41893.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 50, max_finished_at: 54313.0, min_finished_at: 41714.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 60, max_finished_at: 54301.0, min_finished_at: 41780.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 30, max_finished_at: 49550.0, min_finished_at: 49490.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 40, max_finished_at: 49549.0, min_finished_at: 49489.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 50, max_finished_at: 50323.0, min_finished_at: 48623.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 60, max_finished_at: 50679.0, min_finished_at: 47738.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 30, max_finished_at: 49548.0, min_finished_at: 49486.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 40, max_finished_at: 49545.0, min_finished_at: 49491.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 50, max_finished_at: 49651.0, min_finished_at: 49484.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 60, max_finished_at: 49540.0, min_finished_at: 49480.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 30, max_finished_at: 49545.0, min_finished_at: 49483.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 40, max_finished_at: 49544.0, min_finished_at: 49483.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 50, max_finished_at: 49541.0, min_finished_at: 49480.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 60, max_finished_at: 49538.0, min_finished_at: 49481.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 30, max_finished_at: 54196.0, min_finished_at: 41852.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 40, max_finished_at: 54225.0, min_finished_at: 41893.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 50, max_finished_at: 54313.0, min_finished_at: 41714.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 60, max_finished_at: 54301.0, min_finished_at: 41780.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 30, max_finished_at: 49550.0, min_finished_at: 49490.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 40, max_finished_at: 49549.0, min_finished_at: 49489.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 50, max_finished_at: 50323.0, min_finished_at: 48623.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 60, max_finished_at: 50679.0, min_finished_at: 47738.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 30, max_finished_at: 49548.0, min_finished_at: 49486.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 40, max_finished_at: 49545.0, min_finished_at: 49491.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 50, max_finished_at: 49651.0, min_finished_at: 49484.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 60, max_finished_at: 49540.0, min_finished_at: 49480.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 30, max_finished_at: 49545.0, min_finished_at: 49483.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 40, max_finished_at: 49544.0, min_finished_at: 49483.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 50, max_finished_at: 49541.0, min_finished_at: 49480.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 60, max_finished_at: 49538.0, min_finished_at: 49481.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 30, max_finished_at: 54196.0, min_finished_at: 41852.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 40, max_finished_at: 54225.0, min_finished_at: 41893.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 50, max_finished_at: 54313.0, min_finished_at: 41714.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 60, max_finished_at: 54301.0, min_finished_at: 41780.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 30, max_finished_at: 49550.0, min_finished_at: 49490.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 40, max_finished_at: 49549.0, min_finished_at: 49489.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 50, max_finished_at: 50323.0, min_finished_at: 48623.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 60, max_finished_at: 50679.0, min_finished_at: 47738.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 30, max_finished_at: 49548.0, min_finished_at: 49486.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 40, max_finished_at: 49545.0, min_finished_at: 49491.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 50, max_finished_at: 49651.0, min_finished_at: 49484.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 60, max_finished_at: 49540.0, min_finished_at: 49480.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 30, max_finished_at: 49545.0, min_finished_at: 49483.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 40, max_finished_at: 49544.0, min_finished_at: 49483.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 50, max_finished_at: 49541.0, min_finished_at: 49480.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed705.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 60, max_finished_at: 49538.0, min_finished_at: 49481.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 60, totalbytes: 19200096000.0
