Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 30, max_finished_at: 56589.0, min_finished_at: 41410.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 40, max_finished_at: 56633.0, min_finished_at: 41412.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 50, max_finished_at: 61537.0, min_finished_at: 40776.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 60, max_finished_at: 61719.0, min_finished_at: 40690.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 30, max_finished_at: 49556.0, min_finished_at: 49491.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 40, max_finished_at: 49559.0, min_finished_at: 49491.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 50, max_finished_at: 53375.0, min_finished_at: 47265.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 60, max_finished_at: 53973.0, min_finished_at: 45354.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 30, max_finished_at: 53576.0, min_finished_at: 49492.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 40, max_finished_at: 53666.0, min_finished_at: 49490.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 50, max_finished_at: 57504.0, min_finished_at: 49487.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 60, max_finished_at: 57865.0, min_finished_at: 49485.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 30, max_finished_at: 50495.0, min_finished_at: 49486.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 40, max_finished_at: 50841.0, min_finished_at: 49489.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 50, max_finished_at: 54355.0, min_finished_at: 49484.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 60, max_finished_at: 54151.0, min_finished_at: 49478.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 30, max_finished_at: 56589.0, min_finished_at: 41410.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 40, max_finished_at: 56633.0, min_finished_at: 41412.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 50, max_finished_at: 61537.0, min_finished_at: 40776.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 60, max_finished_at: 61719.0, min_finished_at: 40690.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 30, max_finished_at: 49556.0, min_finished_at: 49491.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 40, max_finished_at: 49559.0, min_finished_at: 49491.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 50, max_finished_at: 53375.0, min_finished_at: 47265.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 60, max_finished_at: 53973.0, min_finished_at: 45354.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 30, max_finished_at: 53576.0, min_finished_at: 49492.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 40, max_finished_at: 53666.0, min_finished_at: 49490.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 50, max_finished_at: 57504.0, min_finished_at: 49487.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 60, max_finished_at: 57865.0, min_finished_at: 49485.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 30, max_finished_at: 50495.0, min_finished_at: 49486.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 40, max_finished_at: 50841.0, min_finished_at: 49489.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 50, max_finished_at: 54355.0, min_finished_at: 49484.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 60, max_finished_at: 54151.0, min_finished_at: 49478.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 30, max_finished_at: 56589.0, min_finished_at: 41410.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 40, max_finished_at: 56633.0, min_finished_at: 41412.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 50, max_finished_at: 61537.0, min_finished_at: 40776.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 60, max_finished_at: 61719.0, min_finished_at: 40690.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 30, max_finished_at: 49556.0, min_finished_at: 49491.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 40, max_finished_at: 49559.0, min_finished_at: 49491.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 50, max_finished_at: 53375.0, min_finished_at: 47265.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 60, max_finished_at: 53973.0, min_finished_at: 45354.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 30, max_finished_at: 53576.0, min_finished_at: 49492.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 40, max_finished_at: 53666.0, min_finished_at: 49490.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 50, max_finished_at: 57504.0, min_finished_at: 49487.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 60, max_finished_at: 57865.0, min_finished_at: 49485.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 30, max_finished_at: 50495.0, min_finished_at: 49486.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 40, max_finished_at: 50841.0, min_finished_at: 49489.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 50, max_finished_at: 54355.0, min_finished_at: 49484.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 60, max_finished_at: 54151.0, min_finished_at: 49478.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 30, max_finished_at: 56589.0, min_finished_at: 41410.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 40, max_finished_at: 56633.0, min_finished_at: 41412.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 50, max_finished_at: 61537.0, min_finished_at: 40776.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 60, max_finished_at: 61719.0, min_finished_at: 40690.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 30, max_finished_at: 49556.0, min_finished_at: 49491.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 40, max_finished_at: 49559.0, min_finished_at: 49491.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 50, max_finished_at: 53375.0, min_finished_at: 47265.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 60, max_finished_at: 53973.0, min_finished_at: 45354.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 30, max_finished_at: 53576.0, min_finished_at: 49492.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 40, max_finished_at: 53666.0, min_finished_at: 49490.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 50, max_finished_at: 57504.0, min_finished_at: 49487.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 60, max_finished_at: 57865.0, min_finished_at: 49485.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 30, max_finished_at: 50495.0, min_finished_at: 49486.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 40, max_finished_at: 50841.0, min_finished_at: 49489.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 50, max_finished_at: 54355.0, min_finished_at: 49484.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 60, max_finished_at: 54151.0, min_finished_at: 49478.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 30, max_finished_at: 56589.0, min_finished_at: 41410.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 40, max_finished_at: 56633.0, min_finished_at: 41412.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 50, max_finished_at: 61537.0, min_finished_at: 40776.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 60, max_finished_at: 61719.0, min_finished_at: 40690.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 30, max_finished_at: 49556.0, min_finished_at: 49491.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 40, max_finished_at: 49559.0, min_finished_at: 49491.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 50, max_finished_at: 53375.0, min_finished_at: 47265.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 60, max_finished_at: 53973.0, min_finished_at: 45354.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 30, max_finished_at: 53576.0, min_finished_at: 49492.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 40, max_finished_at: 53666.0, min_finished_at: 49490.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 50, max_finished_at: 57504.0, min_finished_at: 49487.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 60, max_finished_at: 57865.0, min_finished_at: 49485.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 30, max_finished_at: 50495.0, min_finished_at: 49486.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 40, max_finished_at: 50841.0, min_finished_at: 49489.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 50, max_finished_at: 54355.0, min_finished_at: 49484.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 60, max_finished_at: 54151.0, min_finished_at: 49478.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 30, max_finished_at: 56589.0, min_finished_at: 41410.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 40, max_finished_at: 56633.0, min_finished_at: 41412.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 50, max_finished_at: 61537.0, min_finished_at: 40776.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 60, max_finished_at: 61719.0, min_finished_at: 40690.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 30, max_finished_at: 49556.0, min_finished_at: 49491.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 40, max_finished_at: 49559.0, min_finished_at: 49491.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 50, max_finished_at: 53375.0, min_finished_at: 47265.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 60, max_finished_at: 53973.0, min_finished_at: 45354.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 30, max_finished_at: 53576.0, min_finished_at: 49492.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 40, max_finished_at: 53666.0, min_finished_at: 49490.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 50, max_finished_at: 57504.0, min_finished_at: 49487.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 60, max_finished_at: 57865.0, min_finished_at: 49485.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 30, max_finished_at: 50495.0, min_finished_at: 49486.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 40, max_finished_at: 50841.0, min_finished_at: 49489.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 50, max_finished_at: 54355.0, min_finished_at: 49484.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 60, max_finished_at: 54151.0, min_finished_at: 49478.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 30, max_finished_at: 56589.0, min_finished_at: 41410.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 40, max_finished_at: 56633.0, min_finished_at: 41412.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 50, max_finished_at: 61537.0, min_finished_at: 40776.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 60, max_finished_at: 61719.0, min_finished_at: 40690.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 30, max_finished_at: 49556.0, min_finished_at: 49491.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 40, max_finished_at: 49559.0, min_finished_at: 49491.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 50, max_finished_at: 53375.0, min_finished_at: 47265.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 60, max_finished_at: 53973.0, min_finished_at: 45354.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 30, max_finished_at: 53576.0, min_finished_at: 49492.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 40, max_finished_at: 53666.0, min_finished_at: 49490.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 50, max_finished_at: 57504.0, min_finished_at: 49487.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 60, max_finished_at: 57865.0, min_finished_at: 49485.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 30, max_finished_at: 50495.0, min_finished_at: 49486.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 40, max_finished_at: 50841.0, min_finished_at: 49489.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 50, max_finished_at: 54355.0, min_finished_at: 49484.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 60, max_finished_at: 54151.0, min_finished_at: 49478.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 60, totalbytes: 19200096000.0
