protocol is ndp, connection matric is connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm , number of nodes is 128 , numbe of connections is 96
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed704.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q = 30 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 315 , JCT is: 54442.0us, fastest flow: 41531.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 93 , JCT is: 54410.0us, fastest flow: 41589.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 9 , JCT is: 56834.0us, fastest flow: 40954.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 0 , JCT is: 56926.0us, fastest flow: 40945.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 13955 , JCT is: 50972.0us, fastest flow: 49518.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 12436 , JCT is: 51564.0us, fastest flow: 48501.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 91153 , JCT is: 56314.0us, fastest flow: 45127.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 71900 , JCT is: 57157.0us, fastest flow: 43594.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 21216 , JCT is: 51574.0us, fastest flow: 49515.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 17783 , JCT is: 51202.0us, fastest flow: 49512.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 50587 , JCT is: 52321.0us, fastest flow: 49512.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 45746 , JCT is: 51827.0us, fastest flow: 49507.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 3690 , JCT is: 49703.0us, fastest flow: 49512.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 5037 , JCT is: 49878.0us, fastest flow: 49513.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 47575 , JCT is: 52200.0us, fastest flow: 49511.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 45417 , JCT is: 51988.0us, fastest flow: 49507.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 315 , JCT is: 54442.0us, fastest flow: 41531.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 93 , JCT is: 54410.0us, fastest flow: 41589.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 9 , JCT is: 56834.0us, fastest flow: 40954.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 0 , JCT is: 56926.0us, fastest flow: 40945.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 13955 , JCT is: 50972.0us, fastest flow: 49518.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 12436 , JCT is: 51564.0us, fastest flow: 48501.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 91153 , JCT is: 56314.0us, fastest flow: 45127.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 71900 , JCT is: 57157.0us, fastest flow: 43594.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 21216 , JCT is: 51574.0us, fastest flow: 49515.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 17783 , JCT is: 51202.0us, fastest flow: 49512.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 50587 , JCT is: 52321.0us, fastest flow: 49512.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 45746 , JCT is: 51827.0us, fastest flow: 49507.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 3690 , JCT is: 49703.0us, fastest flow: 49512.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 5037 , JCT is: 49878.0us, fastest flow: 49513.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 47575 , JCT is: 52200.0us, fastest flow: 49511.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 45417 , JCT is: 51988.0us, fastest flow: 49507.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 315 , JCT is: 54442.0us, fastest flow: 41531.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 93 , JCT is: 54410.0us, fastest flow: 41589.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 9 , JCT is: 56834.0us, fastest flow: 40954.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 0 , JCT is: 56926.0us, fastest flow: 40945.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 13955 , JCT is: 50972.0us, fastest flow: 49518.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 12436 , JCT is: 51564.0us, fastest flow: 48501.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 91153 , JCT is: 56314.0us, fastest flow: 45127.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 71900 , JCT is: 57157.0us, fastest flow: 43594.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 21216 , JCT is: 51574.0us, fastest flow: 49515.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 17783 , JCT is: 51202.0us, fastest flow: 49512.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 50587 , JCT is: 52321.0us, fastest flow: 49512.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 45746 , JCT is: 51827.0us, fastest flow: 49507.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 3690 , JCT is: 49703.0us, fastest flow: 49512.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 5037 , JCT is: 49878.0us, fastest flow: 49513.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 47575 , JCT is: 52200.0us, fastest flow: 49511.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 45417 , JCT is: 51988.0us, fastest flow: 49507.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 315 , JCT is: 54442.0us, fastest flow: 41531.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 93 , JCT is: 54410.0us, fastest flow: 41589.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 9 , JCT is: 56834.0us, fastest flow: 40954.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 0 , JCT is: 56926.0us, fastest flow: 40945.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 13955 , JCT is: 50972.0us, fastest flow: 49518.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 12436 , JCT is: 51564.0us, fastest flow: 48501.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 91153 , JCT is: 56314.0us, fastest flow: 45127.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 71900 , JCT is: 57157.0us, fastest flow: 43594.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 21216 , JCT is: 51574.0us, fastest flow: 49515.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 17783 , JCT is: 51202.0us, fastest flow: 49512.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 50587 , JCT is: 52321.0us, fastest flow: 49512.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 45746 , JCT is: 51827.0us, fastest flow: 49507.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 3690 , JCT is: 49703.0us, fastest flow: 49512.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 5037 , JCT is: 49878.0us, fastest flow: 49513.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 47575 , JCT is: 52200.0us, fastest flow: 49511.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 45417 , JCT is: 51988.0us, fastest flow: 49507.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 315 , JCT is: 54442.0us, fastest flow: 41531.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 93 , JCT is: 54410.0us, fastest flow: 41589.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 9 , JCT is: 56834.0us, fastest flow: 40954.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 0 , JCT is: 56926.0us, fastest flow: 40945.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 13955 , JCT is: 50972.0us, fastest flow: 49518.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 12436 , JCT is: 51564.0us, fastest flow: 48501.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 91153 , JCT is: 56314.0us, fastest flow: 45127.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 71900 , JCT is: 57157.0us, fastest flow: 43594.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 21216 , JCT is: 51574.0us, fastest flow: 49515.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 17783 , JCT is: 51202.0us, fastest flow: 49512.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 50587 , JCT is: 52321.0us, fastest flow: 49512.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 45746 , JCT is: 51827.0us, fastest flow: 49507.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 3690 , JCT is: 49703.0us, fastest flow: 49512.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 5037 , JCT is: 49878.0us, fastest flow: 49513.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 47575 , JCT is: 52200.0us, fastest flow: 49511.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 45417 , JCT is: 51988.0us, fastest flow: 49507.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 315 , JCT is: 54442.0us, fastest flow: 41531.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 93 , JCT is: 54410.0us, fastest flow: 41589.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 9 , JCT is: 56834.0us, fastest flow: 40954.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 0 , JCT is: 56926.0us, fastest flow: 40945.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 13955 , JCT is: 50972.0us, fastest flow: 49518.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 12436 , JCT is: 51564.0us, fastest flow: 48501.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 91153 , JCT is: 56314.0us, fastest flow: 45127.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 71900 , JCT is: 57157.0us, fastest flow: 43594.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 21216 , JCT is: 51574.0us, fastest flow: 49515.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 17783 , JCT is: 51202.0us, fastest flow: 49512.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 50587 , JCT is: 52321.0us, fastest flow: 49512.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 45746 , JCT is: 51827.0us, fastest flow: 49507.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 3690 , JCT is: 49703.0us, fastest flow: 49512.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 5037 , JCT is: 49878.0us, fastest flow: 49513.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 47575 , JCT is: 52200.0us, fastest flow: 49511.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 45417 , JCT is: 51988.0us, fastest flow: 49507.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 315 , JCT is: 54442.0us, fastest flow: 41531.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 93 , JCT is: 54410.0us, fastest flow: 41589.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 9 , JCT is: 56834.0us, fastest flow: 40954.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 0 , JCT is: 56926.0us, fastest flow: 40945.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 13955 , JCT is: 50972.0us, fastest flow: 49518.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 12436 , JCT is: 51564.0us, fastest flow: 48501.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 91153 , JCT is: 56314.0us, fastest flow: 45127.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 71900 , JCT is: 57157.0us, fastest flow: 43594.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 21216 , JCT is: 51574.0us, fastest flow: 49515.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 17783 , JCT is: 51202.0us, fastest flow: 49512.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 50587 , JCT is: 52321.0us, fastest flow: 49512.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 45746 , JCT is: 51827.0us, fastest flow: 49507.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 3690 , JCT is: 49703.0us, fastest flow: 49512.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 5037 , JCT is: 49878.0us, fastest flow: 49513.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 47575 , JCT is: 52200.0us, fastest flow: 49511.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 45417 , JCT is: 51988.0us, fastest flow: 49507.0us 

The shortest jct : 49703.0us, with qsize = 30, cwnd = 60, ar_sticky_delta = 6 ,pcf_thresholds = 3 4 ,spine_pfc_threshold = 3 4
The slowest jct : 57157.0us, with qsize = 60, cwnd = 40 , ar_sticky_delta = 6,pcf_thresholds = 3 4 ,spine_pfc_threshold = 3 4

------------------------------------ Thw above results are for the following base command ----------------------
./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000
----------------------------------------------------------------------------------------------------------------
