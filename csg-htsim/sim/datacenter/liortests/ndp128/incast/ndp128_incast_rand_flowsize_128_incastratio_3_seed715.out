protocol is ndp, connection matric is connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm , number of nodes is 128 , numbe of connections is 96
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed715.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q = 30 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 3969 , JCT is: 58025.0us, fastest flow: 40669.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 585 , JCT is: 58265.0us, fastest flow: 40886.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 7698 , JCT is: 63370.0us, fastest flow: 41542.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 652 , JCT is: 63776.0us, fastest flow: 41514.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 26887 , JCT is: 51844.0us, fastest flow: 49492.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 24716 , JCT is: 52143.0us, fastest flow: 48436.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 80031 , JCT is: 56657.0us, fastest flow: 45639.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 63324 , JCT is: 57419.0us, fastest flow: 43960.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 30602 , JCT is: 51365.0us, fastest flow: 49490.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 38789 , JCT is: 51829.0us, fastest flow: 49488.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 122853 , JCT is: 56129.0us, fastest flow: 49487.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 121793 , JCT is: 56172.0us, fastest flow: 49482.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 2402 , JCT is: 49546.0us, fastest flow: 49484.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 2064 , JCT is: 49544.0us, fastest flow: 49480.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 2047 , JCT is: 49568.0us, fastest flow: 49479.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 1768 , JCT is: 49583.0us, fastest flow: 49482.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 3969 , JCT is: 58025.0us, fastest flow: 40669.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 585 , JCT is: 58265.0us, fastest flow: 40886.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 7698 , JCT is: 63370.0us, fastest flow: 41542.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 652 , JCT is: 63776.0us, fastest flow: 41514.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 26887 , JCT is: 51844.0us, fastest flow: 49492.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 24716 , JCT is: 52143.0us, fastest flow: 48436.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 80031 , JCT is: 56657.0us, fastest flow: 45639.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 63324 , JCT is: 57419.0us, fastest flow: 43960.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 30602 , JCT is: 51365.0us, fastest flow: 49490.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 38789 , JCT is: 51829.0us, fastest flow: 49488.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 122853 , JCT is: 56129.0us, fastest flow: 49487.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 121793 , JCT is: 56172.0us, fastest flow: 49482.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 2402 , JCT is: 49546.0us, fastest flow: 49484.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 2064 , JCT is: 49544.0us, fastest flow: 49480.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 2047 , JCT is: 49568.0us, fastest flow: 49479.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 1768 , JCT is: 49583.0us, fastest flow: 49482.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 3969 , JCT is: 58025.0us, fastest flow: 40669.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 585 , JCT is: 58265.0us, fastest flow: 40886.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 7698 , JCT is: 63370.0us, fastest flow: 41542.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 652 , JCT is: 63776.0us, fastest flow: 41514.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 26887 , JCT is: 51844.0us, fastest flow: 49492.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 24716 , JCT is: 52143.0us, fastest flow: 48436.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 80031 , JCT is: 56657.0us, fastest flow: 45639.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 63324 , JCT is: 57419.0us, fastest flow: 43960.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 30602 , JCT is: 51365.0us, fastest flow: 49490.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 38789 , JCT is: 51829.0us, fastest flow: 49488.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 122853 , JCT is: 56129.0us, fastest flow: 49487.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 121793 , JCT is: 56172.0us, fastest flow: 49482.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 2402 , JCT is: 49546.0us, fastest flow: 49484.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 2064 , JCT is: 49544.0us, fastest flow: 49480.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 2047 , JCT is: 49568.0us, fastest flow: 49479.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 1768 , JCT is: 49583.0us, fastest flow: 49482.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 3969 , JCT is: 58025.0us, fastest flow: 40669.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 585 , JCT is: 58265.0us, fastest flow: 40886.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 7698 , JCT is: 63370.0us, fastest flow: 41542.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 652 , JCT is: 63776.0us, fastest flow: 41514.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 26887 , JCT is: 51844.0us, fastest flow: 49492.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 24716 , JCT is: 52143.0us, fastest flow: 48436.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 80031 , JCT is: 56657.0us, fastest flow: 45639.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 63324 , JCT is: 57419.0us, fastest flow: 43960.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 30602 , JCT is: 51365.0us, fastest flow: 49490.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 38789 , JCT is: 51829.0us, fastest flow: 49488.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 122853 , JCT is: 56129.0us, fastest flow: 49487.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 121793 , JCT is: 56172.0us, fastest flow: 49482.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 2402 , JCT is: 49546.0us, fastest flow: 49484.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 2064 , JCT is: 49544.0us, fastest flow: 49480.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 2047 , JCT is: 49568.0us, fastest flow: 49479.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 1768 , JCT is: 49583.0us, fastest flow: 49482.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 3969 , JCT is: 58025.0us, fastest flow: 40669.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 585 , JCT is: 58265.0us, fastest flow: 40886.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 7698 , JCT is: 63370.0us, fastest flow: 41542.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 652 , JCT is: 63776.0us, fastest flow: 41514.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 26887 , JCT is: 51844.0us, fastest flow: 49492.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 24716 , JCT is: 52143.0us, fastest flow: 48436.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 80031 , JCT is: 56657.0us, fastest flow: 45639.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 63324 , JCT is: 57419.0us, fastest flow: 43960.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 30602 , JCT is: 51365.0us, fastest flow: 49490.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 38789 , JCT is: 51829.0us, fastest flow: 49488.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 122853 , JCT is: 56129.0us, fastest flow: 49487.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 121793 , JCT is: 56172.0us, fastest flow: 49482.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 2402 , JCT is: 49546.0us, fastest flow: 49484.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 2064 , JCT is: 49544.0us, fastest flow: 49480.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 2047 , JCT is: 49568.0us, fastest flow: 49479.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 1768 , JCT is: 49583.0us, fastest flow: 49482.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 3969 , JCT is: 58025.0us, fastest flow: 40669.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 585 , JCT is: 58265.0us, fastest flow: 40886.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 7698 , JCT is: 63370.0us, fastest flow: 41542.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 652 , JCT is: 63776.0us, fastest flow: 41514.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 26887 , JCT is: 51844.0us, fastest flow: 49492.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 24716 , JCT is: 52143.0us, fastest flow: 48436.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 80031 , JCT is: 56657.0us, fastest flow: 45639.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 63324 , JCT is: 57419.0us, fastest flow: 43960.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 30602 , JCT is: 51365.0us, fastest flow: 49490.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 38789 , JCT is: 51829.0us, fastest flow: 49488.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 122853 , JCT is: 56129.0us, fastest flow: 49487.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 121793 , JCT is: 56172.0us, fastest flow: 49482.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 2402 , JCT is: 49546.0us, fastest flow: 49484.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 2064 , JCT is: 49544.0us, fastest flow: 49480.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 2047 , JCT is: 49568.0us, fastest flow: 49479.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 1768 , JCT is: 49583.0us, fastest flow: 49482.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 3969 , JCT is: 58025.0us, fastest flow: 40669.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 585 , JCT is: 58265.0us, fastest flow: 40886.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 7698 , JCT is: 63370.0us, fastest flow: 41542.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 652 , JCT is: 63776.0us, fastest flow: 41514.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 26887 , JCT is: 51844.0us, fastest flow: 49492.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 24716 , JCT is: 52143.0us, fastest flow: 48436.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 80031 , JCT is: 56657.0us, fastest flow: 45639.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 63324 , JCT is: 57419.0us, fastest flow: 43960.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 30602 , JCT is: 51365.0us, fastest flow: 49490.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 38789 , JCT is: 51829.0us, fastest flow: 49488.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 122853 , JCT is: 56129.0us, fastest flow: 49487.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 121793 , JCT is: 56172.0us, fastest flow: 49482.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 2402 , JCT is: 49546.0us, fastest flow: 49484.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 2064 , JCT is: 49544.0us, fastest flow: 49480.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 2047 , JCT is: 49568.0us, fastest flow: 49479.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 1768 , JCT is: 49583.0us, fastest flow: 49482.0us 

The shortest jct : 49544.0us, with qsize = 40, cwnd = 60, ar_sticky_delta = 6 ,pcf_thresholds = 3 4 ,spine_pfc_threshold = 3 4
The slowest jct : 63776.0us, with qsize = 60, cwnd = 30 , ar_sticky_delta = 6,pcf_thresholds = 3 4 ,spine_pfc_threshold = 3 4

------------------------------------ Thw above results are for the following base command ----------------------
./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000
----------------------------------------------------------------------------------------------------------------
