protocol is ndp, connection matric is connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm , number of nodes is 128 , numbe of connections is 96
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed703.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q = 30 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 652 , JCT is: 56589.0us, fastest flow: 41410.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 187 , JCT is: 56633.0us, fastest flow: 41412.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 138 , JCT is: 61537.0us, fastest flow: 40776.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 2 , JCT is: 61719.0us, fastest flow: 40690.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 1221 , JCT is: 49556.0us, fastest flow: 49491.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 721 , JCT is: 49559.0us, fastest flow: 49491.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 21896 , JCT is: 53375.0us, fastest flow: 47265.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 12198 , JCT is: 53973.0us, fastest flow: 45354.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 43197 , JCT is: 53576.0us, fastest flow: 49492.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 43222 , JCT is: 53666.0us, fastest flow: 49490.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 82834 , JCT is: 57504.0us, fastest flow: 49487.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 82838 , JCT is: 57865.0us, fastest flow: 49485.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 19381 , JCT is: 50495.0us, fastest flow: 49486.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 13105 , JCT is: 50841.0us, fastest flow: 49489.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 64148 , JCT is: 54355.0us, fastest flow: 49484.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 58302 , JCT is: 54151.0us, fastest flow: 49478.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 652 , JCT is: 56589.0us, fastest flow: 41410.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 187 , JCT is: 56633.0us, fastest flow: 41412.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 138 , JCT is: 61537.0us, fastest flow: 40776.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 2 , JCT is: 61719.0us, fastest flow: 40690.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 1221 , JCT is: 49556.0us, fastest flow: 49491.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 721 , JCT is: 49559.0us, fastest flow: 49491.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 21896 , JCT is: 53375.0us, fastest flow: 47265.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 12198 , JCT is: 53973.0us, fastest flow: 45354.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 43197 , JCT is: 53576.0us, fastest flow: 49492.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 43222 , JCT is: 53666.0us, fastest flow: 49490.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 82834 , JCT is: 57504.0us, fastest flow: 49487.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 82838 , JCT is: 57865.0us, fastest flow: 49485.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 19381 , JCT is: 50495.0us, fastest flow: 49486.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 13105 , JCT is: 50841.0us, fastest flow: 49489.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 64148 , JCT is: 54355.0us, fastest flow: 49484.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 58302 , JCT is: 54151.0us, fastest flow: 49478.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 652 , JCT is: 56589.0us, fastest flow: 41410.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 187 , JCT is: 56633.0us, fastest flow: 41412.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 138 , JCT is: 61537.0us, fastest flow: 40776.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 2 , JCT is: 61719.0us, fastest flow: 40690.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 1221 , JCT is: 49556.0us, fastest flow: 49491.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 721 , JCT is: 49559.0us, fastest flow: 49491.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 21896 , JCT is: 53375.0us, fastest flow: 47265.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 12198 , JCT is: 53973.0us, fastest flow: 45354.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 43197 , JCT is: 53576.0us, fastest flow: 49492.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 43222 , JCT is: 53666.0us, fastest flow: 49490.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 82834 , JCT is: 57504.0us, fastest flow: 49487.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 82838 , JCT is: 57865.0us, fastest flow: 49485.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 19381 , JCT is: 50495.0us, fastest flow: 49486.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 13105 , JCT is: 50841.0us, fastest flow: 49489.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 64148 , JCT is: 54355.0us, fastest flow: 49484.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 58302 , JCT is: 54151.0us, fastest flow: 49478.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 652 , JCT is: 56589.0us, fastest flow: 41410.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 187 , JCT is: 56633.0us, fastest flow: 41412.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 138 , JCT is: 61537.0us, fastest flow: 40776.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 2 , JCT is: 61719.0us, fastest flow: 40690.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 1221 , JCT is: 49556.0us, fastest flow: 49491.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 721 , JCT is: 49559.0us, fastest flow: 49491.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 21896 , JCT is: 53375.0us, fastest flow: 47265.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 12198 , JCT is: 53973.0us, fastest flow: 45354.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 43197 , JCT is: 53576.0us, fastest flow: 49492.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 43222 , JCT is: 53666.0us, fastest flow: 49490.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 82834 , JCT is: 57504.0us, fastest flow: 49487.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 82838 , JCT is: 57865.0us, fastest flow: 49485.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 19381 , JCT is: 50495.0us, fastest flow: 49486.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 13105 , JCT is: 50841.0us, fastest flow: 49489.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 64148 , JCT is: 54355.0us, fastest flow: 49484.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 58302 , JCT is: 54151.0us, fastest flow: 49478.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 652 , JCT is: 56589.0us, fastest flow: 41410.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 187 , JCT is: 56633.0us, fastest flow: 41412.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 138 , JCT is: 61537.0us, fastest flow: 40776.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 2 , JCT is: 61719.0us, fastest flow: 40690.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 1221 , JCT is: 49556.0us, fastest flow: 49491.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 721 , JCT is: 49559.0us, fastest flow: 49491.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 21896 , JCT is: 53375.0us, fastest flow: 47265.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 12198 , JCT is: 53973.0us, fastest flow: 45354.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 43197 , JCT is: 53576.0us, fastest flow: 49492.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 43222 , JCT is: 53666.0us, fastest flow: 49490.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 82834 , JCT is: 57504.0us, fastest flow: 49487.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 82838 , JCT is: 57865.0us, fastest flow: 49485.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 19381 , JCT is: 50495.0us, fastest flow: 49486.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 13105 , JCT is: 50841.0us, fastest flow: 49489.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 64148 , JCT is: 54355.0us, fastest flow: 49484.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 58302 , JCT is: 54151.0us, fastest flow: 49478.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 652 , JCT is: 56589.0us, fastest flow: 41410.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 187 , JCT is: 56633.0us, fastest flow: 41412.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 138 , JCT is: 61537.0us, fastest flow: 40776.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 2 , JCT is: 61719.0us, fastest flow: 40690.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 1221 , JCT is: 49556.0us, fastest flow: 49491.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 721 , JCT is: 49559.0us, fastest flow: 49491.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 21896 , JCT is: 53375.0us, fastest flow: 47265.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 12198 , JCT is: 53973.0us, fastest flow: 45354.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 43197 , JCT is: 53576.0us, fastest flow: 49492.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 43222 , JCT is: 53666.0us, fastest flow: 49490.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 82834 , JCT is: 57504.0us, fastest flow: 49487.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 82838 , JCT is: 57865.0us, fastest flow: 49485.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 19381 , JCT is: 50495.0us, fastest flow: 49486.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 13105 , JCT is: 50841.0us, fastest flow: 49489.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 64148 , JCT is: 54355.0us, fastest flow: 49484.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 58302 , JCT is: 54151.0us, fastest flow: 49478.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 652 , JCT is: 56589.0us, fastest flow: 41410.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 187 , JCT is: 56633.0us, fastest flow: 41412.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 138 , JCT is: 61537.0us, fastest flow: 40776.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 2 , JCT is: 61719.0us, fastest flow: 40690.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 1221 , JCT is: 49556.0us, fastest flow: 49491.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 721 , JCT is: 49559.0us, fastest flow: 49491.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 21896 , JCT is: 53375.0us, fastest flow: 47265.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 12198 , JCT is: 53973.0us, fastest flow: 45354.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 43197 , JCT is: 53576.0us, fastest flow: 49492.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 43222 , JCT is: 53666.0us, fastest flow: 49490.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 82834 , JCT is: 57504.0us, fastest flow: 49487.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 82838 , JCT is: 57865.0us, fastest flow: 49485.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 19381 , JCT is: 50495.0us, fastest flow: 49486.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 13105 , JCT is: 50841.0us, fastest flow: 49489.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 64148 , JCT is: 54355.0us, fastest flow: 49484.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 58302 , JCT is: 54151.0us, fastest flow: 49478.0us 

The shortest jct : 49556.0us, with qsize = 30, cwnd = 40, ar_sticky_delta = 6 ,pcf_thresholds = 3 4 ,spine_pfc_threshold = 3 4
The slowest jct : 61719.0us, with qsize = 60, cwnd = 30 , ar_sticky_delta = 6,pcf_thresholds = 3 4 ,spine_pfc_threshold = 3 4

------------------------------------ Thw above results are for the following base command ----------------------
./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000
----------------------------------------------------------------------------------------------------------------
