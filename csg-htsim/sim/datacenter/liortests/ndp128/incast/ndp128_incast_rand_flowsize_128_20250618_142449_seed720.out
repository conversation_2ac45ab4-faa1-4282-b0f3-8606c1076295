Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 30, max_finished_at: 55835.0, min_finished_at: 39884.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 40, max_finished_at: 55801.0, min_finished_at: 39888.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 50, max_finished_at: 62238.0, min_finished_at: 38680.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 60, max_finished_at: 62281.0, min_finished_at: 38743.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 30, max_finished_at: 49574.0, min_finished_at: 49492.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 40, max_finished_at: 49638.0, min_finished_at: 49465.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 50, max_finished_at: 49655.0, min_finished_at: 49303.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 60, max_finished_at: 49817.0, min_finished_at: 49051.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 30, max_finished_at: 51854.0, min_finished_at: 49487.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 40, max_finished_at: 51957.0, min_finished_at: 49485.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 50, max_finished_at: 54988.0, min_finished_at: 49485.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 60, max_finished_at: 54917.0, min_finished_at: 49484.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 30, max_finished_at: 50941.0, min_finished_at: 49484.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 40, max_finished_at: 51841.0, min_finished_at: 49480.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 50, max_finished_at: 54762.0, min_finished_at: 49481.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
For q: 60, max_finished_at: 54929.0, min_finished_at: 49480.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 3, spine_pfc_threshold: 3, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 30, max_finished_at: 55835.0, min_finished_at: 39884.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 40, max_finished_at: 55801.0, min_finished_at: 39888.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 50, max_finished_at: 62238.0, min_finished_at: 38680.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 60, max_finished_at: 62281.0, min_finished_at: 38743.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 30, max_finished_at: 49574.0, min_finished_at: 49492.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 40, max_finished_at: 49638.0, min_finished_at: 49465.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 50, max_finished_at: 49655.0, min_finished_at: 49303.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 60, max_finished_at: 49817.0, min_finished_at: 49051.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 30, max_finished_at: 51854.0, min_finished_at: 49487.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 40, max_finished_at: 51957.0, min_finished_at: 49485.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 50, max_finished_at: 54988.0, min_finished_at: 49485.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 60, max_finished_at: 54917.0, min_finished_at: 49484.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 30, max_finished_at: 50941.0, min_finished_at: 49484.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 40, max_finished_at: 51841.0, min_finished_at: 49480.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 50, max_finished_at: 54762.0, min_finished_at: 49481.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
For q: 60, max_finished_at: 54929.0, min_finished_at: 49480.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 4, spine_pfc_threshold: 4, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 30, max_finished_at: 55835.0, min_finished_at: 39884.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 40, max_finished_at: 55801.0, min_finished_at: 39888.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 50, max_finished_at: 62238.0, min_finished_at: 38680.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 60, max_finished_at: 62281.0, min_finished_at: 38743.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 30, max_finished_at: 49574.0, min_finished_at: 49492.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 40, max_finished_at: 49638.0, min_finished_at: 49465.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 50, max_finished_at: 49655.0, min_finished_at: 49303.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 60, max_finished_at: 49817.0, min_finished_at: 49051.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 30, max_finished_at: 51854.0, min_finished_at: 49487.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 40, max_finished_at: 51957.0, min_finished_at: 49485.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 50, max_finished_at: 54988.0, min_finished_at: 49485.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 60, max_finished_at: 54917.0, min_finished_at: 49484.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 30, max_finished_at: 50941.0, min_finished_at: 49484.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 40, max_finished_at: 51841.0, min_finished_at: 49480.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 50, max_finished_at: 54762.0, min_finished_at: 49481.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 60, max_finished_at: 54929.0, min_finished_at: 49480.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 30, max_finished_at: 55835.0, min_finished_at: 39884.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 40, max_finished_at: 55801.0, min_finished_at: 39888.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 50, max_finished_at: 62238.0, min_finished_at: 38680.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 60, max_finished_at: 62281.0, min_finished_at: 38743.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 30, max_finished_at: 49574.0, min_finished_at: 49492.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 40, max_finished_at: 49638.0, min_finished_at: 49465.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 50, max_finished_at: 49655.0, min_finished_at: 49303.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 60, max_finished_at: 49817.0, min_finished_at: 49051.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 30, max_finished_at: 51854.0, min_finished_at: 49487.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 40, max_finished_at: 51957.0, min_finished_at: 49485.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 50, max_finished_at: 54988.0, min_finished_at: 49485.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 60, max_finished_at: 54917.0, min_finished_at: 49484.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 30, max_finished_at: 50941.0, min_finished_at: 49484.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 40, max_finished_at: 51841.0, min_finished_at: 49480.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 50, max_finished_at: 54762.0, min_finished_at: 49481.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 60, max_finished_at: 54929.0, min_finished_at: 49480.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 30, max_finished_at: 55835.0, min_finished_at: 39884.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 40, max_finished_at: 55801.0, min_finished_at: 39888.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 50, max_finished_at: 62238.0, min_finished_at: 38680.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 60, max_finished_at: 62281.0, min_finished_at: 38743.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 30, max_finished_at: 49574.0, min_finished_at: 49492.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 40, max_finished_at: 49638.0, min_finished_at: 49465.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 50, max_finished_at: 49655.0, min_finished_at: 49303.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 60, max_finished_at: 49817.0, min_finished_at: 49051.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 30, max_finished_at: 51854.0, min_finished_at: 49487.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 40, max_finished_at: 51957.0, min_finished_at: 49485.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 50, max_finished_at: 54988.0, min_finished_at: 49485.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 60, max_finished_at: 54917.0, min_finished_at: 49484.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 30, max_finished_at: 50941.0, min_finished_at: 49484.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 40, max_finished_at: 51841.0, min_finished_at: 49480.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 50, max_finished_at: 54762.0, min_finished_at: 49481.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 60, max_finished_at: 54929.0, min_finished_at: 49480.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 30, max_finished_at: 55835.0, min_finished_at: 39884.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 40, max_finished_at: 55801.0, min_finished_at: 39888.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 50, max_finished_at: 62238.0, min_finished_at: 38680.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 60, max_finished_at: 62281.0, min_finished_at: 38743.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 30, max_finished_at: 49574.0, min_finished_at: 49492.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 40, max_finished_at: 49638.0, min_finished_at: 49465.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 50, max_finished_at: 49655.0, min_finished_at: 49303.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 60, max_finished_at: 49817.0, min_finished_at: 49051.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 30, max_finished_at: 51854.0, min_finished_at: 49487.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 40, max_finished_at: 51957.0, min_finished_at: 49485.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 50, max_finished_at: 54988.0, min_finished_at: 49485.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 60, max_finished_at: 54917.0, min_finished_at: 49484.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 30, max_finished_at: 50941.0, min_finished_at: 49484.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 40, max_finished_at: 51841.0, min_finished_at: 49480.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 50, max_finished_at: 54762.0, min_finished_at: 49481.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 60, max_finished_at: 54929.0, min_finished_at: 49480.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 30, max_finished_at: 55835.0, min_finished_at: 39884.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 40, max_finished_at: 55801.0, min_finished_at: 39888.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 50, max_finished_at: 62238.0, min_finished_at: 38680.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 60, max_finished_at: 62281.0, min_finished_at: 38743.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 30, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 30, max_finished_at: 49574.0, min_finished_at: 49492.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 40, max_finished_at: 49638.0, min_finished_at: 49465.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 50, max_finished_at: 49655.0, min_finished_at: 49303.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 60, max_finished_at: 49817.0, min_finished_at: 49051.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 40, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 30, max_finished_at: 51854.0, min_finished_at: 49487.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 40, max_finished_at: 51957.0, min_finished_at: 49485.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 50, max_finished_at: 54988.0, min_finished_at: 49485.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 60, max_finished_at: 54917.0, min_finished_at: 49484.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 50, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 30, max_finished_at: 50941.0, min_finished_at: 49484.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 40, max_finished_at: 51841.0, min_finished_at: 49480.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 50, max_finished_at: 54762.0, min_finished_at: 49481.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 60, totalbytes: 19200096000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed720.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 60, max_finished_at: 54929.0, min_finished_at: 49480.0, dropped_count: 0, finished_count: 96, ar_sticky_delta: 6, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 60, totalbytes: 19200096000.0
