protocol is ndp, connection matric is connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm , number of nodes is 128 , numbe of connections is 96
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 3 4 -spine_pfc_thresholds 3 4
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 4 5 -spine_pfc_thresholds 4 5
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 30 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 40 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 50 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 60 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 30 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 40 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 30 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 40 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 30 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 40 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 50 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast128/incast_rand_flowsize_incastratio_3_seed706.cm -q 60 -cwnd 60 -ar_sticky_delta 6 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q = 30 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 46287 , JCT is: 62058.0us, fastest flow: 41196.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 33836 , JCT is: 63409.0us, fastest flow: 41287.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 42872 , JCT is: 68880.0us, fastest flow: 41042.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 26119 , JCT is: 69736.0us, fastest flow: 41062.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 140351 , JCT is: 56465.0us, fastest flow: 49494.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 127102 , JCT is: 56829.0us, fastest flow: 48752.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 177120 , JCT is: 60159.0us, fastest flow: 47771.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 133211 , JCT is: 60952.0us, fastest flow: 48004.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 228755 , JCT is: 65583.0us, fastest flow: 49489.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 191408 , JCT is: 63582.0us, fastest flow: 49487.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 288145 , JCT is: 68726.0us, fastest flow: 49484.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 285034 , JCT is: 69658.0us, fastest flow: 49482.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 240538 , JCT is: 65756.0us, fastest flow: 49481.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 229773 , JCT is: 64748.0us, fastest flow: 49480.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 340761 , JCT is: 72275.0us, fastest flow: 49483.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 3 4 spine_pfc_thresholds 3 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 334123 , JCT is: 72493.0us, fastest flow: 49481.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 46287 , JCT is: 62058.0us, fastest flow: 41196.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 33836 , JCT is: 63409.0us, fastest flow: 41287.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 42872 , JCT is: 68880.0us, fastest flow: 41042.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 26119 , JCT is: 69736.0us, fastest flow: 41062.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 140351 , JCT is: 56465.0us, fastest flow: 49494.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 127102 , JCT is: 56829.0us, fastest flow: 48752.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 177120 , JCT is: 60159.0us, fastest flow: 47771.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 133211 , JCT is: 60952.0us, fastest flow: 48004.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 228755 , JCT is: 65583.0us, fastest flow: 49489.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 191408 , JCT is: 63582.0us, fastest flow: 49487.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 288145 , JCT is: 68726.0us, fastest flow: 49484.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 285034 , JCT is: 69658.0us, fastest flow: 49482.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 240538 , JCT is: 65756.0us, fastest flow: 49481.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 229773 , JCT is: 64748.0us, fastest flow: 49480.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 340761 , JCT is: 72275.0us, fastest flow: 49483.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 4 5 spine_pfc_thresholds 4 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 334123 , JCT is: 72493.0us, fastest flow: 49481.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 46287 , JCT is: 62058.0us, fastest flow: 41196.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 33836 , JCT is: 63409.0us, fastest flow: 41287.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 42872 , JCT is: 68880.0us, fastest flow: 41042.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 26119 , JCT is: 69736.0us, fastest flow: 41062.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 140351 , JCT is: 56465.0us, fastest flow: 49494.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 127102 , JCT is: 56829.0us, fastest flow: 48752.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 177120 , JCT is: 60159.0us, fastest flow: 47771.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 133211 , JCT is: 60952.0us, fastest flow: 48004.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 228755 , JCT is: 65583.0us, fastest flow: 49489.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 191408 , JCT is: 63582.0us, fastest flow: 49487.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 288145 , JCT is: 68726.0us, fastest flow: 49484.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 285034 , JCT is: 69658.0us, fastest flow: 49482.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 240538 , JCT is: 65756.0us, fastest flow: 49481.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 229773 , JCT is: 64748.0us, fastest flow: 49480.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 340761 , JCT is: 72275.0us, fastest flow: 49483.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 5 6 spine_pfc_thresholds 5 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 334123 , JCT is: 72493.0us, fastest flow: 49481.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 46287 , JCT is: 62058.0us, fastest flow: 41196.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 33836 , JCT is: 63409.0us, fastest flow: 41287.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 42872 , JCT is: 68880.0us, fastest flow: 41042.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 26119 , JCT is: 69736.0us, fastest flow: 41062.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 140351 , JCT is: 56465.0us, fastest flow: 49494.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 127102 , JCT is: 56829.0us, fastest flow: 48752.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 177120 , JCT is: 60159.0us, fastest flow: 47771.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 133211 , JCT is: 60952.0us, fastest flow: 48004.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 228755 , JCT is: 65583.0us, fastest flow: 49489.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 191408 , JCT is: 63582.0us, fastest flow: 49487.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 288145 , JCT is: 68726.0us, fastest flow: 49484.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 285034 , JCT is: 69658.0us, fastest flow: 49482.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 240538 , JCT is: 65756.0us, fastest flow: 49481.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 229773 , JCT is: 64748.0us, fastest flow: 49480.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 340761 , JCT is: 72275.0us, fastest flow: 49483.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 6 7 spine_pfc_thresholds 6 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 334123 , JCT is: 72493.0us, fastest flow: 49481.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 46287 , JCT is: 62058.0us, fastest flow: 41196.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 33836 , JCT is: 63409.0us, fastest flow: 41287.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 42872 , JCT is: 68880.0us, fastest flow: 41042.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 26119 , JCT is: 69736.0us, fastest flow: 41062.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 140351 , JCT is: 56465.0us, fastest flow: 49494.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 127102 , JCT is: 56829.0us, fastest flow: 48752.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 177120 , JCT is: 60159.0us, fastest flow: 47771.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 133211 , JCT is: 60952.0us, fastest flow: 48004.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 228755 , JCT is: 65583.0us, fastest flow: 49489.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 191408 , JCT is: 63582.0us, fastest flow: 49487.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 288145 , JCT is: 68726.0us, fastest flow: 49484.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 285034 , JCT is: 69658.0us, fastest flow: 49482.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 240538 , JCT is: 65756.0us, fastest flow: 49481.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 229773 , JCT is: 64748.0us, fastest flow: 49480.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 340761 , JCT is: 72275.0us, fastest flow: 49483.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 334123 , JCT is: 72493.0us, fastest flow: 49481.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 46287 , JCT is: 62058.0us, fastest flow: 41196.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 33836 , JCT is: 63409.0us, fastest flow: 41287.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 42872 , JCT is: 68880.0us, fastest flow: 41042.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 26119 , JCT is: 69736.0us, fastest flow: 41062.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 140351 , JCT is: 56465.0us, fastest flow: 49494.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 127102 , JCT is: 56829.0us, fastest flow: 48752.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 177120 , JCT is: 60159.0us, fastest flow: 47771.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 133211 , JCT is: 60952.0us, fastest flow: 48004.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 228755 , JCT is: 65583.0us, fastest flow: 49489.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 191408 , JCT is: 63582.0us, fastest flow: 49487.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 288145 , JCT is: 68726.0us, fastest flow: 49484.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 285034 , JCT is: 69658.0us, fastest flow: 49482.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 240538 , JCT is: 65756.0us, fastest flow: 49481.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 229773 , JCT is: 64748.0us, fastest flow: 49480.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 340761 , JCT is: 72275.0us, fastest flow: 49483.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 334123 , JCT is: 72493.0us, fastest flow: 49481.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 46287 , JCT is: 62058.0us, fastest flow: 41196.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 33836 , JCT is: 63409.0us, fastest flow: 41287.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 42872 , JCT is: 68880.0us, fastest flow: 41042.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 30. 96 flows completed. Number of packets 6400032 ,Number of RTX 26119 , JCT is: 69736.0us, fastest flow: 41062.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 140351 , JCT is: 56465.0us, fastest flow: 49494.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 127102 , JCT is: 56829.0us, fastest flow: 48752.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 177120 , JCT is: 60159.0us, fastest flow: 47771.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 40. 96 flows completed. Number of packets 6400032 ,Number of RTX 133211 , JCT is: 60952.0us, fastest flow: 48004.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 228755 , JCT is: 65583.0us, fastest flow: 49489.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 191408 , JCT is: 63582.0us, fastest flow: 49487.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 288145 , JCT is: 68726.0us, fastest flow: 49484.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 50. 96 flows completed. Number of packets 6400032 ,Number of RTX 285034 , JCT is: 69658.0us, fastest flow: 49482.0us 
For q = 30 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 240538 , JCT is: 65756.0us, fastest flow: 49481.0us 
For q = 40 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 229773 , JCT is: 64748.0us, fastest flow: 49480.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 340761 , JCT is: 72275.0us, fastest flow: 49483.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 60. 96 flows completed. Number of packets 6400032 ,Number of RTX 334123 , JCT is: 72493.0us, fastest flow: 49481.0us 

The shortest jct : 56465.0us, with qsize = 30, cwnd = 40, ar_sticky_delta = 6 ,pcf_thresholds = 3 4 ,spine_pfc_threshold = 3 4
The slowest jct : 72493.0us, with qsize = 60, cwnd = 60 , ar_sticky_delta = 6,pcf_thresholds = 3 4 ,spine_pfc_threshold = 3 4

------------------------------------ Thw above results are for the following base command ----------------------
./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 96 -strat ecmp_ar  -end 50000000 -mtu 3000
----------------------------------------------------------------------------------------------------------------
