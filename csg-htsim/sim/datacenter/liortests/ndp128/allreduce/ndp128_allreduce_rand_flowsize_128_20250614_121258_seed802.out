Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed802.cm -q 100 -cwnd 20 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 100, max_finished_at: 11943.0, min_finished_at: 224.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 20, totalbytes: 3841920000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed802.cm -q 110 -cwnd 20 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 110, max_finished_at: 11952.0, min_finished_at: 224.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 20, totalbytes: 3841920000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed802.cm -q 100 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 100, max_finished_at: 8179.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 30, totalbytes: 3841920000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed802.cm -q 110 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 5 6 -spine_pfc_thresholds 5 6
For q: 110, max_finished_at: 8209.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 5, spine_pfc_threshold: 5, cwnd: 30, totalbytes: 3841920000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed802.cm -q 100 -cwnd 20 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 100, max_finished_at: 11943.0, min_finished_at: 224.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 20, totalbytes: 3841920000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed802.cm -q 110 -cwnd 20 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 110, max_finished_at: 11952.0, min_finished_at: 224.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 20, totalbytes: 3841920000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed802.cm -q 100 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 100, max_finished_at: 8179.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 30, totalbytes: 3841920000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed802.cm -q 110 -cwnd 30 -ar_sticky_delta 6 -pfc_thresholds 6 7 -spine_pfc_thresholds 6 7
For q: 110, max_finished_at: 8209.0, min_finished_at: 170.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 6, spine_pfc_threshold: 6, cwnd: 30, totalbytes: 3841920000.0
