protocol is ndp, connection matric is connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed715.cm , number of nodes is 128 , numbe of connections is 1920
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed715.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed715.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed715.cm -q 70 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed715.cm -q 80 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed715.cm -q 90 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed715.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed715.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed715.cm -q 70 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed715.cm -q 80 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed715.cm -q 90 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed715.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed715.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed715.cm -q 70 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed715.cm -q 80 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed715.cm -q 90 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed715.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed715.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed715.cm -q 70 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed715.cm -q 80 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed715.cm -q 90 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q = 50 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 40. 1920 flows completed. Number of packets 1280640 ,Number of RTX 1 , JCT is: 6011.0us, fastest flow: 171.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 40. 1920 flows completed. Number of packets 1280640 ,Number of RTX 0 , JCT is: 6015.0us, fastest flow: 171.0us 
For q = 70 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 40. 1920 flows completed. Number of packets 1280640 ,Number of RTX 0 , JCT is: 6031.0us, fastest flow: 171.0us 
For q = 80 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 40. 1920 flows completed. Number of packets 1280640 ,Number of RTX 0 , JCT is: 6027.0us, fastest flow: 171.0us 
For q = 90 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 40. 1920 flows completed. Number of packets 1280640 ,Number of RTX 0 , JCT is: 6067.0us, fastest flow: 171.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 50. 1920 flows completed. Number of packets 1280640 ,Number of RTX 91 , JCT is: 5037.0us, fastest flow: 172.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 50. 1920 flows completed. Number of packets 1280640 ,Number of RTX 115 , JCT is: 5036.0us, fastest flow: 172.0us 
For q = 70 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 50. 1920 flows completed. Number of packets 1280640 ,Number of RTX 242 , JCT is: 5103.0us, fastest flow: 172.0us 
For q = 80 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 50. 1920 flows completed. Number of packets 1280640 ,Number of RTX 111 , JCT is: 5108.0us, fastest flow: 172.0us 
For q = 90 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 50. 1920 flows completed. Number of packets 1280640 ,Number of RTX 776 , JCT is: 5239.0us, fastest flow: 172.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 40. 1920 flows completed. Number of packets 1280640 ,Number of RTX 1 , JCT is: 6011.0us, fastest flow: 171.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 40. 1920 flows completed. Number of packets 1280640 ,Number of RTX 0 , JCT is: 6015.0us, fastest flow: 171.0us 
For q = 70 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 40. 1920 flows completed. Number of packets 1280640 ,Number of RTX 0 , JCT is: 6031.0us, fastest flow: 171.0us 
For q = 80 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 40. 1920 flows completed. Number of packets 1280640 ,Number of RTX 0 , JCT is: 6027.0us, fastest flow: 171.0us 
For q = 90 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 40. 1920 flows completed. Number of packets 1280640 ,Number of RTX 0 , JCT is: 6067.0us, fastest flow: 171.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 50. 1920 flows completed. Number of packets 1280640 ,Number of RTX 91 , JCT is: 5037.0us, fastest flow: 172.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 50. 1920 flows completed. Number of packets 1280640 ,Number of RTX 115 , JCT is: 5036.0us, fastest flow: 172.0us 
For q = 70 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 50. 1920 flows completed. Number of packets 1280640 ,Number of RTX 242 , JCT is: 5103.0us, fastest flow: 172.0us 
For q = 80 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 50. 1920 flows completed. Number of packets 1280640 ,Number of RTX 111 , JCT is: 5108.0us, fastest flow: 172.0us 
For q = 90 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 50. 1920 flows completed. Number of packets 1280640 ,Number of RTX 776 , JCT is: 5239.0us, fastest flow: 172.0us 

The shortest jct : 5036.0us, with qsize = 60, cwnd = 50, ar_sticky_delta = 6 ,pcf_thresholds = 7 8 ,spine_pfc_threshold = 7 8
The slowest jct : 6067.0us, with qsize = 90, cwnd = 40 , ar_sticky_delta = 6,pcf_thresholds = 7 8 ,spine_pfc_threshold = 7 8

------------------------------------ Thw above results are for the following base command ----------------------
./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000
----------------------------------------------------------------------------------------------------------------
