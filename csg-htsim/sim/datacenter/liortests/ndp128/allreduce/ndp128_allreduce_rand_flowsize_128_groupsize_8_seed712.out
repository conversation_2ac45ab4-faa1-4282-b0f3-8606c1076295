protocol is ndp, connection matric is connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed712.cm , number of nodes is 128 , numbe of connections is 1920
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed712.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed712.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed712.cm -q 70 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed712.cm -q 80 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed712.cm -q 90 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed712.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed712.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed712.cm -q 70 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed712.cm -q 80 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed712.cm -q 90 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed712.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed712.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed712.cm -q 70 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed712.cm -q 80 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed712.cm -q 90 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed712.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed712.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed712.cm -q 70 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed712.cm -q 80 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed712.cm -q 90 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q = 50 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 40. 1920 flows completed. Number of packets 1280640 ,Number of RTX 1 , JCT is: 5965.0us, fastest flow: 171.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 40. 1920 flows completed. Number of packets 1280640 ,Number of RTX 0 , JCT is: 5959.0us, fastest flow: 171.0us 
For q = 70 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 40. 1920 flows completed. Number of packets 1280640 ,Number of RTX 0 , JCT is: 5985.0us, fastest flow: 171.0us 
For q = 80 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 40. 1920 flows completed. Number of packets 1280640 ,Number of RTX 0 , JCT is: 5998.0us, fastest flow: 171.0us 
For q = 90 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 40. 1920 flows completed. Number of packets 1280640 ,Number of RTX 0 , JCT is: 6013.0us, fastest flow: 171.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 50. 1920 flows completed. Number of packets 1280640 ,Number of RTX 80 , JCT is: 4977.0us, fastest flow: 172.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 50. 1920 flows completed. Number of packets 1280640 ,Number of RTX 36 , JCT is: 4982.0us, fastest flow: 172.0us 
For q = 70 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 50. 1920 flows completed. Number of packets 1280640 ,Number of RTX 286 , JCT is: 5053.0us, fastest flow: 172.0us 
For q = 80 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 50. 1920 flows completed. Number of packets 1280640 ,Number of RTX 185 , JCT is: 5081.0us, fastest flow: 172.0us 
For q = 90 ar_sticky_delta = 6 pfc_thresholds 7 8 spine_pfc_thresholds 7 and cwnd 50. 1920 flows completed. Number of packets 1280640 ,Number of RTX 135 , JCT is: 5124.0us, fastest flow: 171.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 40. 1920 flows completed. Number of packets 1280640 ,Number of RTX 1 , JCT is: 5965.0us, fastest flow: 171.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 40. 1920 flows completed. Number of packets 1280640 ,Number of RTX 0 , JCT is: 5959.0us, fastest flow: 171.0us 
For q = 70 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 40. 1920 flows completed. Number of packets 1280640 ,Number of RTX 0 , JCT is: 5985.0us, fastest flow: 171.0us 
For q = 80 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 40. 1920 flows completed. Number of packets 1280640 ,Number of RTX 0 , JCT is: 5998.0us, fastest flow: 171.0us 
For q = 90 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 40. 1920 flows completed. Number of packets 1280640 ,Number of RTX 0 , JCT is: 6013.0us, fastest flow: 171.0us 
For q = 50 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 50. 1920 flows completed. Number of packets 1280640 ,Number of RTX 80 , JCT is: 4977.0us, fastest flow: 172.0us 
For q = 60 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 50. 1920 flows completed. Number of packets 1280640 ,Number of RTX 36 , JCT is: 4982.0us, fastest flow: 172.0us 
For q = 70 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 50. 1920 flows completed. Number of packets 1280640 ,Number of RTX 286 , JCT is: 5053.0us, fastest flow: 172.0us 
For q = 80 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 50. 1920 flows completed. Number of packets 1280640 ,Number of RTX 185 , JCT is: 5081.0us, fastest flow: 172.0us 
For q = 90 ar_sticky_delta = 6 pfc_thresholds 8 9 spine_pfc_thresholds 8 and cwnd 50. 1920 flows completed. Number of packets 1280640 ,Number of RTX 135 , JCT is: 5124.0us, fastest flow: 171.0us 

The shortest jct : 4977.0us, with qsize = 50, cwnd = 50, ar_sticky_delta = 6 ,pcf_thresholds = 7 8 ,spine_pfc_threshold = 7 8
The slowest jct : 6013.0us, with qsize = 90, cwnd = 40 , ar_sticky_delta = 6,pcf_thresholds = 7 8 ,spine_pfc_threshold = 7 8

------------------------------------ Thw above results are for the following base command ----------------------
./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000
----------------------------------------------------------------------------------------------------------------
