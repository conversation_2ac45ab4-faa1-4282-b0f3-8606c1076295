Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed714.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 50, max_finished_at: 5968.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 40, totalbytes: 3841920000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed714.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 60, max_finished_at: 5967.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 40, totalbytes: 3841920000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed714.cm -q 70 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 70, max_finished_at: 5999.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 40, totalbytes: 3841920000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed714.cm -q 80 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 80, max_finished_at: 6005.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 40, totalbytes: 3841920000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed714.cm -q 90 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 90, max_finished_at: 6027.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 40, totalbytes: 3841920000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed714.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 50, max_finished_at: 5015.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 50, totalbytes: 3841920000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed714.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 60, max_finished_at: 5017.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 50, totalbytes: 3841920000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed714.cm -q 70 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 70, max_finished_at: 5061.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 50, totalbytes: 3841920000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed714.cm -q 80 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 80, max_finished_at: 5051.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 50, totalbytes: 3841920000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed714.cm -q 90 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 7 8 -spine_pfc_thresholds 7 8
For q: 90, max_finished_at: 5075.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 7, spine_pfc_threshold: 7, cwnd: 50, totalbytes: 3841920000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed714.cm -q 50 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 50, max_finished_at: 5968.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 40, totalbytes: 3841920000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed714.cm -q 60 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 60, max_finished_at: 5967.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 40, totalbytes: 3841920000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed714.cm -q 70 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 70, max_finished_at: 5999.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 40, totalbytes: 3841920000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed714.cm -q 80 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 80, max_finished_at: 6005.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 40, totalbytes: 3841920000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed714.cm -q 90 -cwnd 40 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 90, max_finished_at: 6027.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 40, totalbytes: 3841920000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed714.cm -q 50 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 50, max_finished_at: 5015.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 50, totalbytes: 3841920000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed714.cm -q 60 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 60, max_finished_at: 5017.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 50, totalbytes: 3841920000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed714.cm -q 70 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 70, max_finished_at: 5061.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 50, totalbytes: 3841920000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed714.cm -q 80 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 80, max_finished_at: 5051.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 50, totalbytes: 3841920000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_128.topo -nodes 128 -conns 1920 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce128/allreduce_rand_flowsize_groupsize_8_seed714.cm -q 90 -cwnd 50 -ar_sticky_delta 6 -pfc_thresholds 8 9 -spine_pfc_thresholds 8 9
For q: 90, max_finished_at: 5075.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 1920, ar_sticky_delta: 6, pfc_threshold: 8, spine_pfc_threshold: 8, cwnd: 50, totalbytes: 3841920000.0
