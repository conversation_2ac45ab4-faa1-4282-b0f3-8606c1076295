import re
import sys
import subprocess
import threading
import queue
import time
from collections import defaultdict

# Shared buffer to hold lines read from subprocess
buffer = queue.Queue()

def input_buffer():
    """Reads lines from the given source (pipe or file) and places them into the buffer."""
    try:
        if not sys.stdin.isatty():
            # Input is piped
            while True:
                line = sys.stdin.readline()
                if line:
                    buffer.put(line.strip())
                else:
                    time.sleep(0.1)  # Prevent tight loop if no new data is available
        else:
            # Input is from a file
            file_path = input("Enter the file path: ").strip()
            with open(file_path, "r") as file:
                for line in file:
                    buffer.put(line.strip())
    except KeyboardInterrupt:
        print("Input buffer interrupted.")

def parse_log_line(line):
    """Parses a log line to extract relevant information."""
    # <<< ADDED: Sanity check for line length and unexpected characters
    if len(line.strip()) < 10 or not re.search(r"\btime\b|\bdropped\b", line):
        return None  # Skip malformed or irrelevant lines

    match = re.match(r"\s*at time ([\d.]+)\s+Src (\d+)\s+sent (\d+)\s+Bytes via Flow\s+(\w+)", line)
    droppattern = r'\bdropped\b'
    matchdropped = re.search(droppattern, line)

    if matchdropped:
        print(f"DROPPES")
    if match:
        time_val, source, bytes_sent, flow = match.groups()
        return float(time_val), int(source), int(bytes_sent), flow+"_Src"+source

    return None

def process_interval_groups_for_incremental(grouped_lines, last_non_zero_bytes_sent):
    if not grouped_lines:
        return {}, last_non_zero_bytes_sent

    current_bytes_sent = {}
    for time, _, bytes_sent, flow in grouped_lines:
        current_bytes_sent[flow] = int(bytes_sent)

    result = {}
    for flow, current_bytes in current_bytes_sent.items():
        prev_bytes = last_non_zero_bytes_sent.get(flow, 0)
        result[flow] = {"diff": current_bytes - prev_bytes, "time": grouped_lines[0][0] if grouped_lines else 0}
        if current_bytes > 0:
            last_non_zero_bytes_sent[flow] = current_bytes

    return result, last_non_zero_bytes_sent

def process_buffer(time_interval, timeout_interval):
    grouped_lines = []
    current_interval_start = 0.0
    last_received_time = time.time()
    program_running = True
    all_flows = set()
    last_non_zero_bytes_sent = {}
    flow_bytes_sent = defaultdict(int)

    while program_running:
        try:
            line = buffer.get(timeout=0.5)
            parsed_line = parse_log_line(line)

            if parsed_line:
                time_value = parsed_line[0]
                last_received_time = time.time()
                _, _, _, flow = parsed_line
                all_flows.add(flow)

                while time_value >= current_interval_start + time_interval:
                    if grouped_lines:
                        incremental_data, last_non_zero_bytes_sent = process_interval_groups_for_incremental(grouped_lines, last_non_zero_bytes_sent)
                        print_incremental_data_formatted(incremental_data, current_interval_start, time_interval, all_flows)
                        grouped_lines.clear()
                        for flow, data in incremental_data.items():
                            flow_bytes_sent[flow] += data["diff"]
                    else:
                        print_incremental_data_formatted({}, current_interval_start, time_interval, all_flows)
                    current_interval_start += time_interval

                grouped_lines.append(parsed_line)

        except queue.Empty:
            if time.time() - last_received_time >= timeout_interval:
                if grouped_lines:
                    incremental_data, last_non_zero_bytes_sent = process_interval_groups_for_incremental(grouped_lines, last_non_zero_bytes_sent)
                    print_incremental_data_formatted(incremental_data, current_interval_start, time_interval, all_flows)
                    for flow, data in incremental_data.items():
                        flow_bytes_sent[flow] += data["diff"]
                    grouped_lines.clear()

                print("\nFinal Bytes Sent per Flow:")
                for flow, total_bytes in sorted(flow_bytes_sent.items()):
                    print(f"  Flow: {flow}, Total Bytes Sent: {total_bytes}")

                program_running = False
                print("Input buffer empty. Program completed")

    time.sleep(1)
    return

def print_incremental_data_formatted(incremental_data, start_time, interval, all_flows):
    print(f"Time Interval: {start_time}-{start_time + interval}")
    output_data = defaultdict(lambda: {"diff": 0, "time": start_time})
    for flow, data in incremental_data.items():
        output_data[flow] = data

    for flow in sorted(all_flows):
        if flow in output_data:
            print(f"  Flow: {flow}, Incremental Bytes Sent: {output_data[flow]['diff']}")
        # == 240625 Lior removed  printing flows that did not send any bytes ==
        # else:
        #    print(f"  Flow: {flow}, Incremental Bytes Sent: 0")

def main():
    file_path = "liortests/for_split2timeframs.in"
    if not sys.stdin.isatty():
        process_buffer(time_interval, timeout_interval)
    else:
        file_path = input("Enter the file path: ").strip()
        try:
            with open(file_path, "r") as f:
                for line in f:
                    line = line.strip()
                    if line:
                        buffer.put(line)
            process_buffer(time_interval, timeout_interval)
        except FileNotFoundError:
            print(f"Error: File '{file_path}' not found.")
        except Exception as e:
            print(f"Error reading file: {e}")

if __name__ == "__main__":
    input_thread = threading.Thread(target=input_buffer, daemon=True)
    input_thread.start()
    time_interval = 2
    timeout_interval = 5

    try:
        main()
    except KeyboardInterrupt:
        print("Terminating program...")
