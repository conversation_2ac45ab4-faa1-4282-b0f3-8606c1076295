Running command: ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 15360 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce1024/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 30 -cwnd 30 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 30, max_finished_at: 8366.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 15360, ar_sticky_delta: 13, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 30, totalbytes: 30735360000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 15360 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce1024/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 40 -cwnd 30 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 40, max_finished_at: 8357.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 15360, ar_sticky_delta: 13, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 30, totalbytes: 30735360000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 15360 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce1024/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 50 -cwnd 30 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 50, max_finished_at: 8390.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 15360, ar_sticky_delta: 13, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 30, totalbytes: 30735360000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 15360 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce1024/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 60 -cwnd 30 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 60, max_finished_at: 8390.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 15360, ar_sticky_delta: 13, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 30, totalbytes: 30735360000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 15360 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce1024/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 30 -cwnd 40 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 30, max_finished_at: 6379.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 15360, ar_sticky_delta: 13, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 40, totalbytes: 30735360000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 15360 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce1024/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 40 -cwnd 40 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 40, max_finished_at: 6381.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 15360, ar_sticky_delta: 13, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 40, totalbytes: 30735360000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 15360 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce1024/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 50 -cwnd 40 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 50, max_finished_at: 6402.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 15360, ar_sticky_delta: 13, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 40, totalbytes: 30735360000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 15360 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce1024/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 60 -cwnd 40 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 60, max_finished_at: 6404.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 15360, ar_sticky_delta: 13, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 40, totalbytes: 30735360000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 15360 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce1024/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 30 -cwnd 50 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 30, max_finished_at: 5260.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 15360, ar_sticky_delta: 13, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 50, totalbytes: 30735360000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 15360 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce1024/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 40 -cwnd 50 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 40, max_finished_at: 5262.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 15360, ar_sticky_delta: 13, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 50, totalbytes: 30735360000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 15360 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce1024/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 50 -cwnd 50 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 50, max_finished_at: 5280.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 15360, ar_sticky_delta: 13, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 50, totalbytes: 30735360000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 15360 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce1024/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 60 -cwnd 50 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 60, max_finished_at: 5279.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 15360, ar_sticky_delta: 13, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 50, totalbytes: 30735360000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 15360 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce1024/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 30 -cwnd 60 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 30, max_finished_at: 4519.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 15360, ar_sticky_delta: 13, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 60, totalbytes: 30735360000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 15360 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce1024/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 40 -cwnd 60 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 40, max_finished_at: 4526.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 15360, ar_sticky_delta: 13, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 60, totalbytes: 30735360000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 15360 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce1024/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 50 -cwnd 60 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 50, max_finished_at: 4524.0, min_finished_at: 171.0, dropped_count: 0, finished_count: 15360, ar_sticky_delta: 13, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 60, totalbytes: 30735360000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 15360 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce1024/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 60 -cwnd 60 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 60, max_finished_at: 4526.0, min_finished_at: 172.0, dropped_count: 0, finished_count: 15360, ar_sticky_delta: 13, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 60, totalbytes: 30735360000.0
