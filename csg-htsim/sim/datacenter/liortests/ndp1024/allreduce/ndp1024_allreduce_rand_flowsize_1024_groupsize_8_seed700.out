protocol is ndp, connection matric is connection_matrices/allreduce1024/allreduce_rand_flowsize_groupsize_8_seed700.cm , number of nodes is 1024 , numbe of connections is 15360
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 15360 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce1024/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 30 -cwnd 30 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 15360 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce1024/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 40 -cwnd 30 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 15360 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce1024/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 50 -cwnd 30 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 15360 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce1024/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 60 -cwnd 30 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 15360 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce1024/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 30 -cwnd 40 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 15360 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce1024/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 40 -cwnd 40 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 15360 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce1024/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 50 -cwnd 40 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 15360 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce1024/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 60 -cwnd 40 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 15360 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce1024/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 30 -cwnd 50 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 15360 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce1024/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 40 -cwnd 50 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 15360 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce1024/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 50 -cwnd 50 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 15360 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce1024/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 60 -cwnd 50 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 15360 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce1024/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 30 -cwnd 60 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 15360 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce1024/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 40 -cwnd 60 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 15360 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce1024/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 50 -cwnd 60 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 15360 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/allreduce1024/allreduce_rand_flowsize_groupsize_8_seed700.cm -q 60 -cwnd 60 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q = 30 ar_sticky_delta = 13 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 30. 15360 flows completed. Number of packets 10245120 ,Number of RTX 19 , JCT is: 8366.0us, fastest flow: 171.0us 
For q = 40 ar_sticky_delta = 13 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 30. 15360 flows completed. Number of packets 10245120 ,Number of RTX 0 , JCT is: 8357.0us, fastest flow: 171.0us 
For q = 50 ar_sticky_delta = 13 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 30. 15360 flows completed. Number of packets 10245120 ,Number of RTX 0 , JCT is: 8390.0us, fastest flow: 171.0us 
For q = 60 ar_sticky_delta = 13 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 30. 15360 flows completed. Number of packets 10245120 ,Number of RTX 0 , JCT is: 8390.0us, fastest flow: 171.0us 
For q = 30 ar_sticky_delta = 13 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 40. 15360 flows completed. Number of packets 10245120 ,Number of RTX 160 , JCT is: 6379.0us, fastest flow: 171.0us 
For q = 40 ar_sticky_delta = 13 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 40. 15360 flows completed. Number of packets 10245120 ,Number of RTX 0 , JCT is: 6381.0us, fastest flow: 171.0us 
For q = 50 ar_sticky_delta = 13 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 40. 15360 flows completed. Number of packets 10245120 ,Number of RTX 0 , JCT is: 6402.0us, fastest flow: 171.0us 
For q = 60 ar_sticky_delta = 13 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 40. 15360 flows completed. Number of packets 10245120 ,Number of RTX 0 , JCT is: 6404.0us, fastest flow: 171.0us 
For q = 30 ar_sticky_delta = 13 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 50. 15360 flows completed. Number of packets 10245120 ,Number of RTX 417 , JCT is: 5260.0us, fastest flow: 171.0us 
For q = 40 ar_sticky_delta = 13 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 50. 15360 flows completed. Number of packets 10245120 ,Number of RTX 0 , JCT is: 5262.0us, fastest flow: 171.0us 
For q = 50 ar_sticky_delta = 13 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 50. 15360 flows completed. Number of packets 10245120 ,Number of RTX 0 , JCT is: 5280.0us, fastest flow: 171.0us 
For q = 60 ar_sticky_delta = 13 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 50. 15360 flows completed. Number of packets 10245120 ,Number of RTX 0 , JCT is: 5279.0us, fastest flow: 171.0us 
For q = 30 ar_sticky_delta = 13 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 60. 15360 flows completed. Number of packets 10245120 ,Number of RTX 815 , JCT is: 4519.0us, fastest flow: 171.0us 
For q = 40 ar_sticky_delta = 13 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 60. 15360 flows completed. Number of packets 10245120 ,Number of RTX 0 , JCT is: 4526.0us, fastest flow: 171.0us 
For q = 50 ar_sticky_delta = 13 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 60. 15360 flows completed. Number of packets 10245120 ,Number of RTX 0 , JCT is: 4524.0us, fastest flow: 171.0us 
For q = 60 ar_sticky_delta = 13 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 60. 15360 flows completed. Number of packets 10245120 ,Number of RTX 0 , JCT is: 4526.0us, fastest flow: 172.0us 

The shortest jct : 4519.0us, with qsize = 30, cwnd = 60, ar_sticky_delta = 13 ,pcf_thresholds = 9 10 ,spine_pfc_threshold = 9 10
The slowest jct : 8390.0us, with qsize = 50, cwnd = 30 , ar_sticky_delta = 13,pcf_thresholds = 9 10 ,spine_pfc_threshold = 9 10

------------------------------------ Thw above results are for the following base command ----------------------
./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 15360 -strat ecmp_ar  -end 50000000 -mtu 3000
----------------------------------------------------------------------------------------------------------------
