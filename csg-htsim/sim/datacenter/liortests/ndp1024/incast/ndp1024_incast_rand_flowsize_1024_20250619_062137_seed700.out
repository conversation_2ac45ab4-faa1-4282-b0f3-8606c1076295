Running command: ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 768 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast1024/incast_rand_flowsize_incastratio_3_seed700.cm -q 30 -cwnd 30 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 30, max_finished_at: 578.0, min_finished_at: 397.0, dropped_count: 0, finished_count: 768, ar_sticky_delta: 13, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 30, totalbytes: 1536768000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 768 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast1024/incast_rand_flowsize_incastratio_3_seed700.cm -q 40 -cwnd 30 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 40, max_finished_at: 574.0, min_finished_at: 397.0, dropped_count: 0, finished_count: 768, ar_sticky_delta: 13, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 30, totalbytes: 1536768000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 768 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast1024/incast_rand_flowsize_incastratio_3_seed700.cm -q 50 -cwnd 30 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 50, max_finished_at: 630.0, min_finished_at: 376.0, dropped_count: 0, finished_count: 768, ar_sticky_delta: 13, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 30, totalbytes: 1536768000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 768 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast1024/incast_rand_flowsize_incastratio_3_seed700.cm -q 60 -cwnd 30 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 60, max_finished_at: 628.0, min_finished_at: 330.0, dropped_count: 0, finished_count: 768, ar_sticky_delta: 13, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 30, totalbytes: 1536768000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 768 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast1024/incast_rand_flowsize_incastratio_3_seed700.cm -q 30 -cwnd 40 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 30, max_finished_at: 527.0, min_finished_at: 469.0, dropped_count: 0, finished_count: 768, ar_sticky_delta: 13, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 40, totalbytes: 1536768000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 768 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast1024/incast_rand_flowsize_incastratio_3_seed700.cm -q 40 -cwnd 40 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 40, max_finished_at: 525.0, min_finished_at: 462.0, dropped_count: 0, finished_count: 768, ar_sticky_delta: 13, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 40, totalbytes: 1536768000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 768 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast1024/incast_rand_flowsize_incastratio_3_seed700.cm -q 50 -cwnd 40 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 50, max_finished_at: 544.0, min_finished_at: 460.0, dropped_count: 0, finished_count: 768, ar_sticky_delta: 13, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 40, totalbytes: 1536768000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 768 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast1024/incast_rand_flowsize_incastratio_3_seed700.cm -q 60 -cwnd 40 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 60, max_finished_at: 538.0, min_finished_at: 452.0, dropped_count: 0, finished_count: 768, ar_sticky_delta: 13, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 40, totalbytes: 1536768000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 768 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast1024/incast_rand_flowsize_incastratio_3_seed700.cm -q 30 -cwnd 50 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 30, max_finished_at: 556.0, min_finished_at: 459.0, dropped_count: 0, finished_count: 768, ar_sticky_delta: 13, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 50, totalbytes: 1536768000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 768 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast1024/incast_rand_flowsize_incastratio_3_seed700.cm -q 40 -cwnd 50 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 40, max_finished_at: 553.0, min_finished_at: 458.0, dropped_count: 0, finished_count: 768, ar_sticky_delta: 13, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 50, totalbytes: 1536768000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 768 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast1024/incast_rand_flowsize_incastratio_3_seed700.cm -q 50 -cwnd 50 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 50, max_finished_at: 575.0, min_finished_at: 461.0, dropped_count: 0, finished_count: 768, ar_sticky_delta: 13, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 50, totalbytes: 1536768000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 768 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast1024/incast_rand_flowsize_incastratio_3_seed700.cm -q 60 -cwnd 50 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 60, max_finished_at: 570.0, min_finished_at: 460.0, dropped_count: 0, finished_count: 768, ar_sticky_delta: 13, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 50, totalbytes: 1536768000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 768 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast1024/incast_rand_flowsize_incastratio_3_seed700.cm -q 30 -cwnd 60 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 30, max_finished_at: 523.0, min_finished_at: 459.0, dropped_count: 0, finished_count: 768, ar_sticky_delta: 13, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 60, totalbytes: 1536768000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 768 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast1024/incast_rand_flowsize_incastratio_3_seed700.cm -q 40 -cwnd 60 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 40, max_finished_at: 520.0, min_finished_at: 455.0, dropped_count: 0, finished_count: 768, ar_sticky_delta: 13, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 60, totalbytes: 1536768000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 768 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast1024/incast_rand_flowsize_incastratio_3_seed700.cm -q 50 -cwnd 60 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 50, max_finished_at: 519.0, min_finished_at: 461.0, dropped_count: 0, finished_count: 768, ar_sticky_delta: 13, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 60, totalbytes: 1536768000.0
Running command: ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 768 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast1024/incast_rand_flowsize_incastratio_3_seed700.cm -q 60 -cwnd 60 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q: 60, max_finished_at: 517.0, min_finished_at: 461.0, dropped_count: 0, finished_count: 768, ar_sticky_delta: 13, pfc_threshold: 9, spine_pfc_threshold: 9, cwnd: 60, totalbytes: 1536768000.0
