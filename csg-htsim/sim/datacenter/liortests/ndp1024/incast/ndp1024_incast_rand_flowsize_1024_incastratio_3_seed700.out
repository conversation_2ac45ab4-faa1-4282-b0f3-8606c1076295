protocol is ndp, connection matric is connection_matrices/incast1024/incast_rand_flowsize_incastratio_3_seed700.cm , number of nodes is 1024 , numbe of connections is 768
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 768 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast1024/incast_rand_flowsize_incastratio_3_seed700.cm -q 30 -cwnd 30 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 768 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast1024/incast_rand_flowsize_incastratio_3_seed700.cm -q 40 -cwnd 30 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 768 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast1024/incast_rand_flowsize_incastratio_3_seed700.cm -q 50 -cwnd 30 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 768 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast1024/incast_rand_flowsize_incastratio_3_seed700.cm -q 60 -cwnd 30 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 768 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast1024/incast_rand_flowsize_incastratio_3_seed700.cm -q 30 -cwnd 40 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 768 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast1024/incast_rand_flowsize_incastratio_3_seed700.cm -q 40 -cwnd 40 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 768 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast1024/incast_rand_flowsize_incastratio_3_seed700.cm -q 50 -cwnd 40 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 768 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast1024/incast_rand_flowsize_incastratio_3_seed700.cm -q 60 -cwnd 40 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 768 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast1024/incast_rand_flowsize_incastratio_3_seed700.cm -q 30 -cwnd 50 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 768 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast1024/incast_rand_flowsize_incastratio_3_seed700.cm -q 40 -cwnd 50 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 768 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast1024/incast_rand_flowsize_incastratio_3_seed700.cm -q 50 -cwnd 50 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 768 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast1024/incast_rand_flowsize_incastratio_3_seed700.cm -q 60 -cwnd 50 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 768 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast1024/incast_rand_flowsize_incastratio_3_seed700.cm -q 30 -cwnd 60 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 768 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast1024/incast_rand_flowsize_incastratio_3_seed700.cm -q 40 -cwnd 60 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 768 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast1024/incast_rand_flowsize_incastratio_3_seed700.cm -q 50 -cwnd 60 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
ndp full command is : ./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 768 -strat ecmp_ar  -end 50000000 -mtu 3000 -tm connection_matrices/incast1024/incast_rand_flowsize_incastratio_3_seed700.cm -q 60 -cwnd 60 -ar_sticky_delta 13 -pfc_thresholds 9 10 -spine_pfc_thresholds 9 10
For q = 30 ar_sticky_delta = 13 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 30. 768 flows completed. Number of packets 512256 ,Number of RTX 4510 , JCT is: 578.0us, fastest flow: 397.0us 
For q = 40 ar_sticky_delta = 13 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 30. 768 flows completed. Number of packets 512256 ,Number of RTX 2090 , JCT is: 574.0us, fastest flow: 397.0us 
For q = 50 ar_sticky_delta = 13 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 30. 768 flows completed. Number of packets 512256 ,Number of RTX 730 , JCT is: 630.0us, fastest flow: 376.0us 
For q = 60 ar_sticky_delta = 13 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 30. 768 flows completed. Number of packets 512256 ,Number of RTX 14 , JCT is: 628.0us, fastest flow: 330.0us 
For q = 30 ar_sticky_delta = 13 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 40. 768 flows completed. Number of packets 512256 ,Number of RTX 9330 , JCT is: 527.0us, fastest flow: 469.0us 
For q = 40 ar_sticky_delta = 13 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 40. 768 flows completed. Number of packets 512256 ,Number of RTX 6731 , JCT is: 525.0us, fastest flow: 462.0us 
For q = 50 ar_sticky_delta = 13 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 40. 768 flows completed. Number of packets 512256 ,Number of RTX 4812 , JCT is: 544.0us, fastest flow: 460.0us 
For q = 60 ar_sticky_delta = 13 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 40. 768 flows completed. Number of packets 512256 ,Number of RTX 2371 , JCT is: 538.0us, fastest flow: 452.0us 
For q = 30 ar_sticky_delta = 13 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 50. 768 flows completed. Number of packets 512256 ,Number of RTX 15006 , JCT is: 556.0us, fastest flow: 459.0us 
For q = 40 ar_sticky_delta = 13 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 50. 768 flows completed. Number of packets 512256 ,Number of RTX 12329 , JCT is: 553.0us, fastest flow: 458.0us 
For q = 50 ar_sticky_delta = 13 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 50. 768 flows completed. Number of packets 512256 ,Number of RTX 10642 , JCT is: 575.0us, fastest flow: 461.0us 
For q = 60 ar_sticky_delta = 13 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 50. 768 flows completed. Number of packets 512256 ,Number of RTX 7831 , JCT is: 570.0us, fastest flow: 460.0us 
For q = 30 ar_sticky_delta = 13 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 60. 768 flows completed. Number of packets 512256 ,Number of RTX 19686 , JCT is: 523.0us, fastest flow: 459.0us 
For q = 40 ar_sticky_delta = 13 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 60. 768 flows completed. Number of packets 512256 ,Number of RTX 16970 , JCT is: 520.0us, fastest flow: 455.0us 
For q = 50 ar_sticky_delta = 13 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 60. 768 flows completed. Number of packets 512256 ,Number of RTX 14899 , JCT is: 519.0us, fastest flow: 461.0us 
For q = 60 ar_sticky_delta = 13 pfc_thresholds 9 10 spine_pfc_thresholds 9 and cwnd 60. 768 flows completed. Number of packets 512256 ,Number of RTX 12282 , JCT is: 517.0us, fastest flow: 461.0us 

The shortest jct : 517.0us, with qsize = 60, cwnd = 60, ar_sticky_delta = 13 ,pcf_thresholds = 9 10 ,spine_pfc_threshold = 9 10
The slowest jct : 630.0us, with qsize = 50, cwnd = 30 , ar_sticky_delta = 13,pcf_thresholds = 9 10 ,spine_pfc_threshold = 9 10

------------------------------------ Thw above results are for the following base command ----------------------
./htsim_ndp -topo topologies/fat_tree_1024.topo -nodes 1024 -conns 768 -strat ecmp_ar  -end 50000000 -mtu 3000
----------------------------------------------------------------------------------------------------------------
