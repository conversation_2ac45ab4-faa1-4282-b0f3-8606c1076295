import re
import sys

def summarize_log(file_path):
    # Regex to match interval lines
    interval_re = re.compile(r'^Time Interval:\s*([\d.]+)-([\d.]+)')
    # Regex to match flow lines with bytes
    flow_re     = re.compile(r'^Flow:.*?Incremental Bytes Sent:\s*(\d+)')

    start_end = None
    flow_count = 0
    bytes_sum = 0

    with open(file_path, 'r') as f:
        for line in f:
            stripped = line.strip()

            # Check for a new time interval
            m_int = interval_re.match(stripped)
            if m_int:
                # Print summary for the previous interval
                if start_end is not None:
                    print(f"Time Interval: {start_end} {flow_count} {bytes_sum}")
                # Reset counters for the new interval
                start, end = m_int.groups()
                start_end = f"{start}-{end}"
                flow_count = 0
                bytes_sum = 0
                continue

            # Check for a flow line with bytes
            m_flow = flow_re.match(stripped)
            if m_flow:
                flow_count += 1
                bytes_sum += int(m_flow.group(1))

    # Print the final interval summary
    if start_end is not None:
        print(f"Time Interval: {start_end} {flow_count} {bytes_sum}")

if __name__ == '__main__':
    if len(sys.argv) != 2:
        print(f"Usage: python {sys.argv[0]} path/to/logfile")
        sys.exit(1)
    summarize_log(sys.argv[1])
